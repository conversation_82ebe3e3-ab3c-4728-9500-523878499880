<?php

class SeismicCalculator {
    private $conn;

    public function __construct() {
        $this->conn = getConnection();
    }

    /**
     * Scrive un messaggio nel file di log
     * @param mixed $message Il messaggio da loggare
     * @param string $type Il tipo di log (default: INFO)
     */
    public function writeLog($message, $type = 'INFO') {
        $timestamp = date('Y-m-d H:i:s');
        
        // Se il messaggio è un array o un oggetto, convertilo in JSON formattato
        if (is_array($message) || is_object($message)) {
            $type = 'DATA';  // Cambia il tipo per i dati strutturati
            $logMessage = "[$timestamp] [$type]\n" . json_encode($message, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n";
        } else {
            $message = (string)$message;  // Converti in stringa qualsiasi altro tipo
            $logMessage = "[$timestamp] [$type] $message\n";
        }
        
        $logFile = dirname(__DIR__) . '/logs/seismic_calc.log';
        
        // Assicurati che il messaggio sia in UTF-8
        if (!mb_check_encoding($logMessage, 'UTF-8')) {
            $logMessage = mb_convert_encoding($logMessage, 'UTF-8', mb_detect_encoding($logMessage));
        }
        
        // Aggiungi BOM UTF-8 se il file non esiste
        if (!file_exists($logFile)) {
            file_put_contents($logFile, "\xEF\xBB\xBF"); // BOM UTF-8
        }
        
        return file_put_contents($logFile, $logMessage, FILE_APPEND);
    }

    // Stati limite e relative probabilità di superamento
    private $states = [
        'SLO' => ['PVR' => 0.81, 'description' => 'Operatività'],
        'SLD' => ['PVR' => 0.63, 'description' => 'Danno'],
        'SLV' => ['PVR' => 0.10, 'description' => 'Salvaguardia Vita'],
        'SLC' => ['PVR' => 0.05, 'description' => 'Collasso']
    ];

    // Coefficienti per le categorie di sottosuolo
    private $soilCategories = [
        'A' => [
            'SS' => 1.00,
            'CC' => ['coeff' => 1.00, 'exp' => 0.00]  // CC = 1.00
        ],
        'B' => [
            'SS' => ['min' => 1.00, 'max' => 1.20],
            'CC' => ['coeff' => 1.10, 'exp' => -0.20]  // CC = 1.10 * (TC*)-0.20
        ],
        'C' => [
            'SS' => ['min' => 1.00, 'max' => 1.50],
            'CC' => ['coeff' => 1.05, 'exp' => -0.33]  // CC = 1.05 * (TC*)-0.33
        ],
        'D' => [
            'SS' => ['min' => 0.90, 'max' => 1.80],
            'CC' => ['coeff' => 1.25, 'exp' => -0.50]  // CC = 1.25 * (TC*)-0.50
        ],
        'E' => [
            'SS' => ['min' => 1.00, 'max' => 1.60],
            'CC' => ['coeff' => 1.15, 'exp' => -0.40]  // CC = 1.15 * (TC*)-0.40
        ]
    ];

    // Coefficienti topografici
    private $topographicCategories = [
        'T1' => 1.0,
        'T2' => 1.2,
        'T3' => 1.2,
        'T4' => 1.4
    ];

    /**
     * Restituisce gli stati limite
     */
    public function getStates() {
        return $this->states;
    }

    /**
     * Interpola i parametri di base per un punto
     */
    public function interpolateBaseParams($lat, $lng) {
        // Log dei parametri di input
        $this->writeLog("Inizio interpolazione con lat=$lat, lng=$lng", "INFO");
        
        // Recupera i 4 punti più vicini dalla griglia
        $stmt = $this->conn->prepare("
            SELECT 
                latitude, longitude, 
                ag_30/9.81 as ag,      -- Converti ag da m/s² a g
                fo_30 as F0,
                tc_30 as tc,           -- Usa tc invece di TC_star per coerenza
                CASE 
                    WHEN latitude <= ? AND longitude <= ? THEN 1  -- SW
                    WHEN latitude <= ? AND longitude > ? THEN 2   -- SE
                    WHEN latitude > ? AND longitude <= ? THEN 3   -- NW
                    WHEN latitude > ? AND longitude > ? THEN 4    -- NE
                END as corner
            FROM seismic_grid_points
            WHERE latitude BETWEEN ? - 0.5 AND ? + 0.5 
            AND longitude BETWEEN ? - 0.5 AND ? + 0.5
            ORDER BY POW(latitude - ?, 2) + POW(longitude - ?, 2)
            LIMIT 4
        ");
        
        $stmt->execute([
            $lat, $lng,
            $lat, $lng,
            $lat, $lng,
            $lat, $lng,
            $lat, $lat,
            $lng, $lng,
            $lat, $lng
        ]);
        
        $points = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Log del numero di punti trovati
        $this->writeLog("Trovati " . count($points) . " punti nella griglia", "INFO");
        
        if (count($points) < 4) {
            throw new Exception('Non ci sono abbastanza punti nella griglia per interpolare');
        }
        
        // Ordina i punti per corner
        usort($points, function($a, $b) {
            return $a['corner'] - $b['corner'];
        });
        
        // Log dei punti trovati
        $this->writeLog("Punti per interpolazione:", "INFO");
        foreach ($points as $i => $point) {
            $this->writeLog("Punto " . ($i+1) . ": lat={$point['latitude']}, lng={$point['longitude']}, ag={$point['ag']}, F0={$point['F0']}, tc={$point['tc']}", "INFO");
        }
        
        // Calcola i coefficienti di interpolazione
        $x = ($lng - $points[0]['longitude']) / ($points[1]['longitude'] - $points[0]['longitude']);
        $y = ($lat - $points[0]['latitude']) / ($points[2]['latitude'] - $points[0]['latitude']);
        
        // Limita i coefficienti tra 0 e 1
        $x = max(0, min(1, $x));
        $y = max(0, min(1, $y));
        
        $this->writeLog("Coefficienti di interpolazione: x=$x, y=$y", "INFO");
        
        // Interpola i valori
        $result = [];
        foreach (['ag', 'F0', 'tc'] as $param) {  // Usa tc invece di TC_star
            $result[$param] = $this->interpolate(
                $x, $y,
                floatval($points[0][$param]),
                floatval($points[1][$param]),
                floatval($points[2][$param]),
                floatval($points[3][$param])
            );
            $this->writeLog("Parametro $param interpolato: " . $result[$param], "INFO");
        }
        
        // Rinomina tc in TC* per la coerenza con il resto del codice
        $result['TC*'] = $result['tc'];
        unset($result['tc']);
        
        return $result;
    }

    /**
     * Interpola un valore usando i 4 punti più vicini
     */
    private function interpolate($x, $y, $p1, $p2, $p3, $p4) {
        // Formula di interpolazione bilineare
        return $p1 * (1-$x) * (1-$y) +  // SW
               $p2 * $x * (1-$y) +      // SE
               $p3 * (1-$x) * $y +      // NW
               $p4 * $x * $y;           // NE
    }

    /**
     * Interpola i parametri per un dato periodo di ritorno
     */
    public function interpolateForTR($lat, $lng, $TR) {
        // Array dei periodi di ritorno disponibili
        $TR_values = [30, 50, 72, 101, 140, 201, 475, 975, 2475];
        
        // Trova i due periodi di ritorno più vicini
        $lower_TR = null;
        $upper_TR = null;
        
        for ($i = 0; $i < count($TR_values); $i++) {
            if ($TR_values[$i] > $TR) {
                if ($i > 0) {
                    $lower_TR = $TR_values[$i-1];
                    $upper_TR = $TR_values[$i];
                } else {
                    $lower_TR = $TR_values[0];
                    $upper_TR = $TR_values[1];
                }
                break;
            }
        }
        
        if ($lower_TR === null) {
            $lower_TR = $TR_values[count($TR_values)-2];
            $upper_TR = $TR_values[count($TR_values)-1];
        }
        
        $this->writeLog("Periodi di ritorno selezionati: lower=$lower_TR, upper=$upper_TR", "INFO");
        
        // Recupera i parametri per entrambi i periodi
        $stmt = $this->conn->prepare("
            SELECT 
                ag_{$lower_TR}/9.81 as ag_lower,  -- Converti ag da m/s² a g
                fo_{$lower_TR} as fo_lower, 
                tc_{$lower_TR} as tc_lower,
                ag_{$upper_TR}/9.81 as ag_upper,  -- Converti ag da m/s² a g
                fo_{$upper_TR} as fo_upper, 
                tc_{$upper_TR} as tc_upper,
                latitude, longitude
            FROM seismic_grid_points
            WHERE latitude BETWEEN ? - 0.5 AND ? + 0.5 
            AND longitude BETWEEN ? - 0.5 AND ? + 0.5 
            ORDER BY POW(latitude - ?, 2) + POW(longitude - ?, 2)
            LIMIT 4
        ");
        
        $stmt->execute([$lat, $lat, $lng, $lng, $lat, $lng]);
        $points = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $this->writeLog("Punti trovati per interpolazione TR", $points);
        
        if (count($points) < 4) {
            throw new Exception('Punti insufficienti per l\'interpolazione. Trovati: ' . count($points));
        }
        
        // Ordina i punti per formare un quadrilatero
        usort($points, function($a, $b) {
            if ($a['latitude'] != $b['latitude']) {
                return $a['latitude'] < $b['latitude'] ? -1 : 1;
            }
            return $a['longitude'] < $b['longitude'] ? -1 : 1;
        });
        
        // Interpola spazialmente per ogni periodo
        $lower_params = $this->interpolateParams($points, 'lower', $lat, $lng);
        $upper_params = $this->interpolateParams($points, 'upper', $lat, $lng);
        
        // Interpola tra i due periodi di ritorno
        $x = (log($TR) - log($lower_TR)) / (log($upper_TR) - log($lower_TR));
        
        $result = [
            'ag' => $lower_params['ag'] + $x * ($upper_params['ag'] - $lower_params['ag']),  // ag è già in g
            'F0' => $lower_params['fo'] + $x * ($upper_params['fo'] - $lower_params['fo']),
            'TC*' => $lower_params['tc'] + $x * ($upper_params['tc'] - $lower_params['tc'])
        ];
        
        $this->writeLog("Risultato interpolazione TR", $result);
        
        return $result;
    }
    
    /**
     * Interpola i parametri spazialmente
     */
    private function interpolateParams($points, $suffix, $lat, $lng) {
        // Normalizza le coordinate
        $x = ($lng - $points[0]['longitude']) / ($points[1]['longitude'] - $points[0]['longitude']);
        $y = ($lat - $points[0]['latitude']) / ($points[2]['latitude'] - $points[0]['latitude']);
        
        // Interpola ogni parametro
        $params = [];
        foreach (['ag', 'fo', 'tc'] as $param) {
            $field = $param . '_' . $suffix;
            $params[$param] = $this->interpolate(
                $x, $y,
                $points[0][$field],
                $points[1][$field],
                $points[2][$field],
                $points[3][$field]
            );
        }
        
        return $params;
    }

    /**
     * Calcola il periodo di ritorno
     */
    public function calculateTR($VN, $buildingClass, $PVR) {
        // Mappa delle classi d'uso
        $CU = [
            'I' => 0.7,
            'II' => 1.0,
            'III' => 1.5,
            'IV' => 2.0
        ];
        
        if (!isset($CU[$buildingClass])) {
            throw new Exception("Classe d'uso non valida: $buildingClass");
        }
        
        $VR = $VN * $CU[$buildingClass];
        return -$VR / log(1 - $PVR);
    }

    /**
     * Calcola i coefficienti di amplificazione
     */
    public function calculateAmplificationCoefficients($soilCategory, $topographicCategory, $ag, $F0, $TC_star) {
        $this->writeLog("CALCOLO COEFFICIENTI DI AMPLIFICAZIONE", "INFO");
        $this->writeLog("Input: soilCategory=$soilCategory, TC*=$TC_star s", "INFO");
        
        if (!isset($this->soilCategories[$soilCategory])) {
            throw new Exception('Categoria di sottosuolo non valida');
        }

        $soil = $this->soilCategories[$soilCategory];
        
        // Calcola CC con log dettagliato
        $coeff = $soil['CC']['coeff'];
        $exp = $soil['CC']['exp'];
        
        // Verifica che TC* sia positivo
        if ($TC_star <= 0) {
            $this->writeLog("ATTENZIONE: TC* <= 0, imposto valore minimo", "WARNING");
            $TC_star = 0.01;
        }
        
        $this->writeLog("Formula CC = $coeff * (TC*)^($exp)", "INFO");
        $this->writeLog("Valori: coeff=$coeff, TC*=$TC_star, exp=$exp", "INFO");
        
        // Calcola CC con maggiore precisione
        $CC = $coeff * pow($TC_star, $exp);
        $CC = round($CC, 5); // Arrotonda a 5 decimali per maggiore precisione
        
        $this->writeLog("Risultato: CC = $CC", "INFO");

        // Resto del codice...
        $SS = is_numeric($soil['SS']) ? $soil['SS'] : min(max($soil['SS']['min'], 
            $this->calculateSS($soilCategory, $F0, $ag)), $soil['SS']['max']);
        
        $ST = $this->topographicCategories[$topographicCategory];
        $S = $SS * $ST;

        $result = [
            'SS' => round($SS, 3),
            'CC' => round($CC, 3),
            'ST' => round($ST, 3),
            'S' => round($S, 3)
        ];

        return $result;
    }

    /**
     * Calcola i periodi caratteristici
     */
    public function calculatePeriods($TC_star, $CC, $ag) {
        $this->writeLog("CALCOLO PERIODI CARATTERISTICI", "INFO");
        $this->writeLog("Input: TC*=$TC_star s, CC=$CC", "INFO");
        
        // TC = CC * TC* (in secondi)
        $TC = $CC * $TC_star;
        $this->writeLog("Formula: TC = CC * TC*", "INFO");
        $this->writeLog("Calcolo: TC = $CC * $TC_star = $TC s", "INFO");
        
        // Verifica che TC non sia negativo o nullo
        if ($TC <= 0) {
            $this->writeLog("ATTENZIONE: TC calcolato <= 0, imposto valore minimo", "WARNING");
            $TC = 0.001; // valore minimo in secondi
        }
        
        // TB = TC/3 (in secondi)
        $TB = $TC / 3;
        $this->writeLog("Formula: TB = TC/3", "INFO");
        $this->writeLog("Calcolo: TB = $TC/3 = $TB s", "INFO");
        
        // Verifica che TB non sia negativo o nullo
        if ($TB <= 0) {
            $this->writeLog("ATTENZIONE: TB calcolato <= 0, imposto valore minimo", "WARNING");
            $TB = 0.001; // valore minimo in secondi
        }
        
        // TD = 4.0 * ag + 1.6 (in secondi, ag è già in g)
        $TD = 4.0 * $ag + 1.6;
        $this->writeLog("Formula: TD = 4.0 * ag + 1.6", "INFO");
        $this->writeLog("Calcolo: TD = 4.0 * $ag + 1.6 = $TD s", "INFO");

        // Arrotonda a 3 decimali
        $result = [
            'TB' => round($TB, 3),
            'TC' => round($TC, 3),
            'TD' => round($TD, 3)
        ];
        
        $this->writeLog("Periodi calcolati (in secondi):", "INFO");
        $this->writeLog($result, "INFO");
        
        return $result;
    }

    /**
     * Calcola SS in base alla categoria di sottosuolo
     */
    private function calculateSS($category, $F0, $ag) {
        switch ($category) {
            case 'A':
                return 1.0;
            case 'B':
                return 1.40 - 0.40 * $F0 * $ag;
            case 'C':
                return 1.70 - 0.60 * $F0 * $ag;
            case 'D':
                return 2.40 - 1.50 * $F0 * $ag;
            case 'E':
                return 2.00 - 1.10 * $F0 * $ag;
            default:
                throw new Exception("Categoria di sottosuolo non valida: $category");
        }
    }

    /**
     * Calcola il fattore di smorzamento
     */
    public function calculateDampingFactor($xi) {
        return max(sqrt(10 / (5 + $xi)), 0.55);
    }

    /**
     * Genera i punti dello spettro
     */
    public function generateSpectrumPoints($params) {
        $points = [];
        $T = 0.01; // Partiamo da un valore piccolo ma non zero
        $step = 0.01;
        $maxT = 4.0;

        // Aggiungiamo il punto T=0 manualmente
        $Se0 = $this->calculateSpectralAcceleration($params, 0.001); // Usiamo un valore molto piccolo per T=0
        $points[] = ['T' => 0, 'Se' => $Se0];

        while ($T <= $maxT) {
            $Se = $this->calculateSpectralAcceleration($params, $T);
            $points[] = ['T' => $T, 'Se' => $Se];
            $T += $step;
        }

        return $points;
    }

    /**
     * Calcola l'accelerazione spettrale
     */
    private function calculateSpectralAcceleration($params, $T) {
        // Verifica che tutti i parametri necessari siano presenti
        $required = ['ag', 'S', 'eta', 'F0', 'TB', 'TC', 'TD'];
        foreach ($required as $param) {
            if (!isset($params[$param])) {
                $this->writeLog("Parametro mancante: $param", "ERROR");
                throw new Exception("Parametro mancante: $param");
            }
        }

        $ag = $params['ag'];
        $S = $params['S'];
        $eta = $params['eta'];
        $F0 = $params['F0'];
        $TB = $params['TB'];
        $TC = $params['TC'];
        $TD = $params['TD'];

        // Protezione contro valori negativi o zero
        $T = max($T, 0.001);
        $TB = max($TB, 0.001);

        $this->writeLog("Calcolo Se(T) per T=$T", "INFO");
        $this->writeLog("Parametri: ag=$ag, S=$S, eta=$eta, F0=$F0, TB=$TB, TC=$TC, TD=$TD", "INFO");

        $Se = 0;
        if ($T >= 0 && $T < $TB) {
            $Se = $ag * $S * $eta * $F0 * ($T/$TB + (1/($eta*$F0)) * (1 - $T/$TB));
            $this->writeLog("Formula: Se(T) = ag * S * eta * F0 * (T/TB + (1/(eta*F0)) * (1 - T/TB))", "INFO");
        }
        elseif ($T >= $TB && $T < $TC) {
            $Se = $ag * $S * $eta * $F0;
            $this->writeLog("Formula: Se(T) = ag * S * eta * F0", "INFO");
        }
        elseif ($T >= $TC && $T < $TD) {
            $Se = $ag * $S * $eta * $F0 * ($TC/$T);
            $this->writeLog("Formula: Se(T) = ag * S * eta * F0 * (TC/T)", "INFO");
        }
        else { // T >= TD
            $Se = $ag * $S * $eta * $F0 * ($TC*$TD/($T*$T));
            $this->writeLog("Formula: Se(T) = ag * S * eta * F0 * (TC*TD/T^2)", "INFO");
        }

        $this->writeLog("Se(T) = $Se", "INFO");
        return $Se;
    }

    /**
     * Calcola TC* secondo la procedura NTC 2008
     * @param float $ag Accelerazione di picco al sito in g
     * @param string $soilCategory Categoria di sottosuolo
     * @return float TC* in secondi
     */
    public function calculateTcStar($ag, $soilCategory) {
        $this->writeLog("CALCOLO TC* SECONDO NTC 2008", "INFO");
        $this->writeLog("Input: ag=$ag g, soilCategory=$soilCategory", "INFO");

        // 1. Identifica la categoria del sottosuolo
        if (!isset($this->soilCategories[$soilCategory])) {
            throw new Exception("Categoria di sottosuolo non valida: $soilCategory");
        }
        $this->writeLog("1. Categoria sottosuolo identificata: $soilCategory", "INFO");

        // 2. Determina la pericolosità sismica del sito
        // ag è già fornito come input

        // 3. Ottieni i valori tabulati dall'Allegato B
        $stmt = $this->conn->prepare("
            SELECT DISTINCT ag/9.81 as ag_val, tc as tc_val
            FROM seismic_grid_points 
            ORDER BY ABS(ag/9.81 - ?)
            LIMIT 2
        ");
        $stmt->execute([$ag]);
        $points = $stmt->fetchAll(PDO::FETCH_ASSOC);

        if (count($points) < 2) {
            throw new Exception("Impossibile trovare valori tabulati sufficienti per ag=$ag");
        }

        // Ordina i punti per ag crescente
        usort($points, function($a, $b) {
            return $a['ag_val'] <=> $b['ag_val'];
        });

        // 4. Interpolazione lineare
        $ag1 = $points[0]['ag_val'];
        $ag2 = $points[1]['ag_val'];
        $Tc1 = $points[0]['tc_val'];
        $Tc2 = $points[1]['tc_val'];

        $this->writeLog("Valori tabulati per interpolazione:", "INFO");
        $this->writeLog("ag1=$ag1 g → Tc1=$Tc1 s", "INFO");
        $this->writeLog("ag2=$ag2 g → Tc2=$Tc2 s", "INFO");

        // Formula: Tc = Tc1 + ((Tc2 - Tc1)/(ag2 - ag1)) * (ag - ag1)
        $Tc = $Tc1 + (($Tc2 - $Tc1)/($ag2 - $ag1)) * ($ag - $ag1);
        
        $this->writeLog("Formula: Tc = Tc1 + ((Tc2 - Tc1)/(ag2 - ag1)) * (ag - ag1)", "INFO");
        $this->writeLog("Calcolo: Tc = $Tc1 + (($Tc2 - $Tc1)/($ag2 - $ag1)) * ($ag - $ag1) = $Tc", "INFO");

        // 5. Verifica risultato
        if ($Tc <= 0) {
            $this->writeLog("ATTENZIONE: TC* calcolato <= 0, imposto valore minimo", "WARNING");
            $Tc = 0.01;
        }

        $Tc = round($Tc, 3); // Arrotonda a 3 decimali
        $this->writeLog("TC* finale = $Tc s", "INFO");

        return $Tc;
    }
} 