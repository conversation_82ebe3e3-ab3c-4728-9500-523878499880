<?php
require_once '../../includes/db_config.php';
session_start();

// Verifica accesso admin
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Accesso non autorizzato']);
    exit;
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $data = json_decode(file_get_contents('php://input'), true);
        
        // Validazione dei dati
        $requiredFields = ['site_name', 'smtp_host', 'smtp_port', 'backup_retention', 'log_level'];
        foreach ($requiredFields as $field) {
            if (!isset($data[$field]) || empty($data[$field])) {
                throw new Exception("Il campo $field è obbligatorio");
            }
        }

        // Validazione specifica per alcuni campi
        if (!filter_var($data['smtp_port'], FILTER_VALIDATE_INT, ["options" => ["min_range" => 1, "max_range" => 65535]])) {
            throw new Exception('La porta SMTP deve essere un numero tra 1 e 65535');
        }

        if (!filter_var($data['backup_retention'], FILTER_VALIDATE_INT, ["options" => ["min_range" => 1]])) {
            throw new Exception('I giorni di conservazione backup devono essere maggiori di 0');
        }

        if (!in_array($data['log_level'], ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL'])) {
            throw new Exception('Livello log non valido');
        }

        $conn = getConnection();
        
        // Inizia la transazione
        $conn->beginTransaction();

        try {
            // Elimina le vecchie impostazioni
            $conn->exec("DELETE FROM settings");

            // Prepara lo statement per l'inserimento
            $stmt = $conn->prepare("INSERT INTO settings (setting_key, setting_value) VALUES (?, ?)");

            // Inserisce le nuove impostazioni
            foreach ($data as $key => $value) {
                $stmt->execute([$key, $value]);
            }

            // Commit della transazione
            $conn->commit();

            // Log dell'operazione
            $adminUsername = $_SESSION['username'];
            error_log("Impostazioni aggiornate da $adminUsername");

            echo json_encode([
                'success' => true, 
                'message' => 'Impostazioni salvate con successo'
            ]);
        } catch (Exception $e) {
            // Rollback in caso di errore
            $conn->rollBack();
            throw $e;
        }
    } catch (Exception $e) {
        http_response_code(400);
        echo json_encode([
            'success' => false, 
            'message' => $e->getMessage()
        ]);
    }
} else {
    http_response_code(405);
    echo json_encode([
        'success' => false, 
        'message' => 'Metodo non consentito'
    ]);
} 