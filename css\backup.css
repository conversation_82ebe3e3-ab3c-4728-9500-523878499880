@import 'variables.css';

body {
    font-family: 'Segoe UI', Aria<PERSON>, sans-serif;
    background-color: var(--background-dark);
    color: var(--text-primary);
    line-height: 1.6;
    margin: 0;
    padding: 0;
}

.container {
    max-width: 800px;
    margin: 80px auto 20px;
    padding: var(--spacing-lg);
}

/* Navbar */
.navbar {
    background-color: var(--surface);
    border-bottom: 1px solid var(--border-color);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.navbar-brand {
    color: var(--primary-color) !important;
    font-weight: 500;
}

.nav-link {
    color: var(--text-secondary) !important;
    transition: color var(--transition-speed) var(--transition-timing);
}

.nav-link:hover {
    color: var(--primary-color) !important;
}

/* Card */
.card {
    background-color: var(--surface);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    margin-bottom: var(--spacing-lg);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.card-header {
    background-color: transparent;
    border-bottom: 1px solid var(--border-color);
    color: var(--primary-color);
    font-weight: 500;
    padding: var(--spacing-md) var(--spacing-lg);
}

.card-body {
    padding: var(--spacing-lg);
}

/* Alerts */
.alert {
    background-color: var(--surface);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-md);
}

.alert-success {
    border-color: var(--success-color);
    color: var(--success-color);
}

.alert-danger {
    border-color: var(--danger-color);
    color: var(--danger-color);
}

.alert-warning {
    border-color: var(--warning-color);
    color: var(--warning-color);
}

/* Buttons */
.backup-container {
    background-color: var(--surface);
    border-radius: var(--border-radius);
    padding: var(--spacing-lg);
    margin: var(--spacing-lg);
    border: 1px solid var(--border-color);
    box-shadow: var(--box-shadow);
}

.progress-container {
    background: var(--background-lighter);
    border-radius: var(--border-radius);
    height: 30px;
    margin: var(--spacing-md) 0;
    position: relative;
    overflow: hidden;
}

.progress-bar {
    width: 100%;
    height: 10px;
    background-color: var(--background-lighter);
    border-radius: 5px;
    overflow: hidden;
    position: relative;
}

.progress-fill {
    background: var(--primary-color);
    height: 100%;
    width: 0;
    transition: width var(--transition-speed) var(--transition-timing);
}

.progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        45deg,
        transparent 25%,
        rgba(255, 255, 255, 0.1) 25%,
        rgba(255, 255, 255, 0.1) 50%,
        transparent 50%,
        transparent 75%,
        rgba(255, 255, 255, 0.1) 75%
    );
    background-size: 20px 20px;
    animation: progress-animation 1s linear infinite;
}

@keyframes progress-animation {
    0% {
        background-position: 0 0;
    }
    100% {
        background-position: 20px 0;
    }
}

.progress-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: var(--text-primary);
    font-weight: bold;
}

.status-message {
    margin: var(--spacing-md) 0;
    color: var(--text-primary);
}

.backup-log {
    margin-top: var(--spacing-xl);
    background-color: var(--background-darker);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
    overflow: hidden;
}

.log-header {
    background-color: var(--background-lighter);
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.log-header i {
    color: var(--primary-color);
}

.log-content {
    padding: var(--spacing-md);
    max-height: 200px;
    overflow-y: auto;
    font-family: 'Consolas', monospace;
    color: var(--text-primary);
}

.log-entry {
    padding: var(--spacing-xs) 0;
    color: var(--text-secondary);
    font-family: 'Consolas', monospace;
    font-size: 0.9rem;
    line-height: 1.4;
    padding: var(--spacing-sm);
    border-bottom: 1px solid var(--border-color);
}

.log-entry:last-child {
    border-bottom: none;
}

.log-time {
    color: var(--primary-color);
    margin-right: var(--spacing-sm);
}

.log-info {
    color: var(--text-primary);
}

.log-error {
    color: var(--danger-color);
}

.log-success {
    color: var(--success-color);
}

.card-description {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-md);
}

/* Azioni */
.backup-actions {
    text-align: center;
    margin: var(--spacing-xl) 0;
}

.backup-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.info-card {
    background-color: var(--background-lighter);
    border-radius: var(--border-radius);
    padding: var(--spacing-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    transition: transform var(--transition-speed) var(--transition-timing);
}

.info-card:hover {
    transform: translateY(-2px);
}

.info-icon {
    width: 50px;
    height: 50px;
    background-color: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.info-icon i {
    font-size: 1.5rem;
    color: var(--text-on-primary);
}

.info-text h3 {
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
    font-size: 1.1rem;
}

.info-text p {
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin: 0;
}

.btn {
    padding: var(--spacing-md) var(--spacing-xl);
    border-radius: var(--border-radius);
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-speed) var(--transition-timing);
    border: none;
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: 1rem;
}

.btn-primary {
    background: var(--primary-color);
    color: var(--text-on-primary);
    padding: var(--spacing-md) var(--spacing-lg);
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: all var(--transition-speed) var(--transition-timing);
    font-size: 1rem;
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.btn-primary:hover {
    background: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: var(--box-shadow-hover);
}

.btn-primary:disabled {
    background: var(--gray-700);
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.btn i {
    font-size: 1.2rem;
}

/* Progress */
.progress {
    background-color: var(--background-lighter);
    border-radius: var(--border-radius);
    height: 8px;
    margin: var(--spacing-md) 0;
}

.progress-bar {
    background-color: var(--primary-color);
    border-radius: var(--border-radius);
}

/* Lists */
.list-group {
    border-radius: var(--border-radius);
    overflow: hidden;
}

.list-group-item {
    background-color: var(--surface);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
    padding: var(--spacing-md);
}

.list-group-item:not(:last-child) {
    border-bottom: 1px solid var(--border-color);
}

/* Form Controls */
.form-control {
    background-color: var(--background-lighter);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    color: var(--text-primary);
    padding: var(--spacing-sm) var(--spacing-md);
}

.form-control:focus {
    background-color: var(--background-lighter);
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(255, 140, 0, 0.1);
    color: var(--text-primary);
}

/* Tables */
.table {
    color: var(--text-primary);
}

.table th {
    border-bottom: 2px solid var(--border-color);
    color: var(--text-secondary);
}

.table td {
    border-bottom: 1px solid var(--border-color);
}

/* Responsive */
@media (max-width: 768px) {
    .container {
        margin-top: 60px;
        padding: var(--spacing-md);
    }
    
    .card-body {
        padding: var(--spacing-md);
    }
}

.main-content {
    flex: 1;
    margin-left: 280px;
    padding: var(--spacing-lg);
    min-height: 100vh;
}

.page-header {
    margin-bottom: var(--spacing-xl);
}

.page-header h1 {
    color: var(--text-primary);
    font-size: 2rem;
    margin-bottom: var(--spacing-sm);
}

.page-header p {
    color: var(--text-secondary);
    font-size: 1.1rem;
}
