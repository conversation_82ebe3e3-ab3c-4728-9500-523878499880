<?php
/**
 * llm_service.php - Gestione chiamate LLM per calcolo massa inerziale
 * Path: /inertial_mass/api/llm_service.php
 */

session_start();

// Configurazioni PHP per prevenire timeout
ini_set('max_execution_time', 300); // 5 minuti
ini_set('memory_limit', '256M');
set_time_limit(300);

// Verifica autenticazione
$isTestEnvironment = strpos($_SERVER['SCRIPT_NAME'], 'test_integration.php') !== false;

if (!isset($_SESSION['user_id']) && !$isTestEnvironment) {
    http_response_code(401);
    echo json_encode(['error' => 'Non autorizzato']);
    exit;
}

// Carica configurazione
$config = require_once __DIR__ . '/../includes/config.php';

// Verifica metodo
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Metodo non consentito']);
    exit;
}

// Ottieni dati dalla richiesta
$input = json_decode(file_get_contents('php://input'), true);

if (!$input) {
    http_response_code(400);
    echo json_encode(['error' => 'Dati non validi']);
    exit;
}

// Sistema a tre livelli: Gemma3 → Deepseek → Locale (ORDINE MODIFICATO)
$hasDeepseek = !empty($config['deepseek_api_key']) && $config['deepseek_api_key'] !== '***********************************';
$hasGemini = !empty($config['gemini_api_key']);

error_log("Verifica API disponibili - Gemini: " . ($hasGemini ? 'SI' : 'NO') . ", Deepseek: " . ($hasDeepseek ? 'SI' : 'NO'));

if (!$hasDeepseek && !$hasGemini) {
    error_log("Nessuna API LLM configurata - utilizzo calcolatore locale");
    include __DIR__ . '/local_calculator.php';
    exit;
}

try {
    // Log inizio elaborazione
    error_log("=== INIZIO ELABORAZIONE MASSA INERZIALE ===");
    error_log("Timestamp: " . date('Y-m-d H:i:s'));
    error_log("Session ID: " . session_id());
    error_log("User ID: " . ($_SESSION['user_id'] ?? 'guest'));
    error_log("Memory usage: " . memory_get_usage(true) . " bytes");
    
    // Prepara il prompt per il LLM
    $prompt = preparePrompt($input);
    error_log("Prompt preparato, lunghezza: " . strlen($prompt) . " caratteri");
    
    // Log della richiesta prima della chiamata API
    logAIRequest($input, $prompt, $config);
    
    // Sistema a tre livelli: Gemma3 → Deepseek → Locale (ORDINE MODIFICATO)
    $response = null;
    $usedProvider = null;
    $startTime = microtime(true);

    // Tentativo 1: Gemma3 (primario)
    if ($hasGemini) {
        try {
            error_log("Tentativo 1: Chiamata API Gemma3...");
            $response = callGeminiAPI($prompt, $config);
            $usedProvider = 'gemini';
            error_log("Gemma3 API: Successo");
        } catch (Exception $e) {
            error_log("Gemma3 API fallita: " . $e->getMessage());
        }
    }

    // Tentativo 2: Deepseek (se Gemma3 fallisce e Deepseek disponibile)
    if (!$response && $hasDeepseek) {
        try {
            error_log("Tentativo 2: Chiamata API Deepseek...");
            $response = callDeepseekAPI($prompt, $config);
            $usedProvider = 'deepseek';
            error_log("Deepseek API: Successo");
        } catch (Exception $e) {
            error_log("Deepseek API fallita: " . $e->getMessage());
        }
    }
    
    // Tentativo 3: Calcolo locale (se entrambe le API falliscono)
    if (!$response) {
        error_log("Entrambe le API fallite - Utilizzo calcolatore locale");
        include __DIR__ . '/local_calculator.php';
        exit;
    }
    
    $endTime = microtime(true);
    $apiDuration = round(($endTime - $startTime), 2);
    error_log("Chiamata API completata con $usedProvider in {$apiDuration} secondi");
    
    // Processa la risposta
    error_log("Inizio processamento risposta...");
    $result = processLLMResponse($response, $input);
    $result['provider_used'] = $usedProvider; // Aggiungi info sul provider
    error_log("Risposta processata con successo usando $usedProvider");
    
    // Log della risposta dopo la chiamata API
    logAIResponse($response, $result, $usedProvider);
    
    // Salva in cache se abilitato
    if ($config['enable_cache']) {
        error_log("Salvataggio in cache...");
        saveToCache($input, $result, $config['cache_ttl']);
    }
    
    // Log finale
    error_log("Memory usage finale: " . memory_get_usage(true) . " bytes");
    error_log("Memory peak: " . memory_get_peak_usage(true) . " bytes");
    error_log("=== ELABORAZIONE COMPLETATA CON SUCCESSO ===");
    
    // Restituisci il risultato
    header('Content-Type: application/json');
    echo json_encode($result);
    
} catch (Exception $e) {
    // Log dettagliato dell'errore
    error_log("=== ERRORE DURANTE ELABORAZIONE ===");
    error_log("Errore: " . $e->getMessage());
    error_log("File: " . $e->getFile());
    error_log("Linea: " . $e->getLine());
    error_log("Stack trace: " . $e->getTraceAsString());
    error_log("Memory usage al momento dell'errore: " . memory_get_usage(true) . " bytes");
    error_log("=== FINE LOG ERRORE ===");
    
    http_response_code(500);
    echo json_encode([
        'error' => 'Errore durante il calcolo: ' . $e->getMessage(),
        'timestamp' => date('Y-m-d H:i:s'),
        'memory_usage' => memory_get_usage(true)
    ]);
}

/**
 * Prepara il prompt per il LLM
 * @param array $data Dati dell'edificio
 * @return string Prompt formattato
 */
function preparePrompt($data) {
    $prompt = "Sei un ingegnere strutturale esperto in calcoli sismici secondo NTC 2018. ";
    $prompt .= "Calcola la massa inerziale e le forze sismiche per il seguente edificio:\n\n";
    
    // Dati posizione e sismici
    $prompt .= "DATI SISMICI:\n";
    $prompt .= "- Coordinate: {$data['location']['lat']}, {$data['location']['lon']}\n";
    $prompt .= "- Zona sismica: {$data['seismic_params']['zone']}\n";
    $prompt .= "- ag: {$data['seismic_params']['ag']} g\n";
    $prompt .= "- F0: {$data['seismic_params']['F0']}\n";
    $prompt .= "- TC*: {$data['seismic_params']['TC']} s\n";
    $prompt .= "- Categoria sottosuolo: {$data['seismic_params']['soil_category']}\n";
    $damping = isset($data['seismic_params']['damping']) ? $data['seismic_params']['damping'] : 5.0;
    $prompt .= "- Smorzamento: {$damping}%\n\n";
    
    // Dati struttura
    $prompt .= "DATI STRUTTURA:\n";
    $prompt .= "- Tipologia: {$data['building']['structure_type']}\n";
    $prompt .= "- Solaio: {$data['building']['slab_type']}\n";
    $prompt .= "- Anno costruzione: {$data['building']['construction_year']}\n\n";
    
    // Dati piani
    $prompt .= "PIANI:\n";
    foreach ($data['building']['floors'] as $floor) {
        $prompt .= "- Piano {$floor['level']}: Area {$floor['area']} m², ";
        $prompt .= "Altezza {$floor['height']} m, Uso: {$floor['use']}\n";
    }
    
    $prompt .= "\nCalcola secondo NTC 2018:\n";
    $prompt .= "1. Peso per unità di superficie per ogni piano (kN/m²)\n";
    $prompt .= "2. Massa totale dell'edificio (tonnellate)\n";
    $prompt .= "3. Periodo fondamentale T1 (secondi)\n";
    $prompt .= "4. Fattore di smorzamento eta = sqrt(10/(5+xi)) >= 0.55 dove xi = {$damping}%\n";
    $prompt .= "5. Forza sismica totale alla base (kN) utilizzando lo spettro di risposta con eta calcolato\n";
    $prompt .= "6. Distribuzione delle forze per piano (kN)\n";
    $prompt .= "7. Breve analisi della vulnerabilità sismica\n\n";
    
    $prompt .= "Restituisci SOLO un JSON con questa struttura:\n";
    $prompt .= '```json
{
    "total_mass": numero,
    "period": numero,
    "total_force": numero,
    "floor_forces": [
        {
            "level": numero,
            "mass": numero,
            "height": numero,
            "force": numero
        }
    ],
    "analysis": "testo analisi"
}```';
    
    return $prompt;
}

/**
 * Chiama l'API Deepseek con retry logic
 * @param string $prompt Prompt da inviare
 * @param array $config Configurazione
 * @return array Risposta API
 */
function callDeepseekAPI($prompt, $config) {
    $maxRetries = $config['max_retries'];
    $retryDelay = $config['retry_delay'];
    
    for ($attempt = 1; $attempt <= $maxRetries; $attempt++) {
        try {
            error_log("Deepseek API - Tentativo $attempt di $maxRetries");
            
            $data = [
                'model' => $config['deepseek_model'],
                'messages' => [
                    [
                        'role' => 'system',
                        'content' => 'Sei un assistente esperto in ingegneria strutturale e calcoli sismici.'
                    ],
                    [
                        'role' => 'user',
                        'content' => $prompt
                    ]
                ],
                'temperature' => 0.2,
                'max_tokens' => 2000,
                'stream' => false
            ];
            
            $ch = curl_init($config['deepseek_api_url']);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Content-Type: application/json',
                'Authorization: Bearer ' . $config['deepseek_api_key']
            ]);
            curl_setopt($ch, CURLOPT_TIMEOUT, $config['api_timeout']);
            curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 30);
            
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $curlError = curl_error($ch);
            
            curl_close($ch);
            
            if ($curlError) {
                throw new Exception('Errore cURL: ' . $curlError);
            }
            
            if ($httpCode !== 200) {
                error_log("Deepseek API Error - HTTP Code: $httpCode - Response: " . substr($response, 0, 500));
                
                // Retry per errori temporanei (5xx, timeout)
                if ($httpCode >= 500 || $httpCode === 429) {
                    if ($attempt < $maxRetries) {
                        error_log("Errore temporaneo, retry in {$retryDelay} secondi...");
                        sleep($retryDelay);
                        continue;
                    }
                }
                
                throw new Exception('Errore API Deepseek: HTTP ' . $httpCode);
            }
            
            $result = json_decode($response, true);
            
            if (!$result) {
                throw new Exception('Errore decodifica JSON: ' . json_last_error_msg());
            }
            
            if (!isset($result['choices'][0]['message']['content'])) {
                throw new Exception('Risposta API non valida - struttura mancante');
            }
            
            error_log("Deepseek API - Successo al tentativo $attempt");
            return $result['choices'][0]['message']['content'];
            
        } catch (Exception $e) {
            error_log("Deepseek API - Errore tentativo $attempt: " . $e->getMessage());
            
            if ($attempt === $maxRetries) {
                throw $e; // Ultimo tentativo fallito
            }
            
            // Attendi prima del prossimo tentativo
            sleep($retryDelay);
        }
    }
}

/**
 * Chiama l'API Gemini con retry logic
 * @param string $prompt Prompt da inviare
 * @param array $config Configurazione
 * @return string Risposta API
 */
function callGeminiAPI($prompt, $config) {
    $maxRetries = $config['max_retries'];
    $retryDelay = $config['retry_delay'];
    
    for ($attempt = 1; $attempt <= $maxRetries; $attempt++) {
        try {
            error_log("Gemini API - Tentativo $attempt di $maxRetries");
            
            $data = [
                'contents' => [
                    [
                        'parts' => [
                            [
                                'text' => $prompt
                            ]
                        ]
                    ]
                ],
                'generationConfig' => [
                    'temperature' => 0.2,
                    'maxOutputTokens' => 2000,
                    'topP' => 0.8,
                    'topK' => 10
                ]
            ];
            
            $url = $config['gemini_api_url'] . $config['gemini_model'] . ':generateContent?key=' . $config['gemini_api_key'];
            
            $ch = curl_init($url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Content-Type: application/json'
            ]);
            curl_setopt($ch, CURLOPT_TIMEOUT, $config['api_timeout']);
            curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 30);
            
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $curlError = curl_error($ch);
            
            curl_close($ch);
            
            if ($curlError) {
                throw new Exception('Errore cURL Gemini: ' . $curlError);
            }
            
            if ($httpCode !== 200) {
                error_log("Gemini API Error - HTTP Code: $httpCode - Response: " . substr($response, 0, 500));
                
                // Retry per errori temporanei
                if ($httpCode >= 500 || $httpCode === 429) {
                    if ($attempt < $maxRetries) {
                        error_log("Errore temporaneo Gemini, retry in {$retryDelay} secondi...");
                        sleep($retryDelay);
                        continue;
                    }
                }
                
                throw new Exception('Errore API Gemini: HTTP ' . $httpCode);
            }
            
            $result = json_decode($response, true);
            
            if (!$result) {
                throw new Exception('Errore decodifica JSON Gemini: ' . json_last_error_msg());
            }
            
            if (!isset($result['candidates'][0]['content']['parts'][0]['text'])) {
                throw new Exception('Risposta API Gemini non valida - struttura mancante');
            }
            
            error_log("Gemini API - Successo al tentativo $attempt");
            return $result['candidates'][0]['content']['parts'][0]['text'];
            
        } catch (Exception $e) {
            error_log("Gemini API - Errore tentativo $attempt: " . $e->getMessage());
            
            if ($attempt === $maxRetries) {
                throw $e; // Ultimo tentativo fallito
            }
            
            // Attendi prima del prossimo tentativo
            sleep($retryDelay);
        }
    }
}

/**
 * Processa la risposta del LLM
 * @param string $response Risposta del LLM
 * @param array $originalData Dati originali
 * @return array Risultati processati
 */
function processLLMResponse($response, $originalData) {
    // Estrai il JSON dalla risposta
    preg_match('/```json\s*(.*?)\s*```/s', $response, $matches);
    
    if (!isset($matches[1])) {
        // Prova a decodificare direttamente
        $jsonData = json_decode($response, true);
    } else {
        $jsonData = json_decode($matches[1], true);
    }
    
    if (!$jsonData) {
        throw new Exception('Impossibile decodificare la risposta del LLM');
    }
    
    // Valida i campi richiesti
    $requiredFields = ['total_mass', 'period', 'total_force', 'floor_forces'];
    foreach ($requiredFields as $field) {
        if (!isset($jsonData[$field])) {
            throw new Exception("Campo mancante nella risposta: $field");
        }
    }
    
    // Aggiungi metadati
    $result = [
        'calculation_id' => uniqid('calc_'),
        'timestamp' => date('Y-m-d H:i:s'),
        'input_data' => $originalData,
        'total_mass' => floatval($jsonData['total_mass']),
        'period' => floatval($jsonData['period']),
        'total_force' => floatval($jsonData['total_force']),
        'floor_forces' => array_map(function($floor) {
            return [
                'level' => intval($floor['level']),
                'mass' => floatval($floor['mass']),
                'height' => floatval($floor['height']),
                'force' => floatval($floor['force'])
            ];
        }, $jsonData['floor_forces']),
        'llm_analysis' => $jsonData['analysis'] ?? ''
    ];
    
    return $result;
}

/**
 * Salva in cache
 * @param array $input Input originale
 * @param array $result Risultato
 * @param int $ttl Time to live in secondi
 */
function saveToCache($input, $result, $ttl) {
    // Implementazione semplice con file
    // In produzione usare Redis o simili
    $cacheDir = '../cache';
    if (!is_dir($cacheDir)) {
        mkdir($cacheDir, 0755, true);
    }
    
    $cacheKey = md5(json_encode($input));
    $cacheFile = "$cacheDir/$cacheKey.json";
    
    $cacheData = [
        'expires' => time() + $ttl,
        'data' => $result
    ];
    
    file_put_contents($cacheFile, json_encode($cacheData));
}

/**
 * Log della richiesta AI
 * @param array $input Dati di input
 * @param string $prompt Prompt generato
 * @param array $config Configurazione
 */
function logAIRequest($input, $prompt, $config) {
    $logDir = dirname(__DIR__, 2) . '/logs';
    if (!is_dir($logDir)) {
        mkdir($logDir, 0755, true);
    }
    
    $logFile = $logDir . '/ai.log';
    $timestamp = date('Y-m-d H:i:s');
    $sessionId = session_id();
    $userId = $_SESSION['user_id'] ?? 'guest';
    
    $logEntry = "\n" . str_repeat('=', 80) . "\n";
    $logEntry .= "[{$timestamp}] RICHIESTA AI - Session: {$sessionId} - User: {$userId}\n";
    $logEntry .= str_repeat('=', 80) . "\n";
    
    // Dati di input
    $logEntry .= "\n--- DATI INPUT ---\n";
    $logEntry .= json_encode($input, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n";
    
    // Configurazione API (senza chiave)
    $logEntry .= "\n--- CONFIGURAZIONE API ---\n";
    $safeConfig = $config;
    $safeConfig['deepseek_api_key'] = '[NASCOSTA]';
    $logEntry .= json_encode($safeConfig, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n";
    
    // Prompt generato
    $logEntry .= "\n--- PROMPT GENERATO ---\n";
    $logEntry .= $prompt . "\n";
    
    // Parametri della chiamata API
    $logEntry .= "\n--- PARAMETRI CHIAMATA API ---\n";
    $apiParams = [
        'model' => $config['deepseek_model'],
        'temperature' => 0.2,
        'max_tokens' => 2000,
        'stream' => false
    ];
    $logEntry .= json_encode($apiParams, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n";
    
    file_put_contents($logFile, $logEntry, FILE_APPEND | LOCK_EX);
}

/**
 * Log della risposta AI
 * @param string $response Risposta grezza dell'API
 * @param array $result Risultato processato
 * @param string $provider Provider utilizzato
 */
function logAIResponse($response, $result, $provider = 'unknown') {
    $logDir = dirname(__DIR__, 2) . '/logs';
    $logFile = $logDir . '/ai.log';
    $timestamp = date('Y-m-d H:i:s');
    
    $logEntry = "\n--- RISPOSTA API GREZZA ($provider) ---\n";
    $logEntry .= $response . "\n";
    
    $logEntry .= "\n--- RISULTATO PROCESSATO ---\n";
    $logEntry .= json_encode($result, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n";
    
    $logEntry .= "\n[{$timestamp}] FINE CHIAMATA AI ($provider)\n";
    $logEntry .= str_repeat('=', 80) . "\n";
    
    file_put_contents($logFile, $logEntry, FILE_APPEND | LOCK_EX);
}
