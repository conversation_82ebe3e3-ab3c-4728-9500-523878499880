{"name": "phpunit/php-invoker", "description": "Invoke callables with a timeout", "type": "library", "keywords": ["process"], "homepage": "https://github.com/sebastian<PERSON>mann/php-invoker/", "license": "BSD-3-<PERSON><PERSON>", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "support": {"issues": "https://github.com/sebastian<PERSON>mann/php-invoker/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/php-invoker/security/policy"}, "prefer-stable": true, "config": {"platform": {"php": "8.2.0"}, "optimize-autoloader": true, "sort-packages": true}, "require": {"php": ">=8.2"}, "require-dev": {"ext-pcntl": "*", "phpunit/phpunit": "^11.0"}, "autoload": {"classmap": ["src/"]}, "autoload-dev": {"classmap": ["tests/_fixture/"]}, "suggest": {"ext-pcntl": "*"}, "extra": {"branch-alias": {"dev-main": "5.0-dev"}}}