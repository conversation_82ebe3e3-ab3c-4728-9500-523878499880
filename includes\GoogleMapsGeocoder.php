<?php
class GoogleMapsGeocoder {
    private $lat;
    private $lng;
    private $apiKey = 'AIzaSyBxsF2esyVKfoIfIirlJ2kSSCc--m80Z4k';
    private $context;
    
    public function __construct($lat, $lng) {
        $this->lat = $lat;
        $this->lng = $lng;
        $this->context = stream_context_create([
            'http' => [
                'ignore_errors' => true,
                'timeout' => 30,
                'user_agent' => 'ASDP/1.0'
            ]
        ]);
    }
    
    public function getLocationInfo() {
        $url = sprintf(
            "https://maps.googleapis.com/maps/api/geocode/json?latlng=%f,%f&key=%s&language=it",
            $this->lat,
            $this->lng,
            $this->apiKey
        );
        
        $response = $this->getCachedResponse($url);
        if ($response === false) {
            throw new Exception('Errore nella richiesta di geocoding');
        }
        
        $data = json_decode($response, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new Exception('Errore nel parsing della risposta di geocoding');
        }
        
        if ($data['status'] !== 'OK') {
            throw new Exception('Geocoding fallito: ' . ($data['error_message'] ?? $data['status']));
        }
        
        if (empty($data['results'])) {
            throw new Exception('Nessun risultato trovato per queste coordinate');
        }
        
        return $this->extractAddressInfo($data['results'][0], $data['results']);
    }
    
    private function extractAddressInfo($result, $allResults) {
        $info = [
            'comune' => '',
            'provincia' => '',
            'regione' => ''
        ];
        
        // Prima prova con il risultato principale
        foreach ($result['address_components'] as $component) {
            $this->processAddressComponent($component, $info);
        }
        
        // Se il comune non è stato trovato, cerca negli altri risultati
        if (empty($info['comune'])) {
            foreach ($allResults as $otherResult) {
                foreach ($otherResult['address_components'] as $component) {
                    if (empty($info['comune'])) {
                        $this->processAddressComponent($component, $info);
                    }
                }
            }
        }
        
        return $info;
    }
    
    private function processAddressComponent($component, &$info) {
        if (in_array('locality', $component['types']) || 
            in_array('administrative_area_level_3', $component['types'])) {
            $info['comune'] = $component['long_name'];
        }
        else if (in_array('administrative_area_level_2', $component['types'])) {
            $info['provincia'] = $component['short_name'];
        }
        else if (in_array('administrative_area_level_1', $component['types'])) {
            $info['regione'] = $component['long_name'];
        }
    }
    
    private function getCachedResponse($url) {
        $cacheFile = sys_get_temp_dir() . '/geocode_' . md5($url) . '.json';
        
        // Se esiste una cache valida (meno di 24 ore), usala
        if (file_exists($cacheFile) && (time() - filemtime($cacheFile)) < 86400) {
            return file_get_contents($cacheFile);
        }
        
        $response = file_get_contents($url, false, $this->context);
        if ($response !== false) {
            file_put_contents($cacheFile, $response);
        }
        
        return $response;
    }
} 