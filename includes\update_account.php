<?php
session_start();
require_once 'db_config.php';

header('Content-Type: application/json');

// Verifica se l'utente è loggato
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode([
        'success' => false,
        'message' => 'Utente non autorizzato'
    ]);
    exit();
}

try {
    // Verifica i dati ricevuti
    if (!isset($_POST['nome']) || !isset($_POST['cognome'])) {
        throw new Exception('Dati mancanti');
    }
    
    $pdo = getConnection();
    
    // Aggiorna i dati dell'utente
    $stmt = $pdo->prepare("
        UPDATE users 
        SET nome = ?, cognome = ? 
        WHERE id = ?
    ");
    
    $stmt->execute([
        htmlspecialchars(strip_tags($_POST['nome'])),
        htmlspecialchars(strip_tags($_POST['cognome'])),
        $_SESSION['user_id']
    ]);
    
    if ($stmt->rowCount() > 0) {
        // Aggiorna i dati della sessione
        $_SESSION['nome'] = htmlspecialchars(strip_tags($_POST['nome']));
        $_SESSION['cognome'] = htmlspecialchars(strip_tags($_POST['cognome']));
        
        echo json_encode([
            'success' => true,
            'message' => 'Dati aggiornati con successo'
        ]);
    } else {
        throw new Exception('Nessuna modifica effettuata');
    }
    
} catch (Exception $e) {
    error_log("Errore nell'aggiornamento dei dati dell'account: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Errore nell\'aggiornamento dei dati'
    ]);
}