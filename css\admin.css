/* Admin Panel Styles */
.admin-container {
    display: flex;
    min-height: 100vh;
    background-color: var(--background-primary);
    color: var(--text-primary);
}

.admin-sidebar {
    width: var(--sidebar-width);
    background-color: var(--background-secondary);
    border-right: 1px solid var(--border-color);
    padding: 1rem;
    position: fixed;
    height: 100vh;
    overflow-y: auto;
}

.admin-content {
    flex: 1;
    margin-left: var(--sidebar-width);
    padding: 2rem;
}

.admin-header {
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid var(--primary-color);
}

.admin-card {
    background-color: var(--background-elevated);
    border-radius: var(--border-radius);
    box-shadow: 0 2px 4px var(--shadow-color);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    border: 1px solid var(--border-color);
}

.admin-table {
    width: 100%;
    border-collapse: collapse;
    margin: 1rem 0;
}

.admin-table th,
.admin-table td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

.admin-table th {
    background-color: var(--background-secondary);
    font-weight: 500;
    color: var(--primary-color);
}

.admin-table tr:hover {
    background-color: var(--background-secondary);
}

.admin-btn {
    background-color: var(--primary-color);
    color: var(--text-on-primary);
    border: none;
    border-radius: var(--border-radius);
    padding: 0.75rem 1.5rem;
    cursor: pointer;
    transition: all var(--transition-speed) var(--transition-timing);
}

.admin-btn:hover {
    background-color: var(--primary-dark);
    transform: translateY(-1px);
    box-shadow: 0 2px 4px var(--shadow-color);
}

.admin-btn.secondary {
    background-color: var(--background-secondary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
}

.admin-btn.secondary:hover {
    background-color: var(--background-elevated);
}

.admin-btn.danger {
    background-color: #FF5252;
}

.admin-btn.danger:hover {
    background-color: #FF1744;
}

.admin-form-group {
    margin-bottom: 1.5rem;
}

.admin-form-group label {
    display: block;
    margin-bottom: 0.5rem;
    color: var(--text-primary);
    font-weight: 500;
}

.admin-form-group input,
.admin-form-group select,
.admin-form-group textarea {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    background-color: var(--background-primary);
    color: var(--text-primary);
    transition: all var(--transition-speed) var(--transition-timing);
}

.admin-form-group input:focus,
.admin-form-group select:focus,
.admin-form-group textarea:focus {
    border-color: var(--primary-color);
    outline: none;
    box-shadow: 0 0 0 2px var(--primary-light);
}

.admin-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background-color: var(--background-elevated);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    border: 1px solid var(--border-color);
    text-align: center;
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-color);
    margin: 0.5rem 0;
}

.stat-label {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

@media (max-width: 768px) {
    .admin-sidebar {
        transform: translateX(-100%);
        z-index: 1000;
        transition: transform var(--transition-speed) var(--transition-timing);
    }

    .admin-sidebar.active {
        transform: translateX(0);
    }

    .admin-content {
        margin-left: 0;
    }

    .admin-stats {
        grid-template-columns: 1fr;
    }
}

/* Log Styles */
.log-container {
    display: flex;
    gap: 2rem;
    margin-bottom: 3rem;
    height: calc(100vh - 200px);
    overflow: hidden;
}

.log-section {
    flex: 1;
    background: #1E1E1E;
    border-radius: 8px;
    padding: 1rem;
    display: flex;
    flex-direction: column;
    min-height: 0;
    max-height: 100%;
}

.log-content {
    flex: 1;
    overflow-y: auto;
    min-height: 0;
    padding-right: 1rem;
}

.log-file-content {
    height: auto;
    overflow-y: auto;
    padding: 1rem;
    background: #1E1E1E;
    border-radius: 4px;
    font-family: 'Consolas', monospace;
    white-space: pre-wrap;
    word-wrap: break-word;
    color: #E0E0E0;
    line-height: 1.5;
}

/* Scrollbar Styles */
.log-file-content::-webkit-scrollbar,
.log-content::-webkit-scrollbar {
    width: 8px;
}

.log-file-content::-webkit-scrollbar-track,
.log-content::-webkit-scrollbar-track {
    background: #2D2D2D;
    border-radius: 4px;
}

.log-file-content::-webkit-scrollbar-thumb,
.log-content::-webkit-scrollbar-thumb {
    background: #FF7043;
    border-radius: 4px;
}

.log-file-content::-webkit-scrollbar-thumb:hover,
.log-content::-webkit-scrollbar-thumb:hover {
    background: #FF5722;
}
