<?php
require_once __DIR__ . '/../includes/db_config.php';

$conn = getConnection();

echo "=== VERIFICA TIMEZONE ===\n";
$php_timezone = date_default_timezone_get();
$mysql_timezone = $conn->query("SELECT @@time_zone, @@system_time_zone")->fetch(PDO::FETCH_ASSOC);
echo "PHP Timezone: " . $php_timezone . "\n";
echo "MySQL Time Zone: " . print_r($mysql_timezone, true) . "\n";

echo "\n=== VERIFICA DATE ===\n";
$php_date = date('Y-m-d H:i:s');
$mysql_date = $conn->query("SELECT NOW() as now, CURDATE() as today")->fetch(PDO::FETCH_ASSOC);
echo "PHP Date: " . $php_date . "\n";
echo "MySQL Date: " . print_r($mysql_date, true) . "\n";

echo "\n=== TUTTI GLI ACCESSI DI OGGI (DETTAGLIATI) ===\n";
$stmt = $conn->query("SELECT al.id, al.user_id, al.timestamp, al.action, al.ip_address, 
                             u.username, u.role, u.nome, u.cognome
                      FROM access_logs al 
                      JOIN users u ON al.user_id = u.id 
                      WHERE DATE(al.timestamp) = CURDATE()
                      ORDER BY al.timestamp DESC");
$logs = $stmt->fetchAll(PDO::FETCH_ASSOC);
foreach ($logs as $log) {
    echo "ID: {$log['id']}\n";
    echo "Timestamp: {$log['timestamp']}\n";
    echo "User: {$log['username']} ({$log['role']})\n";
    echo "Nome: {$log['nome']} {$log['cognome']}\n";
    echo "Action: {$log['action']}\n";
    echo "IP: {$log['ip_address']}\n";
    echo "----------------------------------------\n";
}

echo "\n=== CONTEGGIO PER TIPO DI AZIONE ===\n";
$stmt = $conn->query("SELECT action, COUNT(*) as count 
                      FROM access_logs 
                      WHERE DATE(timestamp) = CURDATE()
                      GROUP BY action");
$counts = $stmt->fetchAll(PDO::FETCH_ASSOC);
foreach ($counts as $count) {
    echo "{$count['action']}: {$count['count']}\n";
}

echo "\n=== UTENTI UNICI CON LOGIN EFFETTUATO OGGI ===\n";
$stmt = $conn->query("SELECT DISTINCT u.username, u.role, u.nome, u.cognome,
                             COUNT(al.id) as login_count,
                             MIN(al.timestamp) as first_login,
                             MAX(al.timestamp) as last_login
                      FROM access_logs al
                      JOIN users u ON al.user_id = u.id
                      WHERE DATE(al.timestamp) = CURDATE()
                      AND al.action = 'Login effettuato'
                      GROUP BY u.id, u.username, u.role, u.nome, u.cognome
                      ORDER BY u.role, login_count DESC");
$users = $stmt->fetchAll(PDO::FETCH_ASSOC);
foreach ($users as $user) {
    echo "User: {$user['username']} ({$user['role']})\n";
    echo "Nome: {$user['nome']} {$user['cognome']}\n";
    echo "Login effettuati: {$user['login_count']}\n";
    echo "Primo login: {$user['first_login']}\n";
    echo "Ultimo login: {$user['last_login']}\n";
    echo "----------------------------------------\n";
}

echo "\n=== VERIFICA CONTEGGIO UTENTI ATTIVI ===\n";
$stmt = $conn->query("SELECT COUNT(DISTINCT al.user_id) as active_today 
                      FROM access_logs al 
                      JOIN users u ON al.user_id = u.id 
                      WHERE DATE(al.timestamp) = CURDATE() 
                      AND u.role = 'user'
                      AND al.action = 'Login effettuato'
                      AND al.user_id NOT IN (SELECT id FROM users WHERE role = 'admin')");
$activeToday = $stmt->fetch(PDO::FETCH_ASSOC)['active_today'];
echo "Utenti attivi oggi (query corrente): " . $activeToday . "\n";

// Verifica senza il filtro admin
$stmt = $conn->query("SELECT COUNT(DISTINCT al.user_id) as active_all 
                      FROM access_logs al 
                      JOIN users u ON al.user_id = u.id 
                      WHERE DATE(al.timestamp) = CURDATE() 
                      AND al.action = 'Login effettuato'");
$activeAll = $stmt->fetch(PDO::FETCH_ASSOC)['active_all'];
echo "Utenti attivi oggi (tutti): " . $activeAll . "\n"; 