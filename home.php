<?php
require_once 'includes/db_config.php';
require_once 'includes/ConfigManager.php';
require_once 'includes/VersionManager.php';
session_start();

if (!isset($_SESSION['user_id'])) {
    header('Location: index.php');
    exit();
}

$config = ConfigManager::getInstance();
$versionManager = VersionManager::getInstance();
$googleMapsApiKey = $config->get('GOOGLE_MAPS_API_KEY');

function getInitials($fullName) {
    $parts = explode(' ', trim($fullName));
    $initials = '';
    
    // Prendi la prima lettera del nome
    if (isset($parts[0])) {
        $initials .= mb_substr($parts[0], 0, 1);
    }
    
    // Prendi la prima lettera del cognome
    if (isset($parts[1])) {
        $initials .= mb_substr($parts[1], 0, 1);
    }
    
    return strtoupper($initials);
}

// Recupera nome e cognome dell'utente
try {
    $pdo = getConnection();
    $stmt = $pdo->prepare("SELECT nome, cognome FROM users WHERE id = ?");
    $stmt->execute([$_SESSION['user_id']]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($user) {
        $fullName = $user['nome'] . ' ' . $user['cognome'];
    } else {
        // Se non troviamo l'utente, usiamo lo username come fallback
        $fullName = $_SESSION['username'] ?? 'Utente';
    }
} catch (PDOException $e) {
    error_log("Errore database in home.php: " . $e->getMessage());
    // Usa lo username come fallback in caso di errore
    $fullName = $_SESSION['username'] ?? 'Utente';
}
?>
<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Advanced Seismic Dissipator Project">
    <title>A.S.D.P. - Advanced Seismic Dissipator Project</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="favicon.ico">
    <link rel="shortcut icon" type="image/x-icon" href="favicon.ico">
    
    <!-- CSS -->
    <link rel="stylesheet" href="css/home.css">
    <link rel="stylesheet" href="css/compact-info.css">
    <link rel="stylesheet" href="css/help.css">
    <link rel="stylesheet" href="css/account.css">
    <link rel="stylesheet" href="css/settings.css">
    <link rel="stylesheet" href="css/privacy.css">
    <link rel="stylesheet" href="css/search.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.7.1/dist/leaflet.css"
          integrity="sha512-xodZBNTC5n17Xt2atTPuE1HxjVMSvLVW9ocqUKLsCC5CXdbqCmblAshOMAS6/keqq/sMZMZ19scR4PsZChSR7A=="
          crossorigin=""/>
    
    <!-- Scripts esterni -->
    <script src="https://unpkg.com/@googlemaps/js-api-loader@1.x/dist/index.min.js" defer></script>
    <script src="https://unpkg.com/leaflet@1.7.1/dist/leaflet.js" defer></script>
    <script src="https://unpkg.com/leaflet.gridlayer.googlemutant@latest/dist/Leaflet.GoogleMutant.js" defer></script>
    <!-- Script personalizzati -->
    <script src="js/report.js" defer></script>
    <script src="js/inertial_mass_integration.js" defer></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Arial, sans-serif;
        }

        body {
            background-color: #000000;
            color: #FFFFFF;
            min-height: 100vh;
            margin: 0;
            padding: 0;
            overflow: hidden;
        }

        /* Stile per il popup del gioco */
        .game-popup {
            display: none;
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 9999;
            background: #000;
            padding: 20px;
            border: 2px solid #0f0;
            box-shadow: 0 0 20px rgba(0, 255, 0, 0.5);
        }

        .game-popup.active {
            display: block;
        }

        .game-popup .close-btn {
            position: absolute;
            top: 10px;
            right: 10px;
            color: #0f0;
            cursor: pointer;
            font-size: 24px;
        }
    </style>
    <script>
        // Easter Egg - Space Invaders
        let lastKeys = [];
        const secretCode = ['a', 's', 'd', 'p'];
        
        document.addEventListener('keydown', (e) => {
            lastKeys.push(e.key.toLowerCase());
            if (lastKeys.length > secretCode.length) {
                lastKeys.shift();
            }
            
            if (lastKeys.join('') === secretCode.join('')) {
                showGame();
            }
        });

        function showGame() {
            // Crea il popup se non esiste
            let popup = document.querySelector('.game-popup');
            if (!popup) {
                popup = document.createElement('div');
                popup.className = 'game-popup';
                popup.innerHTML = `
                    <span class="close-btn">&times;</span>
                    <iframe src="spaceinv/game.html" width="840" height="640" frameborder="0"></iframe>
                `;
                document.body.appendChild(popup);

                // Gestisci la chiusura
                popup.querySelector('.close-btn').addEventListener('click', () => {
                    popup.classList.remove('active');
                });
            }

            // Mostra il popup
            popup.classList.add('active');
        }
    </script>
</head>
<body>
    <!-- Top Bar -->
    <div class="top-bar">
        <div class="logo-container">
            <picture>
                <source srcset="img/icons/asdp-logo.svg" type="image/svg+xml">
                <img src="img/icons/asdp-logo.png" alt="ASDP Logo" class="logo">
            </picture>
            <h1 class="app-title">A.S.D.P. - Advanced Seismic Dissipator Project</h1>
        </div>
        <div class="user-info">
            <div class="user-avatar">
                <?php echo getInitials($fullName); ?>
            </div>
            <span class="username"><?php echo htmlspecialchars($fullName); ?></span>
            <div class="toolbar-right">
                <button class="toolbar-button" onclick="openInertialMassModal()" title="Calcolo Massa Inerziale">
                    <i class="fas fa-balance-scale"></i>
                </button>
                <a href="#" onclick="handleLogout(event)" class="logout-btn-top">
                    <i class="fas fa-sign-out-alt"></i>
                    Logout
                </a>
            </div>
        </div>
    </div>

    <!-- Sidebar -->
    <div class="sidebar" id="sidebar">
        <nav class="nav-menu">
            <a href="#dashboard" class="nav-item disabled" data-section="dashboard">
                <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke-width="2">
                    <rect x="3" y="3" width="7" height="7"></rect>
                    <rect x="14" y="3" width="7" height="7"></rect>
                    <rect x="14" y="14" width="7" height="7"></rect>
                    <rect x="3" y="14" width="7" height="7"></rect>
                </svg>
                <span class="nav-tooltip">Funzionalità non ancora implementata - Dashboard</span>
            </a>
            <a href="#valutazione" class="nav-item disabled" data-section="valutazione">
                <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke-width="2">
                    <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5"></path>
                </svg>
                <span class="nav-tooltip">Funzionalità non ancora implementata - Nuova Valutazione</span>
            </a>
            <a href="#storico" class="nav-item disabled" data-section="storico">
                <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke-width="2">
                    <circle cx="12" cy="12" r="10"></circle>
                    <polyline points="12 6 12 12 16 14"></polyline>
                </svg>
                <span class="nav-tooltip">Funzionalità non ancora implementata - Storico Valutazioni</span>
            </a>
            <a href="#progetti" class="nav-item disabled" data-section="progetti">
                <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke-width="2">
                    <path d="M22 19a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h5l2 3h9a2 2 0 0 1 2 2z"></path>
                </svg>
                <span class="nav-tooltip">Funzionalità non ancora implementata - Progetti</span>
            </a>
            <a href="#report" class="nav-item" data-section="report">
                <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke-width="2">
                    <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                    <polyline points="14 2 14 8 20 8"></polyline>
                    <line x1="16" y1="13" x2="8" y2="13"></line>
                    <line x1="16" y1="17" x2="8" y2="17"></line>
                    <polyline points="10 9 9 9 8 9"></polyline>
                </svg>
                <span class="nav-tooltip">Genera Report</span>
            </a>
            <a href="#account" class="nav-item" data-section="account">
                <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke-width="2">
                    <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                    <circle cx="12" cy="7" r="4"></circle>
                </svg>
                <span class="nav-tooltip">Gestione Profilo</span>
            </a>
            <a href="#impostazioni" class="nav-item" data-section="impostazioni">
                <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke-width="2">
                    <circle cx="12" cy="12" r="3"></circle>
                    <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"></path>
                </svg>
                <span class="nav-tooltip">Impostazioni</span>
            </a>
            <a href="#help" class="nav-item" data-section="help">
                <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke-width="2">
                    <circle cx="12" cy="12" r="10"></circle>
                    <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"></path>
                    <line x1="12" y1="17" x2="12.01" y2="17"></line>
                </svg>
                <span class="nav-tooltip">Aiuto</span>
            </a>
        </nav>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <p class="search-label">Cerca un indirizzo o seleziona un punto sulla mappa</p>
        <!-- Search Bar -->
        <div class="search-container">
            <div class="search-box-container">
                <input 
                    type="text" 
                    id="addressSearch" 
                    class="search-box" 
                    placeholder="Inserisci un indirizzo..."
                    autocomplete="off"
                >
                <i class="fas fa-search search-icon"></i>
                <div class="search-loading"></div>
                <div class="search-actions">
                    <button id="geolocateBtn" class="search-btn geolocation" aria-label="Usa posizione attuale">
                        <i class="fas fa-crosshairs"></i>
                    </button>
                    <button id="clearSearch" class="search-btn" aria-label="Cancella ricerca">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
            <div class="search-suggestions"></div>
        </div>

        <!-- Map and Info Panels -->
        <div class="content-grid">
            <div class="map-container">
                <div id="map"></div>
                <div id="leaflet-overlay"></div>
            </div>
            
            <div class="info-panel">
                <h3>Informazioni punto</h3>
                <div class="param-group">
                    <div class="info-row">
                        <label>Indirizzo</label>
                        <input type="text" id="address" readonly>
                    </div>
                    <div class="info-row">
                        <label>Latitudine (ED50)</label>
                        <input type="text" id="latitude" readonly>
                    </div>
                    <div class="info-row">
                        <label>Longitudine (ED50)</label>
                        <input type="text" id="longitude" readonly>
                    </div>
                    <div class="info-row">
                        <label>Altitudine s.l.m.</label>
                        <input type="text" id="altitude" readonly>                  
                    </div>
                </div>
                <div class="cadastral-data">
                    <h3>Dati Catastali</h3>
                    <div id="message-box" class="message info">
                        Clicca sulla mappa per visualizzare i dati catastali
                    </div>
                    <form>
                        <div class="form-group">
                            <label for="catasto_comune">Comune:</label>
                            <input type="text" id="catasto_comune" readonly>
                        </div>
                        <div class="form-group">
                            <label for="catasto_sezione">Sezione:</label>
                            <input type="text" id="catasto_sezione" readonly>
                        </div>
                        <div class="form-group">
                            <label for="catasto_foglio">Foglio:</label>
                            <input type="text" id="catasto_foglio" readonly>
                        </div>
                        <div class="form-group">
                            <label for="catasto_particella">Particella:</label>
                            <input type="text" id="catasto_particella" readonly>
                        </div>
                        <div class="form-group">
                            <label for="catasto_allegato">Allegato:</label>
                            <input type="text" id="catasto_allegato" readonly>
                        </div>
                        <div class="form-group">
                            <label for="catasto_sviluppo">Sviluppo:</label>
                            <input type="text" id="catasto_sviluppo" readonly>
                        </div>
                    </form>
                </div>

                <!-- Informazioni Località -->
                <div class="location-data">
                    <h3>Informazioni Località</h3>
                    <div class="info-row">
                        <label>Comune</label>
                        <div id="location_comune">-</div>
                    </div>
                    <div class="info-row">
                        <label>Provincia</label>
                        <div id="provincia">-</div>
                    </div>
                    <div class="info-row">
                        <label>Regione</label>
                        <div id="regione">-</div>
                    </div>
                    <div class="info-row">
                        <label>Classificazione</label>
                        <div id="seismicZone">-</div>
                    </div>
                    <div class="info-row">
                        <label>Livello di Rischio</label>
                        <div id="riskLevel">-</div>
                    </div>
                    <div class="info-row description">
                        <label>Descrizione</label>
                        <div id="description">-</div>
                    </div>
                </div>
            </div>
            <div class="info-panel">
                <div class="seismic-zone">
                    <!-- Parametri Sismici -->
                    <h3>Parametri Sismici</h3>
                    <div class="seismic-params">
                        
                        <!-- SLO -->
                        <div class="param-group">
                            <h4>SLO - Stato Limite di Operatività</h4>
                            <div class="param-row">
                                <div class="param">
                                    <label>TR</label>
                                    <span id="slo-tr">-</span>
                                </div>
                                <div class="param">
                                    <label>ag</label>
                                    <span id="slo-ag">-</span>
                                </div>
                                <div class="param">
                                    <label>F0</label>
                                    <span id="slo-f0">-</span>
                                </div>
                                <div class="param">
                                    <label>TC</label>
                                    <span id="slo-tc">-</span>
                                </div>
                            </div>
                        </div>

                        <!-- SLD -->
                        <div class="param-group">
                            <h4>SLD - Stato Limite di Danno</h4>
                            <div class="param-row">
                                <div class="param">
                                    <label>TR</label>
                                    <span id="sld-tr">-</span>
                                </div>
                                <div class="param">
                                    <label>ag</label>
                                    <span id="sld-ag">-</span>
                                </div>
                                <div class="param">
                                    <label>F0</label>
                                    <span id="sld-f0">-</span>
                                </div>
                                <div class="param">
                                    <label>TC</label>
                                    <span id="sld-tc">-</span>
                                </div>
                            </div>
                        </div>

                        <!-- SLV -->
                        <div class="param-group">
                            <h4>SLV - Stato Limite di salvaguardia della Vita</h4>
                            <div class="param-row">
                                <div class="param">
                                    <label>TR</label>
                                    <span id="slv-tr">-</span>
                                </div>
                                <div class="param">
                                    <label>ag</label>
                                    <span id="slv-ag">-</span>
                                </div>
                                <div class="param">
                                    <label>F0</label>
                                    <span id="slv-f0">-</span>
                                </div>
                                <div class="param">
                                    <label>TC</label>
                                    <span id="slv-tc">-</span>
                                </div>
                            </div>
                        </div>

                        <!-- SLC -->
                        <div class="param-group">
                            <h4>SLC - Stato Limite di prevenzione del Collasso</h4>
                            <div class="param-row">
                                <div class="param">
                                    <label>TR</label>
                                    <span id="slc-tr">-</span>
                                </div>
                                <div class="param">
                                    <label>ag</label>
                                    <span id="slc-ag">-</span>
                                </div>
                                <div class="param">
                                    <label>F0</label>
                                    <span id="slc-f0">-</span>
                                </div>
                                <div class="param">
                                    <label>TC</label>
                                    <span id="slc-tc">-</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Parametri di calcolo -->
                <h3>Parametri di calcolo</h3>
                <div class="param-group">
                    
                    <div class="form-group">
                        <label for="nominal-life">Vita nominale</label>
                        <select class="form-control" id="nominal-life">
                            <option value="">Seleziona...</option>
                            <option value="10">10 anni (Opere provvisorie)</option>
                            <option value="50">50 anni (Opere ordinarie)</option>
                            <option value="100">100 anni (Opere importanti)</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="building-class">Classe d'uso</label>
                        <select class="form-control" id="building-class">
                            <option value="">Seleziona...</option>
                            <option value="I" title="Edifici con presenza occasionale di persone, come edifici agricoli o magazzini con basso affollamento.">Classe I</option>
                            <option value="II" title="Edifici con normale affollamento, come abitazioni, uffici e negozi, senza funzioni pubbliche o strategiche significative.">Classe II</option>
                            <option value="III" title="Edifici con affollamento significativo, come scuole, teatri, ospedali, o edifici con attività industriali che comportano rischi per l'ambiente.">Classe III</option>
                            <option value="IV" title="Edifici con funzioni pubbliche o strategiche importanti, come ospedali, caserme, centrali elettriche, che devono rimanere operativi in caso di calamità, e strutture di protezione civile.">Classe IV</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="soil-category">Categoria sottosuolo</label>
                        <select class="form-control" id="soil-category">
                            <option value="">Seleziona...</option>
                            <option value="A">A - Ammassi rocciosi</option>
                            <option value="B">B - Rocce tenere e depositi</option>
                            <option value="C">C - Depositi mediamente addensati</option>
                            <option value="D">D - Depositi scarsamente addensati</option>
                            <option value="E">E - Terreni con caratteristiche speciali</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="topographic-category">Categoria topografica</label>
                        <select class="form-control" id="topographic-category">
                            <option value="">Seleziona...</option>
                            <option value="T1">T1 - Superficie pianeggiante</option>
                            <option value="T2">T2 - Pendii con inclinazione > 15°</option>
                            <option value="T3">T3 - Rilievi con larghezza cresta minore</option>
                            <option value="T4">T4 - Rilievi con larghezza cresta molto minore</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="damping">Smorzamento (%)</label>
                        <input type="number" class="form-control" id="damping" value="5.0" min="0.1" max="30" step="0.1">
                        <small class="form-text text-muted">Valore tipico: 5% per strutture in c.a. e acciaio</small>
                    </div>
                    <div class="form-group">
                        <label for="q-factor">Fattore di struttura (q)</label>
                        <input type="number" class="form-control" id="q-factor" value="1.0" min="1.0" max="8.0" step="0.1">
                        <small class="form-text text-muted">
                            SLE (SLO, SLD): q = 1.0<br>
                            SLU (SLV, SLC): q > 1.0 secondo tipologia strutturale
                        </small>
                    </div>
                </div>

                <div class="button-container">
                    <button id="recalculateBtn" class="action-button recalculate-btn" type="button">
                        <i class="fas fa-sync-alt"></i>
                        Ricalcola Parametri
                    </button>
                </div>

                <div class="seismic-results">
                    <h3>Parametri sismici calcolati</h3>
                    <table class="seismic-table">
                        <thead>
                            <tr>
                                <th>Stato Limite</th>
                                <th>TR [anni]</th>
                                <th>ag [g]</th>
                                <th>Fo</th>
                                <th>Tc* [s]</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>SLO</td>
                                <td>-</td>
                                <td>-</td>
                                <td>-</td>
                                <td>-</td>
                            </tr>
                            <tr>
                                <td>SLD</td>
                                <td>-</td>
                                <td>-</td>
                                <td>-</td>
                                <td>-</td>
                            </tr>
                            <tr>
                                <td>SLV</td>
                                <td>-</td>
                                <td>-</td>
                                <td>-</td>
                                <td>-</td>
                            </tr>
                            <tr>
                                <td>SLC</td>
                                <td>-</td>
                                <td>-</td>
                                <td>-</td>
                                <td>-</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Overlay -->
    <div class="overlay" id="overlay"></div>

    <!-- Footer -->
    <footer class="footer" id="footer">
        <div class="footer-copyright">
            2024 ASDP - Advanced Seismic Dissipator Project <span class="footer-divider">|</span> <?php echo $versionManager->getVersion(); ?>
        </div>
        <div class="footer-links">
            <span>Dati sismici: NTC 2018</span>
            <span class="footer-divider">|</span>
            <span>Dati catastali: <a href="https://creativecommons.org/licenses/by-nc-nd/2.0/it/" target="_blank" rel="noopener noreferrer">Agenzia delle Entrate</a></span>
            <span class="footer-divider">|</span>
            <span>Mappa: Google Maps</span>
            <span class="footer-divider">|</span>
            <a href="#" class="privacy-link">Privacy Policy</a>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="js/min/helpers.min.js"></script>
    <script src="js/min/ui.min.js" defer></script>
    <script src="js/min/search.min.js" defer></script>
    
    <!-- Map Manager -->
    <script src="js/map.js"></script>

    <!-- Google Maps API -->
    <script async defer
        src="https://maps.googleapis.com/maps/api/js?key=<?php echo $googleMapsApiKey; ?>&libraries=places,elevation&callback=initMap">
    </script>

    <!-- Include i popup solo quando richiesti -->
    <div id="popupContainer"></div>

    <script>
    function handleLogout(event) {
        event.preventDefault();
        
        fetch('auth/logout.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                window.location.href = 'logout.php';
            } else {
                console.error('Errore durante il logout:', data.message);
            }
        })
        .catch(error => {
            console.error('Errore durante il logout:', error);
        });
    }

    // Gestione popup
    document.addEventListener('DOMContentLoaded', function() {
        // Funzione per caricare un popup
        async function loadPopup(popupType) {
            const container = document.getElementById('popupContainer');
            if (!container.querySelector(`#${popupType}Popup`)) {
                try {
                    const response = await fetch(`${popupType}.php`);
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    const html = await response.text();
                    const div = document.createElement('div');
                    div.innerHTML = html;
                    container.appendChild(div);
                    
                    // Se è il popup account, inizializza gli event listener
                    if (popupType === 'account') {
                        initializeAccountForm();
                    }
                } catch (error) {
                    console.error(`Errore nel caricamento del popup ${popupType}:`, error);
                }
            } else {
                // Se il popup esiste già, inizializza comunque il form
                if (popupType === 'account') {
                    initializeAccountForm();
                }
            }
        }

        // Funzione per aprire un popup
        async function openPopup(popupId) {
            const popupType = popupId.replace('Popup', '');
            await loadPopup(popupType);
            const popup = document.getElementById(popupId);
            if (popup) {
                popup.style.display = 'flex';
            }
        }

        // Funzione per chiudere un popup
        function closePopup(popupId) {
            const popup = document.getElementById(popupId);
            if (popup) {
                popup.style.display = 'none';
            }
        }

        // Funzione per inizializzare il form dell'account
        function initializeAccountForm() {
            const accountForm = document.getElementById('accountForm');
            const passwordForm = document.getElementById('passwordForm');
            
            if (accountForm) {
                accountForm.addEventListener('submit', function(e) {
                    e.preventDefault();
                    
                    const formData = new FormData();
                    formData.append('nome', document.getElementById('nome').value);
                    formData.append('cognome', document.getElementById('cognome').value);
                    
                    fetch('includes/update_account.php', {
                        method: 'POST',
                        body: formData
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            const fullName = `${document.getElementById('nome').value} ${document.getElementById('cognome').value}`;
                            document.querySelector('.username').textContent = fullName;
                            document.querySelector('.user-avatar').textContent = getInitials(fullName);
                            alert('Dati aggiornati con successo');
                        } else {
                            alert('Errore nell\'aggiornamento dei dati: ' + data.message);
                        }
                    })
                    .catch(error => {
                        console.error('Errore:', error);
                        alert('Errore nella comunicazione con il server');
                    });
                });
            }
            
            if (passwordForm) {
                passwordForm.addEventListener('submit', function(e) {
                    e.preventDefault();
                    const formData = new FormData(this);
                    
                    fetch('includes/update_password.php', {
                        method: 'POST',
                        body: formData
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            alert('Password aggiornata con successo');
                            this.reset();
                        } else {
                            alert('Errore nell\'aggiornamento della password: ' + data.message);
                        }
                    })
                    .catch(error => {
                        console.error('Errore:', error);
                        alert('Errore nella comunicazione con il server');
                    });
                });
            }
        }

        // Funzione per ottenere le iniziali
        function getInitials(fullName) {
            const parts = fullName.trim().split(' ');
            let initials = '';
            if (parts[0]) initials += parts[0].charAt(0);
            if (parts[1]) initials += parts[1].charAt(0);
            return initials.toUpperCase();
        }

        // Gestione click sui link della sidebar
        document.querySelectorAll('.nav-item').forEach(item => {
            item.addEventListener('click', function(e) {
                const section = this.getAttribute('data-section');
                
                // Gestione dei diversi popup
                switch(section) {
                    case 'account':
                        e.preventDefault();
                        openPopup('accountPopup');
                        break;
                    case 'impostazioni':
                        e.preventDefault();
                        openPopup('settingsPopup');
                        break;
                    case 'help':
                        e.preventDefault();
                        openPopup('helpPopup');
                        break;
                }
            });
        });

        // Gestione click sul link Privacy Policy nel footer
        document.querySelector('.privacy-link').addEventListener('click', function(e) {
            e.preventDefault();
            openPopup('privacyPopup');
        });

        // Gestione chiusura popup con il pulsante X
        document.addEventListener('click', function(e) {
            if (e.target.matches('.close-btn')) {
                const popup = e.target.closest('.popup');
                if (popup) {
                    popup.style.display = 'none';
                }
            }
        });

        // Chiudi popup quando si clicca fuori
        document.addEventListener('click', function(e) {
            if (e.target.matches('.popup')) {
                e.target.style.display = 'none';
            }
        });
    });
    </script>
</body>
</html>
