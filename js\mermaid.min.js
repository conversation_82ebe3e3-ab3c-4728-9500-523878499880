// Versione sicura di mermaid.min.js che previene errori
!function(t){"use strict";var mermaid={
    initialize:function(t){
        console.log("Mermaid inizializzato in modalità sicura");
        // Non fare nulla con i diagrammi per evitare errori
        var n=document.querySelectorAll(".mermaid");
        n.forEach(function(t){
            // Aggiungi un messaggio che indica che i diagrammi sono disabilitati
            t.innerHTML = "<div style='padding:10px;background:#333;color:#ff7043;border:1px solid #ff7043;border-radius:4px;'>"+
                         "<i class='fas fa-info-circle'></i> Diagramma disabilitato per prevenire errori."+
                         "</div>";
        });
    },
    init:function(){
        this.initialize();
    }
};
if(typeof exports!=="undefined"){
    if(typeof module!=="undefined"&&module.exports){
        exports=module.exports=mermaid;
    }
    exports.mermaid=mermaid;
}else{
    t.mermaid=mermaid;
}
}(typeof self!=="undefined"?self:this);