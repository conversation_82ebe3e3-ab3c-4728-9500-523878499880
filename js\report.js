// Classe per gestire la generazione dei report
class ReportGenerator {
    constructor() {
        this.setupEventListeners();
    }

    setupEventListeners() {
        // Gestione click sul link del report nella sidebar
        const reportLink = document.querySelector('[data-section="report"]');
        if (reportLink) {
            reportLink.addEventListener('click', (e) => {
                e.preventDefault();
                this.generateReport();
            });
        }
    }

    // Recupera i dati dell'ultima ricerca
    getLastSearchData() {
        return {
            address: document.getElementById('address').value,
            latitude: document.getElementById('latitude').value,
            longitude: document.getElementById('longitude').value,
            altitude: document.getElementById('altitude').value,
            comune: document.getElementById('location_comune').textContent,
            provincia: document.getElementById('provincia').textContent,
            regione: document.getElementById('regione').textContent,
            seismicZone: document.getElementById('seismicZone').textContent,
            riskLevel: document.getElementById('riskLevel').textContent,
            // Parametri sismici
            slo: {
                tr: document.getElementById('slo-tr').textContent,
                ag: document.getElementById('slo-ag').textContent,
                f0: document.getElementById('slo-f0').textContent,
                tc: document.getElementById('slo-tc').textContent
            },
            sld: {
                tr: document.getElementById('sld-tr').textContent,
                ag: document.getElementById('sld-ag').textContent,
                f0: document.getElementById('sld-f0').textContent,
                tc: document.getElementById('sld-tc').textContent
            },
            slv: {
                tr: document.getElementById('slv-tr').textContent,
                ag: document.getElementById('slv-ag').textContent,
                f0: document.getElementById('slv-f0').textContent,
                tc: document.getElementById('slv-tc').textContent
            },
            slc: {
                tr: document.getElementById('slc-tr').textContent,
                ag: document.getElementById('slc-ag').textContent,
                f0: document.getElementById('slc-f0').textContent,
                tc: document.getElementById('slc-tc').textContent
            },
            // Parametri di calcolo
            nominalLife: document.getElementById('nominal-life').value,
            buildingClass: document.getElementById('building-class').value,
            soilCategory: document.getElementById('soil-category').value,
            topographicCategory: document.getElementById('topographic-category').value,
            damping: document.getElementById('damping').value,
            qFactor: document.getElementById('q-factor').value
        };
    }

    generateReport() {
        const data = this.getLastSearchData();
        
        // Verifica se ci sono dati da mostrare
        if (!data.address) {
            alert('Non ci sono dati disponibili per generare il report. Effettua prima una ricerca.');
            return;
        }

        // Apri una nuova finestra per il report
        const reportWindow = window.open('', '_blank');
        reportWindow.document.write(this.getReportTemplate(data));
        reportWindow.document.close();
    }

    getReportTemplate(data) {
        const date = new Date().toLocaleString('it-IT');
        
        return `
        <!DOCTYPE html>
        <html lang="it">
        <head>
            <meta charset="UTF-8">
            <title>Report Analisi Sismica - ${data.address}</title>
            <style>
                body {
                    font-family: Arial, sans-serif;
                    line-height: 1.6;
                    margin: 20px;
                    color: #333;
                }
                .header {
                    text-align: center;
                    margin-bottom: 30px;
                    padding-bottom: 10px;
                    border-bottom: 2px solid #333;
                }
                .section {
                    margin-bottom: 20px;
                    page-break-inside: avoid;
                }
                h1 {
                    color: #2c3e50;
                    font-size: 24px;
                }
                h2 {
                    color: #34495e;
                    font-size: 20px;
                    margin-top: 20px;
                }
                .data-grid {
                    display: grid;
                    grid-template-columns: repeat(2, 1fr);
                    gap: 10px;
                    margin: 10px 0;
                }
                .data-item {
                    padding: 5px;
                    background: #f8f9fa;
                }
                .data-label {
                    font-weight: bold;
                }
                table {
                    width: 100%;
                    border-collapse: collapse;
                    margin: 15px 0;
                }
                th, td {
                    border: 1px solid #ddd;
                    padding: 8px;
                    text-align: left;
                }
                th {
                    background-color: #f4f4f4;
                }
                @media print {
                    body {
                        margin: 1cm;
                    }
                    .no-print {
                        display: none;
                    }
                    @page {
                        margin: 1cm;
                    }
                }
                .print-button {
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    padding: 10px 20px;
                    background: #3498db;
                    color: white;
                    border: none;
                    border-radius: 5px;
                    cursor: pointer;
                }
                .print-button:hover {
                    background: #2980b9;
                }
            </style>
        </head>
        <body>
            <button onclick="window.print()" class="print-button no-print">Stampa Report</button>
            
            <div class="header">
                <h1>Report Analisi Sismica</h1>
                <p>Data: ${date}</p>
            </div>

            <div class="section">
                <h2>Informazioni Località</h2>
                <div class="data-grid">
                    <div class="data-item">
                        <span class="data-label">Indirizzo:</span> ${data.address}
                    </div>
                    <div class="data-item">
                        <span class="data-label">Latitudine:</span> ${data.latitude}
                    </div>
                    <div class="data-item">
                        <span class="data-label">Longitudine:</span> ${data.longitude}
                    </div>
                    <div class="data-item">
                        <span class="data-label">Altitudine:</span> ${data.altitude}
                    </div>
                    <div class="data-item">
                        <span class="data-label">Comune:</span> ${data.comune}
                    </div>
                    <div class="data-item">
                        <span class="data-label">Provincia:</span> ${data.provincia}
                    </div>
                    <div class="data-item">
                        <span class="data-label">Regione:</span> ${data.regione}
                    </div>
                    <div class="data-item">
                        <span class="data-label">Zona Sismica:</span> ${data.seismicZone}
                    </div>
                    <div class="data-item">
                        <span class="data-label">Livello di Rischio:</span> ${data.riskLevel}
                    </div>
                </div>
            </div>

            <div class="section">
                <h2>Parametri di Calcolo</h2>
                <div class="data-grid">
                    <div class="data-item">
                        <span class="data-label">Vita Nominale:</span> ${data.nominalLife}
                    </div>
                    <div class="data-item">
                        <span class="data-label">Classe d'uso:</span> ${data.buildingClass}
                    </div>
                    <div class="data-item">
                        <span class="data-label">Categoria Suolo:</span> ${data.soilCategory}
                    </div>
                    <div class="data-item">
                        <span class="data-label">Categoria Topografica:</span> ${data.topographicCategory}
                    </div>
                    <div class="data-item">
                        <span class="data-label">Smorzamento:</span> ${data.damping}%
                    </div>
                    <div class="data-item">
                        <span class="data-label">Fattore q:</span> ${data.qFactor}
                    </div>
                </div>
            </div>

            <div class="section">
                <h2>Parametri Sismici</h2>
                <table>
                    <thead>
                        <tr>
                            <th>Stato Limite</th>
                            <th>TR [anni]</th>
                            <th>ag [g]</th>
                            <th>F0</th>
                            <th>TC* [s]</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>SLO</td>
                            <td>${data.slo.tr}</td>
                            <td>${data.slo.ag}</td>
                            <td>${data.slo.f0}</td>
                            <td>${data.slo.tc}</td>
                        </tr>
                        <tr>
                            <td>SLD</td>
                            <td>${data.sld.tr}</td>
                            <td>${data.sld.ag}</td>
                            <td>${data.sld.f0}</td>
                            <td>${data.sld.tc}</td>
                        </tr>
                        <tr>
                            <td>SLV</td>
                            <td>${data.slv.tr}</td>
                            <td>${data.slv.ag}</td>
                            <td>${data.slv.f0}</td>
                            <td>${data.slv.tc}</td>
                        </tr>
                        <tr>
                            <td>SLC</td>
                            <td>${data.slc.tr}</td>
                            <td>${data.slc.ag}</td>
                            <td>${data.slc.f0}</td>
                            <td>${data.slc.tc}</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="section">
                <p style="font-size: 12px; color: #666; text-align: center; margin-top: 30px;">
                    Report generato automaticamente da A.S.D.P. - Advanced Seismic Dissipator Project<br>
                    © ${new Date().getFullYear()} Tutti i diritti riservati
                </p>
            </div>
        </body>
        </html>`;
    }
}

// Inizializza il generatore di report quando il DOM è caricato
document.addEventListener('DOMContentLoaded', () => {
    new ReportGenerator();
}); 