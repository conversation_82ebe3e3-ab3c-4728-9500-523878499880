<?php
/**
 * save_results.php - Salvataggio risultati calcolo massa inerziale
 * Path: /inertial_mass/api/save_results.php
 */

session_start();

// Verifica autenticazione
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['error' => 'Non autorizzato']);
    exit;
}

// Include database config
require_once '../../includes/db_config.php';

// Verifica metodo
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Metodo non consentito']);
    exit;
}

// Ottieni dati dalla richiesta
$input = json_decode(file_get_contents('php://input'), true);

if (!$input || !isset($input['calculation_id'])) {
    http_response_code(400);
    echo json_encode(['error' => 'Dati non validi']);
    exit;
}

try {
    // Inizia transazione
    $conn->begin_transaction();
    
    // Salva il calcolo principale
    $sql = "INSERT INTO inertial_mass_calculations (
                calculation_id,
                user_id,
                project_id,
                input_data,
                results,
                created_at
            ) VALUES (?, ?, ?, ?, ?, NOW())";
    
    $stmt = $conn->prepare($sql);
    $userId = $_SESSION['user_id'];
    $projectId = $input['project_id'] ?? null;
    $inputData = json_encode($input['input_data'] ?? []);
    $results = json_encode($input['results'] ?? []);
    
    $stmt->bind_param("siiss", 
        $input['calculation_id'],
        $userId,
        $projectId,
        $inputData,
        $results
    );
    
    if (!$stmt->execute()) {
        throw new Exception('Errore nel salvataggio del calcolo');
    }
    
    $calculationDbId = $conn->insert_id;
    
    // Salva i dettagli per piano se presenti
    if (isset($input['results']['floor_forces'])) {
        $sqlFloor = "INSERT INTO inertial_mass_floor_details (
                        calculation_id,
                        floor_level,
                        mass,
                        height,
                        force
                    ) VALUES (?, ?, ?, ?, ?)";
        
        $stmtFloor = $conn->prepare($sqlFloor);
        
        foreach ($input['results']['floor_forces'] as $floor) {
            $stmtFloor->bind_param("iiddd",
                $calculationDbId,
                $floor['level'],
                $floor['mass'],
                $floor['height'],
                $floor['force']
            );
            
            if (!$stmtFloor->execute()) {
                throw new Exception('Errore nel salvataggio dei dettagli piano');
            }
        }
    }
    
    // Commit transazione
    $conn->commit();
    
    // Log attività
    logActivity($userId, 'inertial_mass_calculation', $calculationDbId);
    
    // Restituisci successo
    echo json_encode([
        'success' => true,
        'calculation_id' => $input['calculation_id'],
        'db_id' => $calculationDbId,
        'message' => 'Calcolo salvato con successo'
    ]);
    
} catch (Exception $e) {
    // Rollback in caso di errore
    $conn->rollback();
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Errore nel salvataggio: ' . $e->getMessage()
    ]);
}

/**
 * Registra l'attività nel log
 * @param int $userId ID utente
 * @param string $action Azione eseguita
 * @param int $referenceId ID di riferimento
 */
function logActivity($userId, $action, $referenceId) {
    global $conn;
    
    $sql = "INSERT INTO activity_log (user_id, action, reference_id, created_at) 
            VALUES (?, ?, ?, NOW())";
    
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("isi", $userId, $action, $referenceId);
    $stmt->execute();
}
