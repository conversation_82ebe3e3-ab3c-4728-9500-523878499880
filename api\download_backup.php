<?php
require_once '../includes/db_config.php';
session_start();

// Verifica autenticazione
if (!isset($_SESSION['user_id']) || !isset($_SESSION['role']) || $_SESSION['role'] !== 'admin') {
    http_response_code(403);
    die('Accesso non autorizzato');
}

// Verifica parametro file
if (!isset($_GET['file'])) {
    http_response_code(400);
    die('Parametro file mancante');
}

// Sanitizza il nome del file
$filename = basename($_GET['file']);
$backup_dir = __DIR__ . '/../backups/';
$file_path = $backup_dir . $filename;

// Verifica esistenza file
if (!file_exists($file_path)) {
    http_response_code(404);
    die('File non trovato');
}

// Verifica che il file sia nella cartella backups
if (dirname(realpath($file_path)) !== realpath($backup_dir)) {
    http_response_code(403);
    die('Accesso non autorizzato');
}

// Verifica che il file sia registrato nel database
try {
    $conn = getConnection();
    $stmt = $conn->prepare("SELECT * FROM backups WHERE filename = ?");
    $stmt->execute([$filename]);
    if (!$stmt->fetch()) {
        http_response_code(404);
        die('Backup non trovato nel database');
    }
} catch (Exception $e) {
    error_log("Errore verifica backup: " . $e->getMessage());
    http_response_code(500);
    die('Errore interno del server');
}

// Imposta gli header per il download
header('Content-Type: application/zip');
header('Content-Disposition: attachment; filename="' . $filename . '"');
header('Content-Length: ' . filesize($file_path));
header('Cache-Control: no-cache, must-revalidate');
header('Pragma: no-cache');
header('Expires: 0');

// Invia il file
readfile($file_path); 