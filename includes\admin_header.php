<?php
session_start();

// Verifica se l'utente è loggato e ha il ruolo di admin
if (!isset($_SESSION['user_id']) || !isset($_SESSION['role']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../index.php');
    exit();
}

// Ottieni il percorso corrente
$current_page = basename($_SERVER['PHP_SELF']);
?>
<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="icon" type="image/x-icon" href="../favicon.ico">
    <title>Admin Dashboard - ASDP</title>
    
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <!-- Aggiorno jQuery alla versione più recente -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="../js/admin.js" defer></script>
    <script>
    // Sostituisco unload con beforeunload
    $(window).on('beforeunload', function() {
        // Cleanup necessario
    });
    </script>
    <style>
        /* Stili per il codice semplificato */
        .code-block {
            background: #2d2d2d;
            border-radius: 4px;
            padding: 0.75rem;
            font-family: 'Consolas', 'Monaco', monospace;
            color: #e0e0e0;
            margin: 0.5rem 0;
            overflow-x: auto;
            white-space: pre-wrap;
            word-wrap: break-word;
        }
        
        .code-inline {
            background: #2d2d2d;
            padding: 0.2rem 0.4rem;
            border-radius: 3px;
            font-family: 'Consolas', 'Monaco', monospace;
            color: #e0e0e0;
        }

        .log-entry {
            background: #1e1e1e;
            border-left: 3px solid #FF7043;
            padding: 0.5rem;
            margin: 0.5rem 0;
            font-family: 'Consolas', 'Monaco', monospace;
        }

        /* Stili esistenti */
        body {
            margin: 0;
            padding: 0;
            font-family: 'Segoe UI', Arial, sans-serif;
            background-color: #000000;
            color: #FFFFFF;
            height: 100vh;
            overflow: hidden;
        }

        .admin-header {
            background: #1E1E1E;
            border-bottom: 1px solid #FF7043;
            padding: 1rem;
            position: sticky;
            top: 0;
            z-index: 1000;
        }

        .admin-nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1200px;
            margin: 0 auto;
        }

        .admin-nav-left {
            display: flex;
            align-items: center;
            gap: 2rem;
        }

        .admin-logo {
            height: 40px;
        }

        .admin-menu {
            display: flex;
            gap: 1rem;
        }

        .admin-menu a {
            color: #FFFFFF;
            text-decoration: none;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .admin-menu a:hover,
        .admin-menu a.active {
            background: #FF7043;
            color: #000000;
        }

        .admin-nav-right {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .admin-user {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: #FFFFFF;
            cursor: pointer;
            position: relative;
        }

        .admin-user .fa-user-shield {
            font-size: 1.2rem;
            color: #FF7043;
        }

        .admin-user .fa-chevron-down {
            font-size: 0.8rem;
            color: #BDBDBD;
            transition: transform 0.3s ease;
        }

        .admin-user:hover .fa-chevron-down {
            transform: rotate(180deg);
        }

        .admin-user-menu {
            position: absolute;
            top: 100%;
            right: 0;
            background: #1E1E1E;
            border: 1px solid #333;
            border-radius: 4px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.2);
            min-width: 200px;
            z-index: 1000;
            display: none;
        }

        .admin-user-menu.active {
            display: block;
        }

        .admin-user-menu a {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1rem;
            color: #FFFFFF;
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .admin-user-menu a:hover {
            background: #FF7043;
            color: #000000;
        }

        .admin-user-menu .divider {
            height: 1px;
            background: #333;
            margin: 0.5rem 0;
        }

        .admin-logout {
            color: #FF7043;
            text-decoration: none;
            padding: 0.5rem 1rem;
            border: 1px solid #FF7043;
            border-radius: 4px;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .admin-logout:hover {
            background: #FF7043;
            color: #000000;
        }

        .content {
            max-width: 1200px;
            margin: 0.5rem auto;
            padding: 0 1rem;
            height: calc(100vh - 80px); /* 80px è l'altezza dell'header */
            overflow: hidden;
        }

        h1, h2, h3 {
            margin-top: 0;
            margin-bottom: 1rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: #1E1E1E;
            border-radius: 8px;
            padding: 1.5rem;
            text-align: center;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.2);
        }

        .stat-card i {
            font-size: 2rem;
            color: #FF7043;
            margin-bottom: 1rem;
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            color: #FFFFFF;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: #BDBDBD;
            font-size: 0.9rem;
        }

        .log-container {
            background: #1E1E1E;
            border-radius: 8px;
            padding: 1rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.2);
            height: calc(100% - 200px); /* 200px è l'altezza delle stats + margini */
            overflow-y: auto;
        }

        .log-title {
            color: #FF7043;
            font-size: 1.5rem;
            margin-bottom: 1.5rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid #FF7043;
        }

        .log-entry {
            padding: 0.75rem;
            border-bottom: 1px solid #333333;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .log-entry:last-child {
            border-bottom: none;
        }

        .log-time {
            color: #BDBDBD;
            font-size: 0.9rem;
        }

        .log-user {
            color: #FF7043;
            font-weight: 500;
        }

        .log-action {
            color: #FFFFFF;
        }

        @media (max-width: 768px) {
            .admin-menu {
                display: none;
            }

            .admin-nav-right {
                gap: 0.5rem;
            }

            .admin-user span {
                display: none;
            }
        }

        /* Stili per il menu a tendina */
        .admin-dropdown {
            position: relative;
            display: inline-block;
        }

        .admin-dropdown-btn {
            background: none;
            border: none;
            color: #FFFFFF;
            padding: 0.5rem 1rem;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 1rem;
        }

        .admin-dropdown-content {
            display: none;
            position: absolute;
            background: #1E1E1E;
            min-width: 200px;
            box-shadow: 0 8px 16px rgba(0,0,0,0.2);
            z-index: 1001;
            border-radius: 4px;
            border: 1px solid #333;
            top: 100%;
            left: 0;
        }

        .admin-dropdown-content a {
            color: #FFFFFF;
            padding: 12px 16px;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.3s ease;
        }

        .admin-dropdown-content a:hover {
            background-color: #FF7043;
            color: #000000;
        }

        .admin-dropdown:hover .admin-dropdown-content {
            display: block;
        }

        /* Stili per i popup */
        .doc-popup {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.8);
            z-index: 2000;
        }

        .doc-popup-content {
            position: relative;
            width: 90%;
            height: 90%;
            margin: 2% auto;
            background: #1E1E1E;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 0 20px rgba(0,0,0,0.5);
        }

        .doc-popup-close {
            position: absolute;
            right: 20px;
            top: 20px;
            color: #FF7043;
            font-size: 24px;
            cursor: pointer;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: all 0.3s ease;
        }

        .doc-popup-close:hover {
            background: #FF7043;
            color: #000000;
        }

        .doc-popup iframe {
            width: 100%;
            height: calc(100% - 40px);
            border: none;
            margin-top: 20px;
            background: #FFFFFF;
        }

        /* Aggiustamenti per il contenuto principale */
        .content {
            padding-bottom: 50px;
        }

        @media (max-width: 768px) {
            .content {
                padding-bottom: 70px;
            }
        }
    </style>
</head>
<body>
    <!-- Popup per la documentazione tecnica -->
    <div id="docTecnicaPopup" class="doc-popup">
        <div class="doc-popup-content">
            <iframe src="../docs/documentazione_tecnica.html" frameborder="0"></iframe>
        </div>
    </div>

    <!-- Popup per la relazione tecnica -->
    <div id="relazioneTecnicaPopup" class="doc-popup">
        <div class="doc-popup-content">
            <iframe src="../docs/relazione_tecnica.html" frameborder="0"></iframe>
        </div>
    </div>

    <!-- Popup per la AI Roadmap rimosso -->

    <header class="admin-header">
        <nav class="admin-nav">
            <div class="admin-nav-left">
                <img src="../img/icons/asdp-logo.svg" alt="ASDP Logo" class="admin-logo">
                <div class="admin-menu">
                    <a href="dashboard.php" <?php echo $current_page == 'dashboard.php' ? 'class="active"' : ''; ?>>
                        <i class="fas fa-tachometer-alt"></i> Dashboard
                    </a>
                    <a href="users.php" <?php echo $current_page == 'users.php' ? 'class="active"' : ''; ?>>
                        <i class="fas fa-users"></i> Utenti
                    </a>
                    <a href="backup.php" <?php echo $current_page == 'backup.php' ? 'class="active"' : ''; ?>>
                        <i class="fas fa-database"></i> Backup
                    </a>
                    <a href="logs.php" <?php echo $current_page == 'logs.php' ? 'class="active"' : ''; ?>>
                        <i class="fas fa-list"></i> Log
                    </a>
                    <a href="settings.php" <?php echo $current_page == 'settings.php' ? 'class="active"' : ''; ?>>
                        <i class="fas fa-cog"></i> Impostazioni
                    </a>
                </div>
            </div>
            <div class="admin-nav-right">
                <div class="admin-user" onclick="toggleUserMenu()">
                    <i class="fas fa-user-shield"></i>
                    <span>Admin ASDP</span>
                    <i class="fas fa-chevron-down"></i>
                    <div class="admin-user-menu" id="userMenu">
                        <a href="#" onclick="updateDocs(event)" class="menu-item">
                            <i class="fas fa-sync"></i> Aggiorna Doc
                        </a>
                        <a href="#" onclick="openDocPopup('docTecnicaPopup')" class="menu-item">
                            <i class="fas fa-file-code"></i> Doc. Tecnica
                        </a>
                        <a href="#" onclick="openDocPopup('relazioneTecnicaPopup')" class="menu-item">
                            <i class="fas fa-file-alt"></i> Relazione Tecnica
                        </a>
                        <!-- Riferimento a AI Roadmap rimosso -->
                                                <div class="menu-divider"></div>
                        <a href="#" onclick="handleLogout(event)" class="menu-item logout-link">
                            <i class="fas fa-sign-out-alt"></i> Logout
                        </a>
                    </div>
                </div>
            </div>
        </nav>
    </header>

    <script>
        // Verifica l'esistenza dell'elemento prima di aggiungere l'event listener
        const adminMenuToggle = document.getElementById('adminMenuToggle');
        if (adminMenuToggle) {
            adminMenuToggle.addEventListener('click', function() {
                const adminMenu = document.getElementById('adminMenu');
                if (adminMenu) {
                    adminMenu.classList.toggle('show');
                }
            });
        }

        function openDocPopup(popupId) {
            document.getElementById(popupId).style.display = 'block';
            // Chiudi il menu utente quando si apre un popup
            document.querySelector('.admin-user-menu').classList.remove('active');
        }

        // Funzione globale per chiudere i popup
        window.closeDocPopup = function() {
            // Chiudi tutti i popup
            document.querySelectorAll('.doc-popup').forEach(popup => {
                popup.style.display = 'none';
            });
        }

        // Ascolta i messaggi dagli iframe
        window.addEventListener('message', function(event) {
            if (event.data === 'closePopup') {
                closeDocPopup();
            }
        });

        function updateDocs(event) {
            event.preventDefault();
            const now = new Date();
            const timeString = now.toLocaleTimeString('it-IT');
            const dateString = now.toLocaleDateString('it-IT');
            
            fetch('../docs/generate_docs.php')
                .then(response => response.text())
                .then(result => {
                    alert(`Documentazione aggiornata con successo!\nData: ${dateString}\nOra: ${timeString}`);
                    // Aggiorna gli iframe se i popup sono aperti
                    const docTecnicaFrame = document.querySelector('#docTecnicaPopup iframe');
                    const relazioneTecnicaFrame = document.querySelector('#relazioneTecnicaPopup iframe');
                    // Riferimento a aiRoadmapFrame rimosso
                    
                    if (docTecnicaFrame) {
                        docTecnicaFrame.contentWindow.location.reload();
                    }
                    if (relazioneTecnicaFrame) {
                        relazioneTecnicaFrame.contentWindow.location.reload();
                    }
                    // Ricarica di aiRoadmapFrame rimossa
                })
                .catch(error => {
                    alert('Errore durante l\'aggiornamento della documentazione: ' + error);
                });
        }

        function handleLogout(event) {
            event.preventDefault();
            if (confirm('Sei sicuro di voler effettuare il logout?')) {
                window.location.href = 'logout.php';
            }
        }

        // Funzione per il toggle del menu utente
        function toggleUserMenu() {
            document.querySelector('.admin-user-menu').classList.toggle('active');
        }

        // Chiudi il menu quando si clicca fuori
        document.addEventListener('click', function(event) {
            const menu = document.getElementById('userMenu');
            const userSection = document.querySelector('.admin-user');
            if (!userSection.contains(event.target) && menu.classList.contains('active')) {
                menu.classList.remove('active');
            }
        });

        // Chiudi i popup quando si preme ESC
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                document.querySelectorAll('.doc-popup').forEach(popup => {
                    popup.style.display = 'none';
                });
                // Chiudi anche il menu utente
                document.querySelector('.admin-user-menu').classList.remove('active');
            }
        });
    </script>
