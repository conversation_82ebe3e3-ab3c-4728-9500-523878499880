// Funzione di inizializzazione della mappa
window.initMap = function() {
    console.log('Google Maps API caricata, inizializzazione mappa...');
    try {
        const mapManager = new MapManager('map', {
            center: { lat: 41.9028, lng: 12.4964 }, // Centro di Roma come default
            zoom: 6,
            mapTypeId: 'hybrid',  // Usa la vista satellite+strade
            styles: [
                {
                    featureType: 'poi',  // Punti di interesse
                    elementType: 'labels',
                    stylers: [{ visibility: 'off' }]  // Nasconde le etichette dei POI
                },
                {
                    featureType: 'transit',  // Fermate dei mezzi pubblici
                    elementType: 'labels',
                    stylers: [{ visibility: 'off' }]  // Nasconde le etichette del trasporto pubblico
                },
                {
                    featureType: 'road',  // Strade
                    elementType: 'labels',
                    stylers: [{ visibility: 'on' }]  // Mantiene visibili i nomi delle strade
                },
                {
                    featureType: 'administrative',  // Città e divisioni amministrative
                    elementType: 'labels',
                    stylers: [{ visibility: 'on' }]  // Mantiene visibili i nomi delle città
                }
            ]
        });
        window.mapManager = mapManager;
        mapManager.init();
        console.log('Mappa inizializzata con successo');
    } catch (error) {
        console.error('Errore durante l\'inizializzazione della mappa:', error);
    }
};

class MapManager {
    constructor(mapElementId, options = {}) {
        this.mapElementId = mapElementId;
        this.options = {
            center: options.center || { lat: 41.9028, lng: 12.4964 },
            zoom: options.zoom || 6,
            mapTypeId: 'hybrid',  // Usa la vista satellite+strade
            styles: [
                {
                    featureType: 'poi',  // Punti di interesse
                    elementType: 'labels',
                    stylers: [{ visibility: 'off' }]  // Nasconde le etichette dei POI
                },
                {
                    featureType: 'transit',  // Fermate dei mezzi pubblici
                    elementType: 'labels',
                    stylers: [{ visibility: 'off' }]  // Nasconde le etichette del trasporto pubblico
                },
                {
                    featureType: 'road',  // Strade
                    elementType: 'labels',
                    stylers: [{ visibility: 'on' }]  // Mantiene visibili i nomi delle strade
                },
                {
                    featureType: 'administrative',  // Città e divisioni amministrative
                    elementType: 'labels',
                    stylers: [{ visibility: 'on' }]  // Mantiene visibili i nomi delle città
                }
            ]
        };
        this.map = null;
        this.marker = null;
        this.searchBox = null;
        this.geocoder = null;
        this.infoWindow = null;
        this.communeBoundaries = null;
        this.catastoLayer = null;
        this.elevator = null;
        this.currentLocation = null;
    }

    init() {
        console.log('Inizializzazione MapManager...');
        try {
            // Verifica se l'API di Google Maps è caricata correttamente
            if (typeof google === 'undefined' || typeof google.maps === 'undefined') {
                throw new Error('Google Maps API non caricata. Verifica che non ci siano blocchi da estensioni o firewall.');
            }

            const mapElement = document.getElementById(this.mapElementId);
            if (!mapElement) {
                throw new Error(`Elemento mappa con ID "${this.mapElementId}" non trovato.`);
            }

            // Inizializza la mappa con le opzioni di base
            const mapOptions = {
                ...this.options,
                mapTypeId: google.maps.MapTypeId[this.options.mapTypeId.toUpperCase()],
                mapTypeControl: true,
                mapTypeControlOptions: {
                    style: google.maps.MapTypeControlStyle.DROPDOWN_MENU,
                    position: google.maps.ControlPosition.TOP_RIGHT
                },
                streetViewControl: false,
                fullscreenControl: true,
                zoomControl: true,
                scrollwheel: true
            };

            this.map = new google.maps.Map(mapElement, mapOptions);

            // Verifica se i servizi necessari sono disponibili
            if (!google.maps.Geocoder || !google.maps.ElevationService) {
                throw new Error('Servizi Google Maps non disponibili. Disabilita eventuali blocchi nel browser.');
            }

            this.geocoder = new google.maps.Geocoder();
            this.elevator = new google.maps.ElevationService();

            // Verifica se i servizi sono stati inizializzati correttamente
            if (!this.geocoder || !this.elevator) {
                throw new Error('Errore nell\'inizializzazione dei servizi Google Maps.');
            }

            // Inizializza i controlli prima di tutto il resto
            this.setupControls();

            // Poi inizializza gli altri componenti
            this.setupSearchBox();
            this.setupMapListeners();
            this.setupGeolocation();
            this.loadCommuneBoundaries();

            // Aggiungo l'event listener per il pulsante ricalcola
            this.setupRecalculateButton();

            // Verifica finale
            if (!this.map) {
                throw new Error('Mappa non inizializzata correttamente.');
            }

            // Forza la visualizzazione dei controlli
            setTimeout(() => {
                const controls = document.querySelector('.map-controls');
                if (controls) {
                    controls.style.display = 'flex';
                    controls.style.visibility = 'visible';
                    controls.style.opacity = '1';
                    console.log('Controlli mappa forzati a visibili');
                }
            }, 1000);

            console.log('MapManager inizializzato con successo');
            
        } catch (error) {
            console.error('Errore durante l\'inizializzazione della mappa:', error);
            alert('Errore durante l\'inizializzazione della mappa: ' + error.message);
        }
    }

    setupGeolocation() {
        const geolocateBtn = document.getElementById('geolocateBtn');
        if (!geolocateBtn) {
            console.error('Pulsante geolocalizzazione non trovato');
            return;
        }

        const checkPermissions = async () => {
            try {
                // Verifica se il browser supporta la geolocalizzazione
                if (!navigator.geolocation) {
                    throw new Error('Geolocalizzazione non supportata dal browser');
                }

                // Verifica i permessi
                const result = await navigator.permissions.query({ name: 'geolocation' });
                console.log('Stato permessi geolocalizzazione:', result.state);

                return result.state;
            } catch (error) {
                console.error('Errore nel controllo dei permessi:', error);
                return 'error';
            }
        };

        const getGeolocationOptions = () => {
            // Su localhost usiamo impostazioni più permissive
            if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
                return {
                    enableHighAccuracy: false,
                    timeout: 30000,        // Timeout più lungo per localhost
                    maximumAge: 300000     // Cache più lunga (5 minuti)
                };
            }
            
            // Impostazioni standard per produzione
            return {
                enableHighAccuracy: true,
                timeout: 10000,
                maximumAge: 30000
            };
        };

        geolocateBtn.addEventListener('click', async () => {
            try {
                const permissionState = await checkPermissions();
                
                if (permissionState === 'denied') {
                    throw new Error('Permessi di geolocalizzazione negati. Controlla le impostazioni del browser.');
                }

                if (permissionState === 'error') {
                    throw new Error('Errore nel controllo dei permessi di geolocalizzazione.');
                }

                geolocateBtn.classList.add('loading');

                // Usa una Promise per gestire la geolocalizzazione
                const position = await new Promise((resolve, reject) => {
                    // Usa IP Geolocation come fallback su localhost
                    if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
                        // Prova prima la geolocalizzazione del browser
                        navigator.geolocation.getCurrentPosition(
                            resolve,
                            () => {
                                // Se fallisce, usa una posizione di default per l'Italia
                                console.log('Usando posizione di default per test su localhost');
                                resolve({
                                    coords: {
                                        latitude: 41.9028,  // Roma
                                        longitude: 12.4964,
                                        accuracy: 1000
                                    }
                                });
                            },
                            getGeolocationOptions()
                        );
                    } else {
                        // In produzione usa la geolocalizzazione standard
                        navigator.geolocation.getCurrentPosition(
                            resolve,
                            reject,
                            getGeolocationOptions()
                        );
                    }
                });

                // Gestisci il successo
                await this.handleGeolocationSuccess(position);

            } catch (error) {
                this.handleGeolocationError(error);
            } finally {
                geolocateBtn.classList.remove('loading');
            }
        });
    }

    async handleGeolocationSuccess(position) {
        try {
            const accuracy = position.coords.accuracy;
            console.log('Accuratezza posizione:', accuracy, 'metri');
            
            const location = new google.maps.LatLng(
                position.coords.latitude,
                position.coords.longitude
            );
            
            console.log('Posizione ottenuta:', location.toString());

            if (accuracy > 1000) {
                console.warn('Accuratezza della posizione bassa:', accuracy, 'metri');
                alert('La posizione rilevata potrebbe non essere molto precisa.\n\n' +
                      'Suggerimenti per migliorare la precisione:\n' +
                      '1. Disabilita eventuali VPN o proxy\n' +
                      '2. Usa Google Chrome o Microsoft Edge\n' +
                      '3. Verifica di essere connesso a Internet\n' +
                      '4. Prova a riavviare il browser');
            }
            
            this.map.setCenter(location);
            this.map.setZoom(accuracy > 1000 ? 13 : 15);

            if (!this.marker) {
                this.marker = new google.maps.Marker({
                    map: this.map,
                    position: location,
                    animation: google.maps.Animation.DROP,
                    draggable: true
                });

                new google.maps.Circle({
                    map: this.map,
                    center: location,
                    radius: accuracy,
                    strokeColor: '#FF0000',
                    strokeOpacity: 0.8,
                    strokeWeight: 2,
                    fillColor: '#FF0000',
                    fillOpacity: 0.1
                });

                this.marker.addListener('dragend', () => {
                    const newPos = this.marker.getPosition();
                    this.getSeismicData(newPos);
                });
            } else {
                this.marker.setPosition(location);
            }

            await this.getSeismicData(location);

        } catch (error) {
            console.error('Errore durante la gestione della posizione:', error);
            throw error;
        }
    }

    handleGeolocationError(error) {
        let message = 'Si è verificato un errore sconosciuto';
        
        if (error.code) {
            switch(error.code) {
                case error.PERMISSION_DENIED:
                    message = 'Accesso alla posizione negato.\n\n' +
                             'Per abilitare la geolocalizzazione:\n' +
                             '1. Clicca sull\'icona del lucchetto nella barra degli indirizzi\n' +
                             '2. Trova "Posizione" nelle impostazioni\n' +
                             '3. Seleziona "Consenti"\n' +
                             '4. Ricarica la pagina';
                    break;
                case error.POSITION_UNAVAILABLE:
                    message = 'Impossibile determinare la posizione.\n\n' +
                             'Possibili soluzioni:\n' +
                             '1. Disabilita temporaneamente AdBlock e simili\n' +
                             '2. Usa Google Chrome o Microsoft Edge\n' +
                             '3. Disabilita VPN o proxy\n' +
                             '4. Verifica la connessione Internet\n' +
                             '5. Prova la navigazione in incognito';
                    break;
                case error.TIMEOUT:
                    message = 'Tempo scaduto per la richiesta della posizione.\n\n' +
                             'Suggerimenti:\n' +
                             '1. Verifica la connessione Internet\n' +
                             '2. Riprova tra qualche secondo\n' +
                             '3. Riavvia il browser se il problema persiste';
                    break;
            }
        }

        console.error('Errore geolocalizzazione:', {
            code: error.code,
            message: error.message,
            details: message
        });

        alert(message);
    }

    setupSearchBox() {
        const input = document.getElementById('addressSearch');
        if (!input) {
            console.error('Elemento addressSearch non trovato');
            return;
        }

        this.searchBox = new google.maps.places.Autocomplete(input, {
            componentRestrictions: { country: 'it' }
        });

        this.searchBox.addListener('place_changed', async () => {
            const place = this.searchBox.getPlace();
            if (!place.geometry) {
                console.warn('Nessuna geometria trovata per questo luogo');
                return;
            }

            // Centra la mappa sulla località selezionata
            this.map.setCenter(place.geometry.location);
            this.map.setZoom(17);

            // Aggiorna il marker
            this.updateMarker(place.geometry.location);
            
            // Recupera i dati sismici
            await this.getSeismicData(place.geometry.location);
        });
    }

    setupMapListeners() {
        if (!this.map) {
            console.error('Mappa non inizializzata');
            return;
        }

        this.map.addListener('click', async (e) => {
            const location = e.latLng;
            this.updateMarker(location);
            await this.getSeismicData(location);
        });
    }

    setupControls() {
        // Rimuovo i controlli non più necessari
    }

    updateMarker(location) {
        console.log('Aggiornamento marker con location:', location);
        
        if (!this.marker) {
            this.marker = new google.maps.Marker({
                map: this.map,
                draggable: true
            });

            this.marker.addListener('dragend', async () => {
                const newLocation = this.marker.getPosition();
                await this.getSeismicData(newLocation);
            });
        }

        this.marker.setPosition(location);
        this.currentLocation = location;
        
        // Aggiorna i campi delle coordinate
        if (location) {
            document.getElementById('latitude').value = location.lat().toFixed(6);
            document.getElementById('longitude').value = location.lng().toFixed(6);
        }
        
        console.log('Marker aggiornato, nuova currentLocation:', this.currentLocation);
    }

    async getSeismicData(location) {
        try {
            // Salva la posizione corrente come proprietà della classe
            this.currentLocation = location;
            
            // Converti le coordinate
            const lat = location.lat();
            const lng = location.lng();

            // Aggiorna i campi delle coordinate
            document.getElementById('latitude').value = lat.toFixed(6);
            document.getElementById('longitude').value = lng.toFixed(6);

            // Ottieni l'altitudine
            await this.getElevation(location);

            // Ottieni l'indirizzo e l'altitudine
            await this.reverseGeocode(location);

            // Recupera i dati catastali
            await this.getCadastralData(location);

            // Recupera i dati sismici
            await this.fetchSeismicData(lat, lng);

            // Recupera i parametri sismici
            await this.fetchSeismicParams(lat, lng);

            console.log('Dati sismici aggiornati per:', lat, lng);
        } catch (error) {
            console.error('Errore nel recupero dei dati sismici:', error);
        }
    }

    async getElevation(location) {
        try {
            const result = await new Promise((resolve, reject) => {
                this.elevator.getElevationForLocations({
                    locations: [location]
                }, (results, status) => {
                    if (status === 'OK' && results[0]) {
                        resolve(results[0]);
                    } else {
                        reject(new Error('Impossibile ottenere l\'altitudine'));
                    }
                });
            });

            // Aggiorna il campo altitudine
            document.getElementById('altitude').value = Math.round(result.elevation);
        } catch (error) {
            console.error('Errore nel recupero dell\'altitudine:', error);
            document.getElementById('altitude').value = '-';
        }
    }

    async reverseGeocode(location) {
        try {
            const response = await this.geocoder.geocode({ location: location });
            if (response.results[0]) {
                document.getElementById('address').value = response.results[0].formatted_address;
            }
        } catch (error) {
            console.error('Errore nel reverse geocoding:', error);
            document.getElementById('address').value = 'Indirizzo non trovato';
        }
    }

    async getCadastralData(location) {
        try {
            const response = await fetch('api/cadastral_data.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(location)
            });
            
            const data = await response.json();
            
            if (data.status === 'service_unavailable') {
                // Mostra il popup di errore
                const errorPopup = document.createElement('div');
                errorPopup.className = 'error-popup';
                errorPopup.innerHTML = `
                    <div class="error-content">
                        <p>${data.error}</p>
                        <button onclick="this.parentElement.parentElement.remove()">Chiudi</button>
                    </div>
                `;
                document.body.appendChild(errorPopup);
                
                // Rimuovi il popup dopo 5 secondi
                setTimeout(() => {
                    errorPopup.remove();
                }, 5000);
                
                return;
            }

            // Aggiorna i campi con i dati catastali
            document.getElementById('catasto_foglio').value = data.foglio || '-';
            document.getElementById('catasto_particella').value = data.particella || '-';
            document.getElementById('catasto_comune').value = data.comune || '-';
            document.getElementById('catasto_sezione').value = data.sezione || '-';
            document.getElementById('catasto_allegato').value = data.allegato || '-';
            document.getElementById('catasto_sviluppo').value = data.sviluppo || '-';

        } catch (error) {
            console.error('Errore nel recupero dei dati catastali:', error);
            // In caso di errore, pulisci i campi
            document.getElementById('catasto_foglio').value = '-';
            document.getElementById('catasto_particella').value = '-';
            document.getElementById('catasto_comune').value = '-';
            document.getElementById('catasto_sezione').value = '-';
            document.getElementById('catasto_allegato').value = '-';
            document.getElementById('catasto_sviluppo').value = '-';
        }
    }

    async fetchSeismicData(lat, lng) {
        try {
            const response = await fetch('api/seismic_data.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ lat, lng })
            });
            
            const data = await response.json();
            
            if (data.success) {
                document.getElementById('seismicZone').textContent = `Zona ${data.data.zona} - ${data.data.categoria}`;
                document.getElementById('riskLevel').textContent = data.data.rischio;
                document.getElementById('location_comune').textContent = data.data.comune;
                document.getElementById('provincia').textContent = data.data.provincia;
                document.getElementById('regione').textContent = data.data.regione;
                document.getElementById('description').textContent = data.data.descrizione;
            } else {
                throw new Error(data.message || 'Errore nel recupero dei dati sismici');
            }
        } catch (error) {
            console.error('Errore nel recupero dei dati sismici:', error);
            document.getElementById('seismicZone').textContent = '-';
            document.getElementById('riskLevel').textContent = '-';
            document.getElementById('location_comune').textContent = '-';
            document.getElementById('provincia').textContent = '-';
            document.getElementById('regione').textContent = '-';
            document.getElementById('description').textContent = '-';
        }
    }

    async fetchSeismicParams(lat, lng) {
        try {
            const response = await fetch('api/calculate_seismic_params.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ lat, lng })
            });
            
            const data = await response.json();
            
            if (data.success) {
                // Aggiorna i parametri sismici per ogni stato limite
                ['SLO', 'SLD', 'SLV', 'SLC'].forEach(state => {
                    const params = data.data[state];
                    document.getElementById(`${state.toLowerCase()}-tr`).textContent = params.TR;
                    document.getElementById(`${state.toLowerCase()}-ag`).textContent = params.ag.toFixed(3);
                    document.getElementById(`${state.toLowerCase()}-f0`).textContent = params.F0.toFixed(3);
                    document.getElementById(`${state.toLowerCase()}-tc`).textContent = params.TC.toFixed(3);
                });
            } else {
                throw new Error(data.message || 'Errore nel recupero dei parametri sismici');
            }
        } catch (error) {
            console.error('Errore nel recupero dei parametri sismici:', error);
            ['SLO', 'SLD', 'SLV', 'SLC'].forEach(state => {
                document.getElementById(`${state.toLowerCase()}-tr`).textContent = '-';
                document.getElementById(`${state.toLowerCase()}-ag`).textContent = '-';
                document.getElementById(`${state.toLowerCase()}-f0`).textContent = '-';
                document.getElementById(`${state.toLowerCase()}-tc`).textContent = '-';
            });
        }
    }

    loadCommuneBoundaries() {
        // Implementazione del caricamento dei confini comunali
        // Da implementare in base ai dati disponibili
    }

    toggleCatasto() {
        try {
            if (!this.catastoLayer) {
                // Verifica lo zoom corrente
                if (this.map.getZoom() < 14) {
                    this.showErrorPopup('Aumenta lo zoom per visualizzare il layer catastale (minimo zoom: 14)');
                    this.map.setZoom(14);
                    return;
                }

                // Crea il layer WMS del catasto
                const proxyUrl = 'api/catasto_proxy.php';
                const wmsUrl = 'https://wms.cartografia.agenziaentrate.gov.it/inspire/wms/ows01.php';
                const map = this.map;

                // Mostra un indicatore di caricamento
                const toggleButton = document.getElementById('catasto-toggle');
                if (toggleButton) {
                    toggleButton.classList.add('loading');
                }

                // Verifica prima la disponibilità del servizio
                fetch(`${proxyUrl}?wmsurl=${encodeURIComponent(wmsUrl)}&SERVICE=WMS&VERSION=1.3.0&REQUEST=GetCapabilities`)
                    .then(async response => {
                        try {
                            return await response.json();
                        } catch (error) {
                            // Se la risposta non è JSON valido, significa che il servizio non è raggiungibile
                            throw new Error('Servizio non raggiungibile');
                        }
                    })
                    .then(data => {
                        if (data.status === 'service_unavailable') {
                            throw new Error(data.error || 'Servizio non raggiungibile');
                        }

                        // Se il servizio è disponibile, crea il layer WMS
                        const wmsLayer = new google.maps.ImageMapType({
                            getTileUrl: function(coord, zoom) {
                                const proj = map.getProjection();
                                const zfactor = Math.pow(2, zoom);
                                const top = proj.fromPointToLatLng(new google.maps.Point(coord.x * 256 / zfactor, coord.y * 256 / zfactor));
                                const bot = proj.fromPointToLatLng(new google.maps.Point((coord.x + 1) * 256 / zfactor, (coord.y + 1) * 256 / zfactor));
                                
                                const url = `${proxyUrl}?` +
                                    `wmsurl=${encodeURIComponent(wmsUrl)}&` +
                                    'SERVICE=WMS&' +
                                    'VERSION=1.3.0&' +
                                    'REQUEST=GetMap&' +
                                    'LAYERS=CP.CadastralParcel&' +
                                    'STYLES=&' +
                                    'CRS=EPSG:4326&' +
                                    `BBOX=${bot.lat()},${top.lng()},${top.lat()},${bot.lng()}&` +
                                    'WIDTH=256&' +
                                    'HEIGHT=256&' +
                                    'FORMAT=image/png';
                                
                                return url;
                            },
                            tileSize: new google.maps.Size(256, 256),
                            isPng: true,
                            name: 'Catasto',
                            alt: 'Layer Catastale',
                            minZoom: 14,
                            maxZoom: 20
                        });

                        this.catastoLayer = wmsLayer;
                        this.map.overlayMapTypes.push(wmsLayer);

                        if (toggleButton) {
                            toggleButton.classList.remove('loading');
                            toggleButton.classList.add('active');
                        }
                    })
                    .catch(error => {
                        console.error('Errore nel caricamento del layer catastale:', error);
                        // Mostra sempre "Servizio non raggiungibile" per questo tipo di errore
                        this.showErrorPopup('Servizio non raggiungibile');
                        if (toggleButton) {
                            toggleButton.classList.remove('loading');
                            toggleButton.classList.remove('active');
                        }
                    });

            } else {
                // Rimuovi il layer del catasto
                const overlays = this.map.overlayMapTypes;
                for (let i = 0; i < overlays.getLength(); i++) {
                    if (overlays.getAt(i) === this.catastoLayer) {
                        overlays.removeAt(i);
                        break;
                    }
                }
                this.catastoLayer = null;
                
                const toggleButton = document.getElementById('catasto-toggle');
                if (toggleButton) {
                    toggleButton.classList.remove('active');
                }
            }
        } catch (error) {
            console.error('Errore nel toggle del catasto:', error);
            this.showErrorPopup('Servizio non raggiungibile');
            const toggleButton = document.getElementById('catasto-toggle');
            if (toggleButton) {
                toggleButton.classList.remove('loading');
                toggleButton.classList.remove('active');
            }
        }
    }

    showErrorPopup(message) {
        try {
            console.log('Mostro popup di errore:', message);
            
            // Rimuovi eventuali popup esistenti
            const existingPopups = document.querySelectorAll('.error-popup');
            existingPopups.forEach(popup => popup.remove());
            
            // Crea il nuovo popup
            const errorPopup = document.createElement('div');
            errorPopup.className = 'error-popup';
            errorPopup.innerHTML = `
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Chiudi"></button>
                </div>
            `;
            
            // Posiziona il popup nella parte superiore della mappa
            const mapControls = document.querySelector('.map-controls');
            if (mapControls) {
                mapControls.parentNode.insertBefore(errorPopup, mapControls.nextSibling);
            } else {
                const mapElement = document.getElementById('map');
                if (mapElement) {
                    mapElement.parentNode.insertBefore(errorPopup, mapElement);
                } else {
                    document.body.appendChild(errorPopup);
                }
            }
            
            // Aggiungi l'event listener al pulsante di chiusura
            const closeButton = errorPopup.querySelector('.btn-close');
            if (closeButton) {
                closeButton.addEventListener('click', () => errorPopup.remove());
            }
            
            // Rimuovi il popup dopo 5 secondi
            setTimeout(() => {
                if (errorPopup && errorPopup.parentElement) {
                    errorPopup.remove();
                }
            }, 5000);
            
            console.log('Popup di errore creato con successo');
        } catch (error) {
            console.error('Errore nella creazione del popup:', error);
        }
    }

    updateLocationInfo(lat, lng) {
        // ... codice esistente per lat/lng ...

        // Aggiungiamo la richiesta di elevazione
        const location = { lat: lat, lng: lng };
        this.elevator.getElevationForLocations({
            locations: [location],
        }, (results, status) => {
            if (status === 'OK' && results[0]) {
                // Arrotondiamo l'altitudine a 1 decimale
                const altitude = Math.round(results[0].elevation * 10) / 10;
                document.getElementById('altitude').value = altitude;
            } else {
                document.getElementById('altitude').value = 'N/D';
            }
        });
    }

    setupRecalculateButton() {
        try {
            const recalcBtn = document.getElementById('recalculateBtn');
            if (!recalcBtn) {
                console.error('Pulsante ricalcolo non trovato');
                return;
            }

            recalcBtn.addEventListener('click', async (e) => {
                try {
                    // Disabilita il pulsante e mostra loading
                    recalcBtn.disabled = true;
                    recalcBtn.classList.add('loading');
                    
                    // Verifica che ci sia una posizione selezionata
                    if (!this.currentLocation) {
                        throw new Error('Seleziona prima un punto sulla mappa');
                    }

                    // Recupera i parametri dai campi input
                    const params = this.getCalculationParams();
                    if (!params.isValid) {
                        throw new Error(params.error);
                    }

                    // Esegue il ricalcolo
                    await this.recalculateSeismicParams();

                    // Feedback successo
                    recalcBtn.classList.add('success');
                    setTimeout(() => recalcBtn.classList.remove('success'), 2000);

                } catch (error) {
                    // Mostra errore
                    console.error('Errore nel ricalcolo:', error);
                    recalcBtn.classList.add('error');
                    setTimeout(() => recalcBtn.classList.remove('error'), 2000);
                    
                    // Mostra messaggio all'utente
                    alert(error.message || 'Errore nel ricalcolo dei parametri');
                    
                } finally {
                    // Riabilita il pulsante e rimuovi loading
                    recalcBtn.disabled = false;
                    recalcBtn.classList.remove('loading');
                }
            });

            console.log('Setup pulsante ricalcolo completato');
        } catch (error) {
            console.error('Errore nel setup del pulsante:', error);
        }
    }

    getCalculationParams() {
        try {
            console.log('Inizio recupero parametri di calcolo');
            
            // Recupera i valori dei parametri
            const nominalLife = document.getElementById('nominal-life')?.value;
            console.log('Vita nominale:', nominalLife);
            
            const buildingClass = document.getElementById('building-class')?.value;
            console.log('Classe edificio:', buildingClass);
            
            const soilCategory = document.getElementById('soil-category')?.value;
            console.log('Categoria suolo:', soilCategory);
            
            const topographicCategory = document.getElementById('topographic-category')?.value;
            console.log('Categoria topografica:', topographicCategory);
            
            const damping = document.getElementById('damping')?.value;
            console.log('Smorzamento:', damping);
            
            const qFactor = document.getElementById('q-factor')?.value;
            console.log('Fattore q:', qFactor);

            // Recupera le coordinate dai campi input
            const latField = document.getElementById('latitude')?.value;
            const lngField = document.getElementById('longitude')?.value;
            console.log('Coordinate dai campi:', { lat: latField, lng: lngField });

            // Se non abbiamo né currentLocation né coordinate nei campi, errore
            if (!latField || !lngField) {
                console.error('Coordinate mancanti');
                return { isValid: false, error: 'Seleziona un punto sulla mappa' };
            }

            // Usa le coordinate dai campi input
            const coordinates = {
                lat: parseFloat(latField),
                lng: parseFloat(lngField)
            };
            console.log('Coordinate parsate:', coordinates);

            // Validazione parametri
            if (!nominalLife) {
                console.error('Vita nominale mancante');
                return { isValid: false, error: 'Seleziona la vita nominale' };
            }
            if (!buildingClass) {
                console.error('Classe edificio mancante');
                return { isValid: false, error: 'Seleziona la classe d\'uso' };
            }
            if (!soilCategory) {
                console.error('Categoria suolo mancante');
                return { isValid: false, error: 'Seleziona la categoria di sottosuolo' };
            }
            if (!topographicCategory) {
                console.error('Categoria topografica mancante');
                return { isValid: false, error: 'Seleziona la categoria topografica' };
            }
            if (!damping) {
                console.error('Smorzamento mancante');
                return { isValid: false, error: 'Inserisci il valore di smorzamento' };
            }
            if (!qFactor) {
                console.error('Fattore q mancante');
                return { isValid: false, error: 'Inserisci il fattore di struttura' };
            }

            // Prepara l'oggetto con tutti i parametri
            const params = {
                lat: coordinates.lat,
                lng: coordinates.lng,
                nominalLife: parseInt(nominalLife),
                buildingClass: buildingClass,
                soilCategory: soilCategory,
                topographicCategory: topographicCategory,
                damping: parseFloat(damping),
                qFactor: parseFloat(qFactor)
            };

            console.log('Parametri finali:', params);
            return { isValid: true, params: params };

        } catch (error) {
            console.error('Errore nel recupero dei parametri:', error);
            return { isValid: false, error: 'Errore nel recupero dei parametri' };
        }
    }

    async recalculateSeismicParams() {
        try {
            console.log('Inizio ricalcolo parametri sismici');
            
            // Recupera e valida i parametri
            const paramResult = this.getCalculationParams();
            console.log('Risultato getCalculationParams:', paramResult);
            
            if (!paramResult.isValid) {
                console.error('Parametri non validi:', paramResult.error);
                throw new Error(paramResult.error);
            }

            console.log('Parametri per il ricalcolo:', paramResult.params);

            console.log('Invio richiesta al server...');
            const response = await fetch('api/calculate_seismic_params.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(paramResult.params)
            });

            console.log('Risposta grezza dal server:', response);
            console.log('Status:', response.status);
            console.log('Status text:', response.statusText);
            console.log('Headers:', [...response.headers.entries()]);
            
            if (!response.ok) {
                console.error('Errore HTTP:', response.status, response.statusText);
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const responseText = await response.text();
            console.log('Risposta testuale dal server:', responseText);

            let data;
            try {
                data = JSON.parse(responseText);
                console.log('Dati parsati dal server:', data);
            } catch (e) {
                console.error('Errore nel parsing JSON:', e);
                console.error('Testo che ha causato l\'errore:', responseText);
                throw new Error('Risposta del server non valida');
            }

            if (data.success) {
                console.log('Aggiornamento parametri sismici...');
                // Aggiorna i parametri sismici per ogni stato limite
                ['SLO', 'SLD', 'SLV', 'SLC'].forEach(state => {
                    console.log(`Aggiornamento parametri per ${state}...`);
                    const params = data.data[state];
                    // Aggiorna i parametri nella sezione principale
                    document.getElementById(`${state.toLowerCase()}-tr`).textContent = params.TR;
                    document.getElementById(`${state.toLowerCase()}-ag`).textContent = params.ag.toFixed(3);
                    document.getElementById(`${state.toLowerCase()}-f0`).textContent = params.F0.toFixed(3);
                    document.getElementById(`${state.toLowerCase()}-tc`).textContent = params.TC.toFixed(3);
                });

                // Aggiorna anche la tabella dei risultati
                this.updateSeismicResults(data.data);

                // Aggiorna il modulo massa inerziale se è aperto
                this.updateInertialMassModal();

                console.log('Parametri aggiornati con successo');
            } else {
                console.error('Errore nei dati:', data.message);
                throw new Error(data.message || 'Errore nel ricalcolo dei parametri sismici');
            }
        } catch (error) {
            console.error('Errore nel ricalcolo dei parametri:', error);
            throw error;
        }
    }

    /**
     * Aggiorna il modulo massa inerziale se è aperto
     */
    updateInertialMassModal() {
        try {
            // Verifica se il modulo massa inerziale è aperto
            const modal = document.getElementById('inertial-mass-modal');
            if (modal && modal.style.display !== 'none' && window.initInertialMassModal) {
                console.log('Aggiornamento modulo massa inerziale dopo ricalcolo...');
                
                // Recupera i nuovi dati sismici
                const updatedSeismicData = window.getCurrentSeismicData ? window.getCurrentSeismicData() : null;
                
                if (updatedSeismicData) {
                    // Reinizializza il modulo con i nuovi dati
                    window.initInertialMassModal(updatedSeismicData);
                    console.log('Modulo massa inerziale aggiornato con successo');
                } else {
                    console.warn('Impossibile recuperare i dati sismici aggiornati');
                }
            }
        } catch (error) {
            console.error('Errore nell\'aggiornamento del modulo massa inerziale:', error);
        }
    }

    updateSeismicResults(data) {
        console.log('Aggiornamento tabella risultati con:', data);
        
        // Aggiorna i valori nella tabella dei risultati
        const states = ['SLO', 'SLD', 'SLV', 'SLC'];
        const table = document.querySelector('.seismic-table tbody');
        
        if (!table) {
            console.error('Tabella risultati non trovata');
            return;
        }
        
        states.forEach((state, index) => {
            const row = table.rows[index];
            const params = data[state];
            
            if (!row || !params) {
                console.error(`Riga o parametri mancanti per stato ${state}`);
                return;
            }
            
            try {
                // Aggiorna le celle della riga
                row.cells[1].textContent = params.TR;
                row.cells[2].textContent = params.ag.toFixed(3);
                row.cells[3].textContent = params.F0.toFixed(3);
                row.cells[4].textContent = params.TC.toFixed(3);
            } catch (error) {
                console.error(`Errore nell'aggiornamento della riga per ${state}:`, error);
            }
        });
    }
}
