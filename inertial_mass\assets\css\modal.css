/**
 * modal.css - Stili per la finestra modale massa inerziale
 * Path: /inertial_mass/assets/css/modal.css
 */

/* Overlay modale */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    backdrop-filter: blur(4px);
}

/* Stile per il contenitore principale della finestra modale */
.modal-container {
    background-color: #1E1E1E; /* Sfondo grigio scuro leggermente più chiaro */
    color: #f8f9fa;           /* Testo chiaro per leggibilità */
    font-family: 'Segoe UI', Arial, sans-serif; /* Aggiunto font-family */
    border: 1px solid #555e67; /* Bordo per definire i limiti della modale */
    border-radius: 0.3rem;     /* Angoli leggermente arrotondati */
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.5); /* Ombra per dare profondità */
    display: flex;
    flex-direction: column;
    width: 90% !important;             /* Aggiunto per responsività e controllo larghezza, FORZATO */
    max-width: 800px !important;       /* Aumentato per dare più spazio ai risultati, FORZATO */
    max-height: 85vh !important;       /* Aumentato per dare più spazio ai risultati, FORZATO */
    margin: auto; /* Aggiunto per aiutare con la centratura se l'overlay è flex */
}

/* Stile per l'intestazione della modale */
.modal-header {
    padding: 0.75rem 1.5rem; /* Ridotto padding verticale */
    border-bottom: 1px solid #555e67; /* Linea di separazione dall'area corpo */
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header .modal-title {
    margin-bottom: 0;
    line-height: 1.5;
    font-size: 1.25rem;
    color: #f8f9fa; /* Assicura che il titolo sia chiaro */
}

.modal-header .close-modal-btn {
    padding: 0.5rem 1rem;
    margin: -0.5rem -1rem -0.5rem auto;
    background-color: transparent;
    border: 0;
    font-size: 1.5rem;
    font-weight: 700;
    line-height: 1;
    color: #f8f9fa;
    text-shadow: 0 1px 0 #000;
    opacity: .7;
}

.modal-header .close-modal-btn:hover {
    opacity: 1;
    color: #fff;
}

/* Stile per il corpo della modale (dove c'è il form) */
.modal-body {
    position: relative;
    flex: 1 1 auto;
    padding: 1rem 1.5rem; /* Ridotto padding verticale */
    overflow-y: auto; /* Abilita lo scorrimento verticale se il contenuto è troppo lungo */
}

/* Stile per il piè di pagina della modale */
.modal-footer {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: flex-end; /* Allinea i pulsanti a destra */
    padding: 0.5rem 1.5rem; /* Ridotto padding verticale */
    border-top: 1px solid #555e67; /* Linea di separazione dall'area corpo */
    border-bottom-right-radius: calc(0.3rem - 1px);
    border-bottom-left-radius: calc(0.3rem - 1px);
}

.modal-footer > * {
    margin: 0.25rem;
}

.modal-footer button.btn-primary {
    background-color: #D97706; /* Arancione scuro per il pulsante primario */
    border-color: #C26A05;
    color: white;
}

.modal-footer button.btn-primary:hover {
    background-color: #C26A05;
    border-color: #AD5E04;
}

/* Sezioni form */
.form-section {
    margin-bottom: 2rem;
}

.form-section h3 {
    color: #f8f9fa; /* Assicura che il titolo sia chiaro */
    margin-bottom: 1rem;
    font-size: 1.1rem;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

/* Info grid per dati automatici */
.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    background-color: #2a2a2a; /* Sfondo scuro per i dati */
    padding: 1rem;
    border-radius: 8px;
    border: 1px solid #6c757d;
}

.info-item {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.info-item label {
    font-size: 0.875rem;
    color: #f8f9fa; /* Assicura che le etichette siano chiare */
}

.info-item span {
    font-weight: 600;
    color: #f8f9fa; /* Assicura che i dati siano chiari */
}

/* Form elements */
.form-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 1rem;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.form-group label {
    font-size: 0.875rem;
    color: #f8f9fa; /* Assicura che le etichette siano chiare */
    font-weight: 500;
}

#inertialMassForm label {
    color: #f8f9fa; 
    font-weight: 500; /* Leggermente più marcato per leggibilità */
    margin-bottom: 0.3rem; /* Spazio sotto l'etichetta */
    display: block; /* Assicura che occupi la sua riga */
}

#inertialMassForm .form-control,
#inertialMassForm input[type="text"],
#inertialMassForm input[type="number"],
#inertialMassForm input[type="date"],
#inertialMassForm select {
    background-color: #404040 !important; /* Sfondo più scuro per i campi, per contrasto con il corpo modale, FORZATO */
    color: #f8f9fa !important; /* FORZATO */
    border: 1px solid #555e67 !important; /* FORZATO */
    padding: 0.65rem 0.75rem; /* Leggermente aggiustato il padding */
    border-radius: 0.25rem; /* Angoli meno arrotondati per un look più sharp */
    width: 100%; /* Assicura che i campi occupino tutta la larghezza disponibile nel loro contenitore */
    box-sizing: border-box; /* Include padding e border nella larghezza/altezza totale */
}

#inertialMassForm select:focus {
    background-color: #404040 !important; /* Sfondo leggermente più chiaro al focus */
    border-color: #80bdff; 
    color: #f8f9fa;
    outline: none; /* Rimuove l'outline di default del browser */
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25); /* Ombra per indicare il focus */
}

/* Rimuoviamo la regola per le option, il browser usa lo stile nativo */

/* Piani container */
#floors-container {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.floor-item {
    background-color: #404040; /* Sfondo scuro per i piani */
    border: 1px solid #6c757d;
    border-radius: 8px;
    padding: 1rem;
    position: relative;
    animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.floor-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.floor-header h4 {
    margin: 0;
    color: #f8f9fa; /* Assicura che il titolo sia chiaro */
}

.btn-remove-floor {
    background: none;
    border: none;
    color: #dc3545; /* Colore rosso per il pulsante di rimozione */
    cursor: pointer;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.875rem;
    transition: all 0.2s;
}

.btn-remove-floor:hover {
    background-color: rgba(220, 53, 69, 0.1);
}

/* Bottoni */
.add-floor-btn {
    background-color: #6c757d; /* Grigio scuro come i pulsanti secondari */
    color: white;
    border: 1px solid #6c757d;
    padding: 0.375rem 0.75rem;
    border-radius: 0.25rem;
    cursor: pointer;
    transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out;
    font-size: 0.9rem;
    margin-bottom: 1rem; /* Aggiunto spazio sotto */
}

.add-floor-btn:hover {
    background-color: #5a6268;
    border-color: #545b62;
}

/* Stili per la tabella dei piani (se presente, per coerenza) */
.floor-table {
    border-radius: 0.25rem;
}

/* Stili per la barra di scorrimento (WebKit) */
.modal-body::-webkit-scrollbar {
    width: 8px;
}

.modal-body::-webkit-scrollbar-track {
    background: #3E444A; /* Sfondo della traccia leggermente più chiaro dello sfondo del corpo */
    border-radius: 4px;
}

.modal-body::-webkit-scrollbar-thumb {
    background-color: #6c757d; /* Colore del cursore della scrollbar (grigio) */
    border-radius: 4px;
    border: 2px solid #3E444A; /* Crea un piccolo padding attorno al thumb */
}

.modal-body::-webkit-scrollbar-thumb:hover {
    background-color: #5a6268; /* Cursore più scuro all'hover */
}

/* Sezione Risultati */
.modal-results {
    margin-top: 2rem;
    padding: 1.5rem;
    max-height: 70vh;
    overflow-y: auto;
    overflow-x: hidden;
}

/* Stili per la barra di scorrimento della sezione risultati */
.modal-results::-webkit-scrollbar {
    width: 8px;
}

.modal-results::-webkit-scrollbar-track {
    background: #3E444A;
    border-radius: 4px;
}

.modal-results::-webkit-scrollbar-thumb {
    background-color: #6c757d;
    border-radius: 4px;
    border: 2px solid #3E444A;
}

.modal-results::-webkit-scrollbar-thumb:hover {
    background-color: #5a6268;
}

/* Stili per i risultati professionali */
.results-summary {
    background: linear-gradient(135deg, #2a2a2a 0%, #3a3a3a 100%);
    border: 1px solid #555e67;
    border-radius: 12px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.results-summary h4 {
    color: #D97706;
    font-size: 1.4rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    text-align: center;
    border-bottom: 2px solid #D97706;
    padding-bottom: 0.5rem;
}

.result-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    transition: background-color 0.2s ease;
}

.result-item:last-child {
    border-bottom: none;
}

.result-item:hover {
    background-color: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    padding-left: 1rem;
    padding-right: 1rem;
}

.result-label {
    font-weight: 500;
    color: #f8f9fa;
    font-size: 1.1rem;
}

.result-value {
    font-weight: 700;
    color: #D97706;
    font-size: 1.2rem;
    background: rgba(217, 119, 6, 0.1);
    padding: 0.5rem 1rem;
    border-radius: 6px;
    border: 1px solid rgba(217, 119, 6, 0.3);
}

.results-details {
    background-color: #2a2a2a;
    border: 1px solid #555e67;
    border-radius: 12px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.results-details h4 {
    color: #f8f9fa;
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.results-details h4::before {
    content: "📊";
    font-size: 1.2rem;
}

.results-table {
    width: 100%;
    border-collapse: collapse;
    background-color: #1e1e1e;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.results-table thead {
    background: linear-gradient(135deg, #D97706 0%, #C26A05 100%);
}

.results-table th {
    padding: 1rem;
    text-align: left;
    font-weight: 600;
    color: white;
    font-size: 1rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.results-table td {
    padding: 1rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    color: #f8f9fa;
    font-size: 1rem;
    transition: background-color 0.2s ease;
}

.results-table tbody tr:hover {
    background-color: rgba(217, 119, 6, 0.1);
}

.results-table tbody tr:last-child td {
    border-bottom: none;
}

.results-analysis {
    background: linear-gradient(135deg, #1a4d3a 0%, #2d5a47 100%);
    border: 1px solid #4a7c59;
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.results-analysis h4 {
    color: #4ade80;
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.results-analysis h4::before {
    content: "🤖";
    font-size: 1.1rem;
}

.analysis-content {
    color: #e5f3e8;
    line-height: 1.5;
    font-size: 0.95rem;
    white-space: pre-wrap;
    max-height: 200px;
    overflow-y: auto;
    padding-right: 0.5rem;
}

/* Scrollbar per l'analisi AI */
.analysis-content::-webkit-scrollbar {
    width: 6px;
}

.analysis-content::-webkit-scrollbar-track {
    background: rgba(74, 124, 89, 0.3);
    border-radius: 3px;
}

.analysis-content::-webkit-scrollbar-thumb {
    background-color: #4ade80;
    border-radius: 3px;
}

.analysis-content::-webkit-scrollbar-thumb:hover {
    background-color: #22c55e;
}

/* Stili per il messaggio di caricamento */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(30, 30, 30, 0.95);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    border-radius: 0.3rem;
    backdrop-filter: blur(4px);
}

.loading-content {
    text-align: center;
    color: #f8f9fa;
}

.loading-spinner {
    width: 60px;
    height: 60px;
    border: 4px solid rgba(217, 119, 6, 0.3);
    border-top: 4px solid #D97706;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 1.5rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-title {
    font-size: 1.4rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #D97706;
}

.loading-message {
    font-size: 1rem;
    margin-bottom: 0.5rem;
    opacity: 0.9;
}

.loading-progress {
    width: 300px;
    height: 4px;
    background-color: rgba(217, 119, 6, 0.2);
    border-radius: 2px;
    overflow: hidden;
    margin-top: 1rem;
}

.loading-progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #D97706, #F59E0B);
    border-radius: 2px;
    animation: progress 3s ease-in-out infinite;
}

@keyframes progress {
    0% { width: 0%; }
    50% { width: 70%; }
    100% { width: 100%; }
}

.loading-steps {
    margin-top: 1.5rem;
    text-align: left;
    max-width: 400px;
}

.loading-step {
    display: flex;
    align-items: center;
    margin-bottom: 0.8rem;
    font-size: 0.9rem;
    opacity: 0.7;
    transition: opacity 0.3s ease;
}

.loading-step.active {
    opacity: 1;
    color: #D97706;
}

.loading-step.completed {
    opacity: 0.8;
    color: #4ade80;
}

.loading-step-icon {
    width: 16px;
    height: 16px;
    margin-right: 0.8rem;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.7rem;
}

.loading-step.active .loading-step-icon {
    background-color: #D97706;
    animation: pulse 1.5s ease-in-out infinite;
}

.loading-step.completed .loading-step-icon {
    background-color: #4ade80;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Miglioramenti per il pulsante di calcolo */
.btn-primary {
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.btn-primary:disabled {
    opacity: 0.7;
    cursor: not-allowed;
}

.btn-loader {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.spinner {
    animation: spin 1s linear infinite;
}

.spinner .path {
    stroke: currentColor;
    stroke-linecap: round;
    stroke-dasharray: 90, 150;
    stroke-dashoffset: 0;
    stroke-width: 5;
    animation: dash 1.5s ease-in-out infinite;
}

@keyframes dash {
    0% {
        stroke-dasharray: 1, 150;
        stroke-dashoffset: 0;
    }
    50% {
        stroke-dasharray: 90, 150;
        stroke-dashoffset: -35;
    }
    100% {
        stroke-dasharray: 90, 150;
        stroke-dashoffset: -124;
    }
}

/* Responsive */
@media (max-width: 768px) {
    .modal-container {
        width: 95%;
        max-height: 95vh;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .info-grid {
        grid-template-columns: 1fr;
    }
    
    .results-summary,
    .results-details,
    .results-analysis {
        padding: 1.5rem;
    }
    
    .result-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
    
    .results-table {
        font-size: 0.9rem;
    }
    
    .results-table th,
    .results-table td {
        padding: 0.75rem 0.5rem;
    }
    
    .loading-progress {
        width: 250px;
    }
    
    .loading-steps {
        max-width: 300px;
    }
}
