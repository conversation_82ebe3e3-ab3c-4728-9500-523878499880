<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once '../../includes/db_config.php';
require_once '../../includes/Logger.php';

// Inizializza il logger
$logger = Logger::getInstance();
$logger->info('=== INIZIO PROCESSO DI LOGIN ===');

// Inizia la sessione
session_start();
$logger->logSession();

header('Content-Type: application/json');

try {
    // Log della richiesta HTTP
    $logger->logRequest();
    
    // Leggi il contenuto della richiesta
    $rawData = file_get_contents('php://input');
    $logger->debug('Raw input ricevuto', ['data' => $rawData]);
    
    // Decodifica JSON
    $data = json_decode($rawData, true);
    $logger->debug('Dati decodificati', $data);

    // Verifica la presenza dei dati necessari
    if (!$data || !isset($data['username']) || !isset($data['password'])) {
        throw new Exception('Dati di login mancanti');
    }

    // Sanitizza l'input
    $username = htmlspecialchars(strip_tags($data['username']), ENT_QUOTES, 'UTF-8');
    $password = $data['password'];
    $logger->debug('Username sanitizzato', ['username' => $username]);

    try {
        // Ottieni la connessione PDO
        $conn = getConnection();
        $logger->debug("Connessione al database stabilita");

        // Query con prepared statement
        $query = "SELECT id, username, password, nome, cognome, role FROM users WHERE username = ? LIMIT 1";
        $logger->logDatabase($query, [$username]);
        
        $stmt = $conn->prepare($query);
        $stmt->execute([$username]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        $logger->debug('Dati utente trovati', $user);

        if ($user && password_verify($password, $user['password'])) {
            // Login riuscito
            $logger->info('Password verificata correttamente per l\'utente', ['username' => $username]);
            
            // Distruggi la sessione esistente
            session_unset();
            session_destroy();
            session_start();
            session_regenerate_id(true);
            
            // Log della nuova sessione
            $logger->debug('Nuova sessione creata', ['session_id' => session_id()]);
            
            // Imposta i dati della sessione
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['username'] = $user['username'];
            $_SESSION['nome'] = $user['nome'];
            $_SESSION['cognome'] = $user['cognome'];
            $_SESSION['role'] = $user['role'];

            $logger->debug('Dati sessione impostati', $_SESSION);

            // Log dell'accesso nel database
            logAccess($conn, $user['id'], 'Login effettuato');
            $logger->info('Accesso registrato nel database', ['user_id' => $user['id']]);

            // Prepara la risposta
            $redirect = $user['role'] === 'admin' ? 'admin/dashboard.php' : 'home.php';
            $logger->info('Reindirizzamento preparato', ['redirect' => $redirect, 'role' => $user['role']]);

            $response = [
                'success' => true,
                'message' => 'Login effettuato con successo',
                'redirect' => $redirect,
                'role' => $user['role'],
                'session' => $_SESSION
            ];
            
            $logger->info('Risposta di successo preparata', $response);
            $logger->logAuthentication($username, true);
        } else {
            // Login fallito
            $logger->warning('Login fallito - Password non valida', ['username' => $username]);
            logAccess($conn, null, 'Tentativo di login fallito per username: ' . $username);
            $logger->logAuthentication($username, false, 'Password non valida');
            throw new Exception('Credenziali non valide');
        }

    } catch (PDOException $e) {
        $logger->error('Errore database', ['error' => $e->getMessage()]);
        throw new Exception('Errore durante il login');
    }

    echo json_encode($response);
    $logger->info('Risposta inviata al client', $response);

} catch (Exception $e) {
    $logger->error('Errore durante il login', ['error' => $e->getMessage()]);
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

// Funzione per registrare l'accesso nel database
function logAccess($conn, $userId, $action) {
    global $logger;
    
    try {
        $query = "INSERT INTO access_logs (user_id, action, ip_address, user_agent) VALUES (?, ?, ?, ?)";
        $params = [
            $userId,
            $action,
            $_SERVER['REMOTE_ADDR'],
            $_SERVER['HTTP_USER_AGENT']
        ];
        
        $logger->logDatabase($query, $params);
        
        $stmt = $conn->prepare($query);
        $stmt->execute($params);
        return true;
    } catch (PDOException $e) {
        $logger->error("Errore nel logging dell'accesso", ['error' => $e->getMessage()]);
        return false;
    }
}

$logger->info("Fine processo di login\n");
?>
