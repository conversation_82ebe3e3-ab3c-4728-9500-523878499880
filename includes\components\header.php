<?php
if (!defined('ACCESS_ALLOWED')) {
    die('Accesso diretto non consentito');
}
?>
<header class="top-bar">
    <div class="header-left">
        <div class="logo">ASDP</div>
    </div>
    <div class="header-right">
        <div class="user-info">
            <span class="user-name"><?php echo htmlspecialchars($_SESSION['user_name'] ?? 'Utente'); ?></span>
            <div class="user-initials"><?php 
                $name = $_SESSION['user_name'] ?? 'U';
                echo htmlspecialchars(strtoupper(substr($name, 0, 1))); 
            ?></div>
        </div>
    </div>
</header> 