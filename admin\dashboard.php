<?php
require_once '../includes/db_config.php';
session_start();

// Verifica se l'utente è loggato e ha il ruolo di admin
if (!isset($_SESSION['user_id']) || !isset($_SESSION['role']) || $_SESSION['role'] !== 'admin') {
    header("Location: ../index.php");
    exit;
}

// Funzione per ottenere le statistiche del sistema
function getSystemStats() {
    try {
        $conn = getConnection();
        
        // Numero totale utenti (solo ruolo 'user')
        $stmt = $conn->query("SELECT COUNT(*) as total_users FROM users WHERE role = 'user'");
        $totalUsers = $stmt->fetch(PDO::FETCH_ASSOC)['total_users'];
        
        // Numero di accessi di oggi (solo utenti non admin)
        $stmt = $conn->query("SELECT COUNT(DISTINCT al.user_id) as active_today 
                            FROM access_logs al 
                            JOIN users u ON al.user_id = u.id 
                            WHERE DATE(al.timestamp) = CURDATE() 
                            AND u.role = 'user'
                            AND al.action = 'Login effettuato'
                            AND al.user_id NOT IN (SELECT id FROM users WHERE role = 'admin')");
        $activeToday = $stmt->fetch(PDO::FETCH_ASSOC)['active_today'];
        
        // Numero di accessi negli ultimi 7 giorni (solo utenti non admin)
        $stmt = $conn->query("SELECT COUNT(DISTINCT al.user_id) as active_week 
                            FROM access_logs al 
                            JOIN users u ON al.user_id = u.id 
                            WHERE al.timestamp >= DATE_SUB(NOW(), INTERVAL 7 DAY) 
                            AND u.role = 'user'
                            AND al.action = 'Login effettuato'
                            AND al.user_id NOT IN (SELECT id FROM users WHERE role = 'admin')");
        $activeWeek = $stmt->fetch(PDO::FETCH_ASSOC)['active_week'];

        // Recupera data ultimo backup
        $backupDir = __DIR__ . '/../backups';
        $lastBackupDate = null;
        if (is_dir($backupDir)) {
            $latest_ctime = 0;
            $latest_filename = '';    
            $d = dir($backupDir);
            while (false !== ($entry = $d->read())) {
                $filepath = "{$backupDir}/{$entry}";
                if (is_file($filepath) && filectime($filepath) > $latest_ctime) {
                    $latest_ctime = filectime($filepath);
                    $latest_filename = $filepath;
                }
            }
            $d->close();
            if ($latest_ctime > 0) {
                $lastBackupDate = date('Y-m-d H:i:s', $latest_ctime);
            }
        }

        // Calcola spazio backup
        $backupSize = 0;
        if (is_dir($backupDir)) {
            foreach (new RecursiveIteratorIterator(new RecursiveDirectoryIterator($backupDir)) as $file) {
                if ($file->isFile()) {
                    $backupSize += $file->getSize();
                }
            }
        }
        $backupSize = round($backupSize / (1024 * 1024 * 1024), 2); // Converti in GB
        
        // Debug log
        error_log("Debug - Totale utenti: " . $totalUsers);
        error_log("Debug - Utenti attivi oggi: " . $activeToday);
        error_log("Debug - Utenti attivi settimana: " . $activeWeek);
        error_log("Debug - Ultimo backup: " . $lastBackupDate);
        error_log("Debug - Spazio backup: " . $backupSize . " GB");
        
        return [
            'total_users' => $totalUsers ?? 0,
            'active_today' => $activeToday ?? 0,
            'active_week' => $activeWeek ?? 0,
            'last_backup' => $lastBackupDate ?? date('Y-m-d H:i:s'),
            'disk_used' => $backupSize,
            'disk_total' => round(disk_total_space("/") / (1024 * 1024 * 1024), 2) // Converti in GB
        ];
    } catch (PDOException $e) {
        error_log("Errore nel recupero delle statistiche: " . $e->getMessage());
        return [
            'total_users' => 0,
            'active_today' => 0,
            'active_week' => 0,
            'last_backup' => null,
            'disk_used' => 0,
            'disk_total' => 0
        ];
    }
}

$stats = getSystemStats();

// Includi l'header admin
include '../includes/admin_header.php';
?>

<div class="admin-wrapper">
    <div class="content">
        <h1>Dashboard</h1>
        <p class="subtitle">Panoramica del sistema e statistiche</p>

        <div class="dashboard-section">
            <!-- Card Statistiche -->
            <div class="dashboard-card stats-card">
                <h2><i class="fas fa-chart-line"></i> Statistiche Sistema</h2>
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-info">
                            <span class="stat-value" id="total-users"><?php echo $stats['total_users'] ?? '0'; ?></span>
                            <span class="stat-label">Utenti Totali</span>
                        </div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-icon">
                            <i class="fas fa-user-clock"></i>
                        </div>
                        <div class="stat-info">
                            <span class="stat-value" id="active-users"><?php echo $stats['active_today'] ?? '0'; ?></span>
                            <span class="stat-label">Utenti Attivi Oggi</span>
                        </div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="stat-info">
                            <span class="stat-value" id="active-week"><?php echo $stats['active_week'] ?? '0'; ?></span>
                            <span class="stat-label">Utenti Ultimi 7 Giorni</span>
                        </div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-icon">
                            <i class="fas fa-database"></i>
                        </div>
                        <div class="stat-info">
                            <span class="stat-value" id="last-backup"><?php echo $stats['last_backup'] ? date('d/m/Y H:i', strtotime($stats['last_backup'])) : 'Mai'; ?></span>
                            <span class="stat-label">Ultimo Backup</span>
                        </div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-icon">
                            <i class="fas fa-hdd"></i>
                        </div>
                        <div class="stat-info">
                            <span class="stat-value" id="disk-space"><?php echo $stats['disk_used']; ?> GB / <?php echo $stats['disk_total']; ?> GB</span>
                            <span class="stat-label">Spazio Disco</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Card Azioni Rapide -->
            <div class="dashboard-card quick-actions-card">
                <h2><i class="fas fa-bolt"></i> Azioni Rapide</h2>
                <div class="quick-actions">
                    <a href="users.php" class="action-btn">
                        <i class="fas fa-user-plus"></i>
                        <span>Gestione Utenti</span>
                    </a>
                    <a href="backup.php" class="action-btn">
                        <i class="fas fa-database"></i>
                        <span>Backup Sistema</span>
                    </a>
                    <a href="logs.php" class="action-btn">
                        <i class="fas fa-list"></i>
                        <span>Log Sistema</span>
                    </a>
                    <a href="settings.php" class="action-btn">
                        <i class="fas fa-cog"></i>
                        <span>Impostazioni</span>
                    </a>
                </div>
            </div>

            <!-- Card Stato Sistema -->
            <div class="dashboard-card system-status-card">
                <h2><i class="fas fa-server"></i> Stato Sistema</h2>
                <div class="system-status">
                    <div class="status-item">
                        <div class="status-label">
                            <i class="fas fa-check-circle"></i>
                            <span>Database</span>
                        </div>
                        <span class="status-value online">Online</span>
                    </div>
                    <div class="status-item">
                        <div class="status-label">
                            <i class="fas fa-check-circle"></i>
                            <span>Server Web</span>
                        </div>
                        <span class="status-value online">Online</span>
                    </div>
                    <div class="status-item">
                        <div class="status-label">
                            <i class="fas fa-check-circle"></i>
                            <span>Sistema Backup</span>
                        </div>
                        <span class="status-value online">Attivo</span>
                    </div>
                    <div class="status-item">
                        <div class="status-label">
                            <i class="fas fa-check-circle"></i>
                            <span>Sistema Log</span>
                        </div>
                        <span class="status-value online">Attivo</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <?php include 'admin_footer.php'; ?>
</div>

<style>
.admin-wrapper {
    min-height: calc(100vh - 50px);
    display: flex;
    flex-direction: column;
    background-color: #121212;
}

.content {
    max-width: 1200px;
    margin: 0.5rem auto;
    padding: 0 1rem;
    height: calc(100vh - 120px);
    overflow: hidden;
    width: 100%;
    position: relative;
    background-color: #121212;
}

h1 {
    color: #FF7043;
    font-size: 1.5rem;
    margin: 0 0 0.25rem 0;
    font-weight: 500;
}

.subtitle {
    color: #BDBDBD;
    margin-bottom: 1rem;
    font-size: 0.9rem;
}

.dashboard-section {
    display: grid;
    grid-template-columns: repeat(12, 1fr);
    gap: 1rem;
    height: calc(100vh - 180px);
}

.dashboard-card {
    background: #1E1E1E;
    border-radius: 8px;
    padding: 1rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.2);
}

.stats-card {
    grid-column: span 8;
}

.quick-actions-card {
    grid-column: span 4;
}

.system-status-card {
    grid-column: span 12;
}

.dashboard-card h2 {
    color: #FF7043;
    font-size: 1.1rem;
    margin: 0 0 0.75rem 0;
    padding-bottom: 0.4rem;
    border-bottom: 2px solid #FF7043;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 1rem;
}

.stat-item {
    background: #242424;
    padding: 0.75rem;
    border-radius: 6px;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.stat-icon {
    font-size: 1.5rem;
    color: #FF7043;
}

.stat-info {
    flex: 1;
}

.stat-value {
    font-size: 1.2rem;
    font-weight: bold;
    color: #FFFFFF;
    display: block;
}

.stat-label {
    font-size: 0.8rem;
    color: #BDBDBD;
}

.quick-actions {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
}

.action-btn {
    background: #242424;
    color: #FFFFFF;
    padding: 0.75rem;
    border-radius: 6px;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
}

.action-btn:hover {
    background: #FF7043;
    transform: translateY(-2px);
}

.system-status {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.status-item {
    background: #242424;
    padding: 0.75rem;
    border-radius: 6px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.status-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
}

.status-value {
    font-size: 0.9rem;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
}

.status-value.online {
    background: #4CAF50;
    color: #FFFFFF;
}

@media (max-width: 1200px) {
    .stats-card {
        grid-column: span 12;
    }
    
    .quick-actions-card {
        grid-column: span 12;
    }
}

@media (max-width: 768px) {
    .dashboard-section {
        height: auto;
        overflow-y: auto;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .quick-actions {
        grid-template-columns: 1fr;
    }
    
    .system-status {
        grid-template-columns: 1fr;
    }
}
</style>

<script>
// Funzione per aggiornare le statistiche
async function updateStats() {
    try {
        const response = await fetch('../api/get_stats.php');
        if (!response.ok) {
            throw new Error('Errore nel recupero delle statistiche');
        }
        const data = await response.json();
        
        // Aggiorna i valori nella dashboard
        const elTotalUsers = document.getElementById('total-users');
        if (elTotalUsers) elTotalUsers.textContent = data.total_users || '0';
        const elActiveUsers = document.getElementById('active-users');
        if (elActiveUsers) elActiveUsers.textContent = data.active_today || '0';
        const elActiveWeek = document.getElementById('active-week');
        if (elActiveWeek) elActiveWeek.textContent = data.active_week || '0';
        const elLastBackup = document.getElementById('last-backup');
        if (elLastBackup) {
            elLastBackup.textContent = data.last_backup ? new Date(data.last_backup).toLocaleString('it-IT', {
                year: 'numeric', month: '2-digit', day: '2-digit',
                hour: '2-digit', minute: '2-digit'
            }) : '-';
        }
        // Formatta lo spazio disco
        const diskUsed = data.disk_used || 0;
        const diskTotal = data.disk_total || 0;
        const elDiskSpace = document.getElementById('disk-space');
        if (elDiskSpace) elDiskSpace.textContent = `${diskUsed} GB / ${diskTotal} GB`;
        
        console.log('Statistiche aggiornate:', data);
    } catch (error) {
        console.error('Errore aggiornamento statistiche:', error);
    }
}

// Gestione chiusura pagina
window.addEventListener('beforeunload', function() {
    clearInterval(statsInterval);
});

// Aggiorna le statistiche ogni 30 secondi
const statsInterval = setInterval(updateStats, 30000);

// Aggiorna le statistiche all'avvio
document.addEventListener('DOMContentLoaded', function() {
    updateStats();
});
</script>

</body>
</html>
