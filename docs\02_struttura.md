# Struttura del Progetto ASDP

## Panoramica
ASDP (Advanced Seismic Dissipator Project) è strutturato come un'applicazione web modulare che segue il pattern MVC (Model-View-Controller). L'applicazione è organizzata in cartelle specifiche per ogni tipo di funzionalità.

## Struttura delle Directory

### Core Application
- `/` - Root directory contenente i file principali dell'applicazione
  - `index.php` - Entry point dell'applicazione
  - `home.php` - Dashboard principale dopo il login
  - `.env` - File di configurazione dell'ambiente
  - `.htaccess` - Configurazione Apache
  - `composer.json` - Gestione dipendenze

### Moduli Principali
1. **Admin Module** (`/admin`)
   - Gestione utenti
   - Configurazioni sistema
   - Monitoraggio accessi
   - Gestione backup

2. **API Module** (`/api`)
   - `backup_process_zip.php` - Gestione backup (sistema ZIP)
   - `delete_backup.php` - Eliminazione backup
   - `get_backups.php` - Lista backup
   - `download_backup.php` - Download backup
   - `calculate_seismic_params.php` - Cal<PERSON>li sismici
   - `seismic_data.php` - <PERSON>ti sismici
   - `catasto_proxy.php` - Proxy catastale
   - `cadastral_data.php` - Dati catastali
   - `get_boundaries.php` - Confini geografici
   - `get_stats.php` - Statistiche sistema

3. **Authentication** (`/auth`)
   - Login/Logout
   - Registrazione
   - Recupero password
   - Gestione sessioni

4. **Core Components** (`/includes`)
   - `SeismicCalculator.php` - Engine calcoli sismici
   - `ConfigManager.php` - Gestione configurazioni
   - `db_config.php` - Database
   - `GoogleMapsGeocoder.php` - Servizi di geocoding
   - `logger.php` - Sistema logging

5. **Frontend** (`/js`, `/css`)
   - **JavaScript Modules** (`/js`)
     - `ui.js` - Core UI
     - `report.js` - Sistema report
     - `map.js` - Gestione mappe
     - `SeismicUI.js` - Interfaccia calcoli
     - `SeismicCalculator.js` - Logica calcoli
   - **CSS Styles** (`/css`)
     - Stili personalizzati
     - Temi
     - Responsive design

6. **Modulo Massa Inerziale** (`/inertial_mass`)
   - Sistema a tre livelli LLM (Deepseek → Gemma3 → Locale)
   - Interfaccia modale responsive
   - API specializzate per calcoli massa inerziale
   - Cache intelligente per ottimizzazione

7. **Resources**
   - `/img` - Immagini e icone
   - `/docs` - Documentazione completa
   - `/logs` - File di log (puliti)
   - `/sql` - Script database
   - `/cache` - Cache sistema
   - `/backups` - Directory backup

8. **Development Tools**
   - `/tools` - Strumenti sviluppo e utilità
   - `/vendor` - Dipendenze esterne (Composer)

## Funzionalità Core

### 1. Sistema di Calcolo Sismico
- Classe principale: `SeismicCalculator.php`
- Interfaccia: `SeismicUI.js`
- API: `calculate_seismic_params.php`

### 2. Gestione Mappe
- Core: `map.js`
- Geocoding: `GoogleMapsGeocoder.php`
- Dati catastali: `catasto_proxy.php`

### 3. Sistema Report
- Generazione: `report.js`
- Template HTML
- Export PDF (in sviluppo)

### 4. Gestione Utenti
- Autenticazione: `/auth`
- Profili: `account.js`
- Permessi: Admin module

## Database Structure
```sql
-- Struttura principale
users/
  |- id
  |- username
  |- email
  |- password
  |- created_at

calculations/
  |- id
  |- user_id
  |- location_data
  |- seismic_params
  |- created_at

settings/
  |- id
  |- user_id
  |- key
  |- value

logs/
  |- id
  |- user_id
  |- action
  |- details
  |- created_at
```

## Sicurezza
- Autenticazione basata su sessioni
- Password hashing (bcrypt)
- Protezione XSS
- Validazione input
- Rate limiting
- CSRF protection

## Configurazione
- File `.env` per variabili ambiente
- `ConfigManager.php` per gestione configurazioni
- Override configurazioni in admin panel

## Logging
Sistema di logging multi-livello:
- Error logging
- Access logging
- Audit logging
- Debug logging

## Backup System
- Backup database
- Backup file
- Backup configurazioni
- Report backup

## Development Workflow
1. Development locale (XAMPP)
2. Testing automatizzato
3. Code review
4. Deployment staging
5. Deployment production

## Note Importanti
- Mantenere aggiornato composer.json
- Seguire le convenzioni di codice
- Documentare le modifiche
- Mantenere il logging
- Consultare STRUTTURA_PROGETTO.md per dettagli architetturali

## Pulizia Workspace (05/06/2025)
La struttura è stata ottimizzata con la rimozione di:
- File obsoleti e duplicati
- Directory di test non utilizzate (`/src`, `/tests`)
- File di debug temporanei
- Cache e log per ambiente pulito
- Documentazione temporanea

Questa pulizia ha migliorato:
- **Performance**: Meno file da processare
- **Manutenibilità**: Struttura più chiara
- **Navigabilità**: Organizzazione ottimizzata
- **Documentazione**: Architettura completamente documentata