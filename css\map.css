.map-info-container {
    display: grid;
    grid-template-columns: 2fr 0.8fr 0.8fr;
    gap: 20px;
    height: calc(100vh - 200px);
    margin-top: 20px;
}

.map-container {
    flex: 1;
    position: relative;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    overflow: visible !important;
}

#map {
    width: 100%;
    height: 100%;
    position: relative;
}

.info-panel {
    background: rgba(255, 255, 255, 0.9);
    border-radius: 8px;
    padding: 12px;
    color: #333333;
    display: flex;
    flex-direction: column;
    gap: 8px;
    overflow: hidden;
    height: 100%;
    font-size: 0.8em;
}

.info-panel h3 {
    font-size: 1.1em;
    margin: 0;
    padding: 0;
    color: #ff8c00;
}

.info-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
    min-height: 120px;
}

.info-column {
    background: rgba(255, 255, 255, 0.9);
    border-radius: 6px;
    padding: 10px;
    border: 1px solid #ddd;
}

.params-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
}

.params-column {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.seismic-params {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 8px;
    padding: 15px;
    overflow: hidden;
    border: 1px solid #ddd;
}

.param-group {
    margin-bottom: 8px;
}

.info-row {
    display: flex;
    flex-direction: column;
    gap: 4px;
    margin-bottom: 6px;
}

.info-row:last-child {
    margin-bottom: 0;
}

.info-row label {
    color: #aaa;
    font-size: 0.9em;
}

.info-row input,
.info-row select {
    width: 100%;
    padding: 5px 8px;
    background: #ffffff;
    border: 1px solid #ddd;
    border-radius: 4px;
    color: #333333;
    font-size: 0.9em;
}

.info-row select {
    cursor: pointer;
    background-color: #ffffff;
}

.info-row select option {
    background-color: #ffffff;
    color: #333333;
    padding: 8px;
}

.info-row select:focus {
    outline: none;
    border-color: #ff8c00;
}

.info-row input:read-only {
    background: rgba(255, 255, 255, 0.05);
    color: #888;
}

.action-button {
    width: 100%;
    padding: 8px;
    background: #ff8c00;
    border: none;
    border-radius: 4px;
    color: white;
    font-size: 0.9em;
    cursor: pointer;
    transition: background 0.2s;
    margin-top: 10px;
}

.action-button:hover {
    background: #ff9d1a;
}

.seismic-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 8px;
    font-size: 0.9em;
    background: rgba(0, 0, 0, 0.2);
}

.seismic-table th,
.seismic-table td {
    padding: 4px 6px;
    text-align: center;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.seismic-table th {
    background: rgba(255, 255, 255, 0.1);
    color: #ff8c00;
    font-weight: normal;
}

h3 {
    color: #ff8c00;
    font-size: 1.1em;
    margin-bottom: 10px;
    padding-bottom: 5px;
    border-bottom: 1px solid rgba(255, 140, 0, 0.3);
}

.no-data {
    color: #888;
    font-style: italic;
    margin: 5px 0;
}

.error-popup {
    position: absolute;
    top: 10px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1000;
    width: auto;
    min-width: 300px;
    max-width: 80%;
}

.error-popup .alert {
    margin-bottom: 0;
    box-shadow: 0 2px 10px rgba(0,0,0,0.2);
    border: none;
    font-size: 14px;
    padding: 12px 20px;
    text-align: center;
}

.error-popup .btn-close {
    padding: 12px;
    margin: -12px -10px -12px auto;
}

.error-content {
    padding: 15px 20px;
    display: flex;
    align-items: center;
    gap: 15px;
}

.error-content p {
    margin: 0;
    color: #e53935;
    font-size: 14px;
}

.error-content button {
    background-color: #e53935;
    color: white;
    border: none;
    padding: 5px 10px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
}

.error-content button:hover {
    background-color: #c62828;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.map-controls {
    position: absolute;
    top: 80px !important;
    right: 10px !important;
    display: flex !important;
    flex-direction: column !important;
    gap: 8px !important;
    z-index: 99999 !important;
    pointer-events: auto !important;
    background: transparent !important;
}

.map-control-btn {
    background: rgba(255, 255, 255, 0.9) !important;
    color: #333333 !important;
    border: 1px solid #ddd !important;
    padding: 8px 15px !important;
    border-radius: 4px !important;
    cursor: pointer !important;
    font-size: 13px !important;
    transition: all 0.2s ease !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    gap: 8px !important;
    font-family: 'Segoe UI', Arial, sans-serif !important;
    opacity: 1 !important;
    visibility: visible !important;
    pointer-events: auto !important;
    min-width: 120px !important;
    white-space: nowrap !important;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
    text-transform: uppercase !important;
    font-weight: 500 !important;
    letter-spacing: 0.5px !important;
}

#catasto-toggle {
    display: flex !important;
    visibility: visible !important;
    opacity: 1 !important;
    pointer-events: auto !important;
    position: relative !important;
    z-index: 100000 !important;
}

.map-control-btn:hover {
    background: #ffffff !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1) !important;
}

.map-control-btn.active {
    background: #ff8c00 !important;
    color: #ffffff !important;
    font-weight: 600 !important;
}

.map-control-btn i {
    font-size: 14px !important;
    display: inline-block !important;
}

.map-control-btn.loading {
    position: relative !important;
    pointer-events: none !important;
    opacity: 0.7 !important;
}

.map-control-btn.loading:after {
    content: '' !important;
    position: absolute !important;
    width: 16px !important;
    height: 16px !important;
    top: 50% !important;
    left: 50% !important;
    margin: -8px 0 0 -8px !important;
    border: 2px solid rgba(255, 255, 255, 0.3) !important;
    border-radius: 50% !important;
    border-top-color: #ffffff !important;
    animation: spin 1s ease-in-out infinite !important;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}