<?php
require_once '../../includes/db_config.php';
session_start();

// Verifica accesso admin
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Accesso non autorizzato']);
    exit;
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $data = json_decode(file_get_contents('php://input'), true);
        
        if (!isset($data['id'])) {
            throw new Exception('ID utente mancante');
        }

        $conn = getConnection();
        
        // Verifica se l'utente esiste
        $stmt = $conn->prepare("SELECT id, username FROM users WHERE id = ?");
        $stmt->execute([$data['id']]);
        $currentUser = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$currentUser) {
            throw new Exception('Utente non trovato');
        }

        // Se si sta modificando l'username, verifica che non sia già in uso
        if (isset($data['username']) && $data['username'] !== $currentUser['username']) {
            $stmt = $conn->prepare("SELECT id FROM users WHERE username = ? AND id != ?");
            $stmt->execute([$data['username'], $data['id']]);
            if ($stmt->fetch()) {
                throw new Exception('Username già in uso da un altro utente');
            }
        }

        // Prepara l'array dei campi da aggiornare
        $updates = [];
        $params = [];

        if (isset($data['username']) && $data['username'] !== $currentUser['username']) {
            $updates[] = "username = ?";
            $params[] = $data['username'];
        }
        if (isset($data['nome'])) {
            $updates[] = "nome = ?";
            $params[] = $data['nome'];
        }
        if (isset($data['cognome'])) {
            $updates[] = "cognome = ?";
            $params[] = $data['cognome'];
        }
        if (isset($data['email'])) {
            $updates[] = "email = ?";
            $params[] = $data['email'];
        }
        if (isset($data['role'])) {
            // Non permettere la modifica del ruolo dell'admin principale
            if ($data['id'] == 1 && $data['role'] !== 'admin') {
                throw new Exception('Non è possibile modificare il ruolo dell\'amministratore principale');
            }
            $updates[] = "role = ?";
            $params[] = $data['role'];
        }
        if (isset($data['password']) && !empty($data['password'])) {
            $updates[] = "password = ?";
            $params[] = password_hash($data['password'], PASSWORD_DEFAULT);
        }

        if (empty($updates)) {
            throw new Exception('Nessun campo da aggiornare');
        }

        // Aggiunge l'ID alla fine dei parametri
        $params[] = $data['id'];

        // Esegue l'update
        $sql = "UPDATE users SET " . implode(", ", $updates) . " WHERE id = ?";
        $stmt = $conn->prepare($sql);
        $stmt->execute($params);

        echo json_encode(['success' => true, 'message' => 'Utente aggiornato con successo']);
    } catch (Exception $e) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
} 