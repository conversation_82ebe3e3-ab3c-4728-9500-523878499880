# 4. Database

## Struttura Database

### Tabelle di Calcolo

#### CALCULATION_PARAMETERS
```sql
CREATE TABLE calculation_parameters (
    id INT PRIMARY KEY AUTO_INCREMENT,
    calculation_id INT,
    parameter_type VARCHAR(10),
    tr INT,
    ag DECIMAL(10,6),
    f0 DECIMAL(10,6),
    tc_star DECIMAL(10,6),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (calculation_id) REFERENCES calculation_log(id)
);
```

#### CALCULATION_SPECTRA
```sql
CREATE TABLE calculation_spectra (
    id INT PRIMARY KEY AUTO_INCREMENT,
    calculation_id INT,
    parameter_type VARCHAR(10),
    spectrum_data JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (calculation_id) REFERENCES calculation_log(id)
);
```

### Tabelle Geografiche

#### SEISMIC_GRID_POINTS
```sql
CREATE TABLE seismic_grid_points (
    id INT PRIMARY KEY AUTO_INCREMENT,
    lat DECIMAL(10,6) NOT NULL,
    lng DECIMAL(10,6) NOT NULL,
    ag_value DECIMAL(10,6),
    f0_value DECIMAL(10,6),
    tc_star_value DECIMAL(10,6),
    UNIQUE KEY lat_lng (lat, lng)
);
```

#### ZONE_SISMICHE
```sql
CREATE TABLE zone_sismiche (
    id INT PRIMARY KEY AUTO_INCREMENT,
    zona VARCHAR(10) NOT NULL,
    descrizione TEXT,
    ag_min DECIMAL(10,6),
    ag_max DECIMAL(10,6)
);
```

#### CLASSIFICAZIONE_ZONE_SISMICHE
```sql
CREATE TABLE classificazione_zone_sismiche (
    id INT PRIMARY KEY AUTO_INCREMENT,
    comune_id INT NOT NULL,
    zona_id INT NOT NULL,
    data_aggiornamento DATE,
    KEY comune_id (comune_id),
    KEY zona_id (zona_id)
);
```

#### COMUNI
```sql
CREATE TABLE comuni (
    id INT PRIMARY KEY AUTO_INCREMENT,
    nome VARCHAR(100) NOT NULL,
    provincia VARCHAR(2) NOT NULL,
    regione VARCHAR(50) NOT NULL,
    lat DECIMAL(10,6),
    lng DECIMAL(10,6),
    UNIQUE KEY nome_provincia (nome, provincia)
);
```

#### CATASTO_INFO
```sql
CREATE TABLE catasto_info (
    id INT PRIMARY KEY AUTO_INCREMENT,
    comune_id INT NOT NULL,
    foglio VARCHAR(10) NOT NULL,
    particella VARCHAR(10) NOT NULL,
    sub VARCHAR(10),
    lat DECIMAL(10,6),
    lng DECIMAL(10,6),
    KEY comune_id (comune_id),
    FOREIGN KEY (comune_id) REFERENCES comuni(id)
);
```

### Tabelle Utenti

#### PASSWORD_RESETS
```sql
CREATE TABLE password_resets (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    token VARCHAR(100) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NOT NULL,
    used TINYINT(1) DEFAULT 0,
    KEY user_id (user_id),
    UNIQUE KEY token (token),
    FOREIGN KEY (user_id) REFERENCES users(id)
);
```

#### USER_SETTINGS
```sql
CREATE TABLE user_settings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    setting_key VARCHAR(50) NOT NULL,
    setting_value TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY user_setting (user_id, setting_key),
    FOREIGN KEY (user_id) REFERENCES users(id)
);
```

## Relazioni

### Relazioni Principali
1. **CALCULATION_PARAMETERS → CALCULATION_LOG**
   - `calculation_parameters.calculation_id` → `calculation_log.id`
   - Tipo: One-to-Many
   - Ogni calcolo può avere più parametri (SLO, SLD, etc.)

2. **CALCULATION_SPECTRA → CALCULATION_LOG**
   - `calculation_spectra.calculation_id` → `calculation_log.id`
   - Tipo: One-to-Many
   - Ogni calcolo può avere più spettri (SLO, SLD, etc.)

3. **CATASTO_INFO → COMUNI**
   - `catasto_info.comune_id` → `comuni.id`
   - Tipo: One-to-Many
   - Ogni record catastale appartiene a un comune

4. **CLASSIFICAZIONE_ZONE_SISMICHE → COMUNI**
   - `classificazione_zone_sismiche.comune_id` → `comuni.id`
   - Tipo: One-to-Many
   - Ogni comune può avere più classificazioni nel tempo

5. **USER_SETTINGS → USERS**
   - `user_settings.user_id` → `users.id`
   - Tipo: One-to-Many
   - Ogni utente può avere multiple impostazioni

### Indici
1. **CALCULATION_PARAMETERS**
   - `idx_calculation_id` su `calculation_id`
   - `idx_parameter_type` su `parameter_type`

2. **CALCULATION_SPECTRA**
   - `idx_calculation_id` su `calculation_id`
   - `idx_parameter_type` su `parameter_type`

3. **SEISMIC_GRID_POINTS**
   - `idx_lat_lng` su (`lat`, `lng`)

4. **COMUNI**
   - `idx_nome_provincia` su (`nome`, `provincia`)
   - `idx_coordinates` su (`lat`, `lng`)

5. **CATASTO_INFO**
   - `idx_comune_id` su `comune_id`
   - `idx_foglio_part` su (`foglio`, `particella`)

### Vincoli
1. **Integrità Referenziale**
   - ON DELETE: RESTRICT per tutte le foreign key
   - ON UPDATE: CASCADE per tutte le foreign key

2. **Unicità**
   - Token reset password
   - Coordinate griglia sismica
   - Nome e provincia dei comuni
   - Impostazioni utente

3. **Not Null**
   - Campi chiave primaria
   - Coordinate griglia sismica
   - Dati comuni (nome, provincia, regione)
   - Dati catastali (foglio, particella)

## Ottimizzazioni

### 1. Indici
```sql
-- Ottimizzazione ricerca parametri
CREATE INDEX idx_calc_params ON calculation_parameters (calculation_id, parameter_type);

-- Ottimizzazione ricerca spettri
CREATE INDEX idx_calc_spectra ON calculation_spectra (calculation_id, parameter_type);

-- Ottimizzazione ricerca geografica
CREATE INDEX idx_coordinates ON comuni (lat, lng);
CREATE INDEX idx_catasto_search ON catasto_info (comune_id, foglio, particella);
```

### 2. Partitioning
```sql
-- Partitioning per data sui calcoli
ALTER TABLE calculation_parameters
PARTITION BY RANGE (TO_DAYS(created_at)) (
    PARTITION p_2024_01 VALUES LESS THAN (TO_DAYS('2024-02-01')),
    PARTITION p_2024_02 VALUES LESS THAN (TO_DAYS('2024-03-01')),
    PARTITION p_future VALUES LESS THAN MAXVALUE
);
```

### 3. Manutenzione
```sql
-- Pulizia dati vecchi
CREATE EVENT clean_old_calculations
ON SCHEDULE EVERY 1 MONTH
DO
    DELETE FROM calculation_parameters 
    WHERE created_at < DATE_SUB(NOW(), INTERVAL 1 YEAR);
```

## Piano di Backup

### 1. Backup Giornaliero
```bash
mysqldump -u root asdp_db > /backups/daily/asdp_$(date +%Y%m%d).sql
```

### 2. Backup Incrementale
```bash
mysqlbinlog --start-datetime="2024-01-01 00:00:00" \
            --stop-datetime="2024-01-02 00:00:00" \
            /var/log/mysql/mysql-bin.* > /backups/incremental/backup.sql
```

### 3. Restore
```bash
mysql -u root asdp_db < /backups/daily/asdp_20240115.sql
``` 