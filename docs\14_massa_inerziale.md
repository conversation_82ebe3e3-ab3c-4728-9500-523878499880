# 14. <PERSON><PERSON>lo Massa Inerziale

## Panoramica

Il modulo Massa Inerziale è un componente avanzato di ASDP che permette il calcolo della massa inerziale sismica degli edifici secondo le normative NTC 2018. Il modulo integra l'intelligenza artificiale tramite Deepseek LLM per fornire calcoli precisi e analisi avanzate.

## Caratteristiche Principali

### Integrazione con ASDP
- **Interfaccia modale** integrata nell'interfaccia principale
- **Pulsante dedicato** nella sezione parametri sismici
- **Recupero automatico** dei dati sismici dal database ASDP
- **Autenticazione** tramite sistema di sessioni esistente

### Funzionalità Avanzate
- **Calcolo AI** tramite integrazione con Deepseek LLM
- **Cache intelligente** per ottimizzare le prestazioni
- **Rate limiting** per sicurezza API
- **Gestione multipiano** dinamica
- **Salvataggio risultati** nel database
- **Cronologia calcoli** per ogni utente

## Architettura del Sistema

### Struttura File
```
inertial_mass/
├── api/                    # Servizi API
│   ├── data_service.php    # Recupero dati sismici
│   ├── llm_service.php     # Integrazione Deepseek
│   └── save_results.php    # Salvataggio risultati
├── assets/                 # Risorse frontend
│   ├── css/
│   │   └── modal.css       # Stili modale
│   └── js/
│       └── modal.js        # Logica JavaScript
├── includes/               # File di supporto
│   ├── config.php          # Configurazione
│   └── utils.php           # Funzioni utilità
├── cache/                  # Cache temporanea
├── modal.php               # Componente modale
├── test_integration.php    # Pagina di test
├── README.md               # Documentazione tecnica
└── in_mass.md              # Piano di sviluppo
```

### Database

Il modulo utilizza quattro tabelle principali:

#### 1. inertial_mass_calculations
Tabella principale per i calcoli della massa inerziale:
- `id`: Identificativo univoco
- `user_id`: Riferimento all'utente
- `project_id`: Riferimento al progetto (opzionale)
- `building_data`: Dati dell'edificio (JSON)
- `seismic_data`: Parametri sismici (JSON)
- `calculation_results`: Risultati del calcolo (JSON)
- `ai_analysis`: Analisi AI (TEXT)
- `total_mass`: Massa totale calcolata
- `timestamp`: Data/ora creazione
- `updated_at`: Data/ora ultimo aggiornamento

#### 2. inertial_mass_floor_details
Dettagli per ogni piano dell'edificio:
- `id`: Identificativo univoco
- `calculation_id`: Riferimento al calcolo principale
- `floor_number`: Numero del piano
- `area`: Superficie del piano (m²)
- `height`: Altezza interpiano (m)
- `use_type`: Destinazione d'uso
- `calculated_mass`: Massa calcolata per il piano
- `load_details`: Dettagli dei carichi (JSON)

#### 3. inertial_mass_cache
Cache per ottimizzare le prestazioni:
- `id`: Identificativo univoco
- `cache_key`: Chiave univoca per il cache
- `cache_data`: Dati memorizzati (JSON)
- `expires_at`: Data/ora scadenza
- `created_at`: Data/ora creazione

#### 4. inertial_mass_api_logs
Log delle chiamate API per monitoraggio:
- `id`: Identificativo univoco
- `user_id`: Utente che ha effettuato la chiamata
- `api_endpoint`: Endpoint chiamato
- `request_data`: Dati della richiesta (JSON)
- `response_data`: Dati della risposta (JSON)
- `response_time`: Tempo di risposta (ms)
- `status`: Stato della chiamata
- `error_message`: Messaggio di errore (se presente)
- `timestamp`: Data/ora della chiamata

## Flusso di Funzionamento

### 1. Accesso al Modulo
1. L'utente accede alla sezione parametri sismici di ASDP
2. Clicca sul pulsante "Calcolo Massa Inerziale"
3. Si apre la finestra modale del modulo
4. Il sistema verifica l'autenticazione dell'utente

### 2. Recupero Dati Automatici
1. Il modulo recupera automaticamente da ASDP:
   - Coordinate geografiche (lat, lon)
   - Zona sismica
   - Categoria suolo
   - Parametri sismici (ag, F0, TC*)
   - Informazioni geografiche (comune, provincia)

### 3. Input Utente
L'utente inserisce i dati mancanti:
- **Anno di costruzione**: Per determinare la normativa applicabile
- **Numero di piani**: Configurazione dell'edificio
- **Per ogni piano**:
  - Area (m²)
  - Altezza interpiano (m)
  - Destinazione d'uso

### 4. Validazione Dati
- Controllo completezza dati
- Validazione range valori
- Verifica coerenza parametri

### 5. Calcolo AI
1. Preparazione prompt per Deepseek LLM
2. Invio richiesta con tutti i parametri
3. Elaborazione risposta AI
4. Parsing e validazione risultati

### 6. Salvataggio e Visualizzazione
1. Salvataggio risultati nel database
2. Visualizzazione risultati all'utente
3. Possibilità di esportazione
4. Aggiornamento cronologia

## Integrazione con LLM

### Configurazione Deepseek
- **API Key**: Configurata in `includes/config.php`
- **Endpoint**: `https://api.deepseek.com/v1/chat/completions`
- **Modello**: `deepseek-chat`
- **Rate Limiting**: 10 richieste/minuto per utente

### Prompt Engineering
Il prompt inviato al LLM include:
- Dati sismici della località
- Parametri strutturali dell'edificio
- Normativa di riferimento (NTC 2018)
- Richiesta di calcolo dettagliato

### Gestione Errori
- Timeout: 30 secondi
- Retry automatico: 3 tentativi
- Fallback: Calcolo semplificato locale
- Log completo degli errori

## Sicurezza e Performance

### Sicurezza
- **Autenticazione**: Verifica sessione ASDP
- **Validazione Input**: Sanitizzazione lato server
- **Rate Limiting**: Prevenzione abusi API
- **Crittografia**: HTTPS per tutte le comunicazioni
- **Log Audit**: Tracciamento completo delle operazioni

### Performance
- **Cache Intelligente**: Memorizzazione risultati frequenti
- **Compressione**: Ottimizzazione trasferimento dati
- **Lazy Loading**: Caricamento progressivo interfaccia
- **Timeout Ottimizzati**: Bilanciamento velocità/affidabilità

## Utilizzo del Modulo

### Prerequisiti
1. Accesso autenticato ad ASDP
2. Progetto con coordinate geografiche definite
3. Connessione internet attiva

### Procedura Passo-Passo

#### 1. Accesso
- Aprire ASDP e autenticarsi
- Navigare alla sezione "Parametri Sismici"
- Cliccare su "Calcolo Massa Inerziale"

#### 2. Inserimento Dati
- **Anno di costruzione**: Inserire l'anno di costruzione dell'edificio
- **Configurazione piani**: Specificare il numero di piani
- **Dettagli per piano**:
  - Area: Superficie netta del piano (m²)
  - Altezza: Altezza interpiano (m)
  - Destinazione: Selezionare dall'elenco

#### 3. Calcolo
- Cliccare "Calcola Massa Inerziale"
- Attendere l'elaborazione (10-30 secondi)
- Visualizzare i risultati

#### 4. Risultati
- **Massa totale**: Massa inerziale complessiva
- **Dettaglio per piano**: Breakdown per ogni livello
- **Analisi AI**: Commenti e raccomandazioni
- **Grafici**: Visualizzazione distribuzione masse

### Interpretazione Risultati

#### Massa Inerziale
- **Unità**: Tonnellate (t)
- **Componenti**: Permanenti + Variabili + Accidentali
- **Coefficienti**: Secondo NTC 2018

#### Analisi AI
- **Validazione**: Controllo coerenza risultati
- **Raccomandazioni**: Suggerimenti miglioramento
- **Confronti**: Benchmark con edifici simili
- **Criticità**: Evidenziazione problemi potenziali

## Manutenzione e Monitoraggio

### Log e Monitoraggio
- **Log API**: Tutte le chiamate vengono registrate
- **Performance**: Monitoraggio tempi di risposta
- **Errori**: Tracciamento e notifica problemi
- **Utilizzo**: Statistiche di utilizzo del modulo

### Backup e Recovery
- **Database**: Backup automatico con ASDP
- **Cache**: Rigenerazione automatica
- **Configurazione**: Versionamento file config

### Aggiornamenti
- **Modulo**: Aggiornamenti tramite repository
- **API LLM**: Monitoraggio versioni Deepseek
- **Database**: Script di migrazione automatici

## Bug Fix e Aggiornamenti Recenti

### v2.1.0 (Gennaio 2025) - Correzioni Critiche UI

#### 🐛 RISOLTO: Raddoppio Icone nei Risultati
**Problema**: Le icone nei titoli delle sezioni risultati apparivano duplicate
- **Sezioni interessate**: 📊 Distribuzione Forze per Piano, 🤖 Analisi AI
- **Causa tecnica**: Mancanza di pulizia del contenuto HTML esistente prima dell'inserimento di nuovi risultati
- **Impatto**: Confusione visiva nell'interfaccia utente

**Soluzione implementata**:
```javascript
// File: assets/js/modal.js - Funzione displayResults()
// PRIMA (problematico)
resultsContent.innerHTML = generateResultsHTML(results);

// DOPO (corretto)
resultsContent.innerHTML = ''; // Pulizia contenuto esistente
const newHTML = generateResultsHTML(results);
resultsContent.innerHTML = newHTML;
```

**Miglioramenti aggiuntivi**:
- Aggiunto logging dettagliato per debug (`console.log` strategici)
- Ottimizzate le icone della tabella per maggiore chiarezza
- Prevenzione completa delle duplicazioni HTML

#### 🎨 Miglioramenti Interfaccia
- **Icone tabella ottimizzate**: 🏢 PIANO, ⚖️ MASSA (T), 📏 ALTEZZA (M), ⭐ FORZA (KN)
- **Animazioni fluide**: Migliorata la transizione tra form e risultati
- **Scroll verticale**: Confermato funzionamento corretto in tutte le sezioni

- **Uniformazione Stile Pulsanti (Giugno 2025)**:
  - Per garantire coerenza visiva e un aspetto professionale, i seguenti pulsanti nel file `inertial_mass/modal.php` sono stati aggiornati per utilizzare la classe `btn btn-primary` (stile primario arancione):
    - Pulsante "Nuovo Calcolo".
    - Pulsante "+ Aggiungi Piano".
    - Pulsante "Annulla".
  - Questo assicura che tutti i pulsanti di interazione principali all'interno del modale presentino uno stile uniforme.

#### 🔧 Miglioramenti Tecnici
- **Performance rendering**: Ridotto tempo di visualizzazione risultati
- **Gestione memoria**: Prevenzione memory leak da duplicazioni HTML
- **Debug avanzato**: Sistema di logging migliorato per troubleshooting

### Test di Validazione
- ✅ **Test raddoppio icone**: Verificato risoluzione completa
- ✅ **Test scroll verticale**: Funzionamento confermato
- ✅ **Test performance**: Rendering ottimizzato
- ✅ **Test cross-browser**: Compatibilità mantenuta

## Risoluzione Problemi

### Problemi Comuni

#### 1. Errore di Connessione API
**Sintomi**: Messaggio "Errore di connessione al servizio AI"
**Cause**:
- Connessione internet assente
- API key non valida
- Servizio Deepseek non disponibile
**Soluzioni**:
- Verificare connessione internet
- Controllare configurazione API key
- Riprovare dopo alcuni minuti

#### 2. Calcolo Non Completato
**Sintomi**: Calcolo si interrompe senza risultati
**Cause**:
- Dati input non validi
- Timeout API
- Errore interno del modulo
**Soluzioni**:
- Verificare completezza dati input
- Controllare log errori
- Contattare supporto tecnico

#### 3. Risultati Incoerenti
**Sintomi**: Valori di massa irrealistici
**Cause**:
- Errori nei dati input
- Problemi nell'elaborazione AI
- Bug nel parsing risultati
**Soluzioni**:
- Ricontrollare dati inseriti
- Ripetere il calcolo
- Confrontare con calcoli manuali

#### 4. Icone Duplicate nei Risultati (RISOLTO v2.1.0)
**Sintomi**: Icone doppie nei titoli delle sezioni
**Causa**: Problema di pulizia HTML risolto
**Soluzione**: Aggiornare alla versione 2.1.0 o successiva

### Supporto Tecnico
- **Log**: Consultare `/logs/app.log` per errori
- **Database**: Verificare tabelle massa inerziale
- **API**: Controllare log chiamate in `inertial_mass_api_logs`
- **Cache**: Pulire cache se necessario

## Sviluppi Futuri

### Funzionalità Pianificate
- **Export PDF**: Generazione report dettagliati
- **Visualizzazione 3D**: Modello tridimensionale edificio
- **Multi-LLM**: Integrazione con OpenAI e Claude
- **Calcolo Vulnerabilità**: Analisi sismica avanzata
- **Mobile App**: Versione per dispositivi mobili

### Miglioramenti Tecnici
- **Performance**: Ottimizzazione algoritmi
- **UI/UX**: Interfaccia più intuitiva
- **API**: Endpoint REST completi
- **Integrazione**: Connessione con altri moduli ASDP

## Conclusioni

Il modulo Massa Inerziale rappresenta un'evoluzione significativa di ASDP, introducendo capacità di calcolo avanzate tramite intelligenza artificiale. L'integrazione con Deepseek LLM permette analisi sofisticate mantenendo semplicità d'uso per l'utente finale.

La struttura modulare e l'architettura scalabile garantiscono facilità di manutenzione e possibilità di espansione futura. Il sistema di cache e rate limiting assicura prestazioni ottimali e sicurezza nell'utilizzo.

Il modulo è ora completamente operativo e integrato nell'ecosistema ASDP, pronto per l'utilizzo in produzione con supporto completo per il calcolo della massa inerziale secondo le normative vigenti.