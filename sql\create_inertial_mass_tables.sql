-- create_inertial_mass_tables.sql
-- Script per la creazione delle tabelle del modulo Massa Inerziale
-- Path: /sql/create_inertial_mass_tables.sql

-- Tabella principale per i calcoli di massa inerziale
CREATE TABLE IF NOT EXISTS `inertial_mass_calculations` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `calculation_id` VARCHAR(50) UNIQUE NOT NULL,
    `user_id` INT NOT NULL,
    `project_id` INT DEFAULT NULL,
    `input_data` JSON NOT NULL,
    `results` JSON NOT NULL,
    `seismic_data` JSON DEFAULT NULL,
    `structural_data` JSON DEFAULT NULL,
    `status` ENUM('pending', 'completed', 'error') DEFAULT 'pending',
    `error_message` TEXT DEFAULT NULL,
    `processing_time` DECIMAL(8,3) DEFAULT NULL,
    `api_calls_count` INT DEFAULT 1,
    `cache_hit` BOOLEAN DEFAULT FALSE,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
    INDEX `idx_user_id` (`user_id`),
    INDEX `idx_calculation_id` (`calculation_id`),
    INDEX `idx_created_at` (`created_at`),
    INDEX `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Tabella per i dettagli per piano
CREATE TABLE IF NOT EXISTS `inertial_mass_floor_details` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `calculation_id` INT NOT NULL,
    `floor_level` INT NOT NULL,
    `floor_name` VARCHAR(50) DEFAULT NULL,
    `mass` DECIMAL(12,3) NOT NULL,
    `height` DECIMAL(8,3) NOT NULL,
    `force` DECIMAL(12,3) NOT NULL,
    `displacement` DECIMAL(8,4) DEFAULT NULL,
    `acceleration` DECIMAL(8,4) DEFAULT NULL,
    `additional_data` JSON DEFAULT NULL,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (`calculation_id`) REFERENCES `inertial_mass_calculations`(`id`) ON DELETE CASCADE,
    INDEX `idx_calculation_id` (`calculation_id`),
    INDEX `idx_floor_level` (`floor_level`),
    UNIQUE KEY `unique_calculation_floor` (`calculation_id`, `floor_level`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Tabella per la cache delle richieste LLM
CREATE TABLE IF NOT EXISTS `inertial_mass_cache` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `cache_key` VARCHAR(64) UNIQUE NOT NULL,
    `input_hash` VARCHAR(64) NOT NULL,
    `response_data` JSON NOT NULL,
    `hit_count` INT DEFAULT 1,
    `last_accessed` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `expires_at` TIMESTAMP NULL,
    INDEX `idx_cache_key` (`cache_key`),
    INDEX `idx_input_hash` (`input_hash`),
    INDEX `idx_expires_at` (`expires_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Tabella per il log delle chiamate API
CREATE TABLE IF NOT EXISTS `inertial_mass_api_logs` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `user_id` INT NOT NULL,
    `calculation_id` VARCHAR(50) DEFAULT NULL,
    `api_endpoint` VARCHAR(100) NOT NULL,
    `request_data` JSON DEFAULT NULL,
    `response_data` JSON DEFAULT NULL,
    `response_time` DECIMAL(8,3) DEFAULT NULL,
    `status_code` INT DEFAULT NULL,
    `error_message` TEXT DEFAULT NULL,
    `ip_address` VARCHAR(45) DEFAULT NULL,
    `user_agent` TEXT DEFAULT NULL,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
    INDEX `idx_user_id` (`user_id`),
    INDEX `idx_calculation_id` (`calculation_id`),
    INDEX `idx_created_at` (`created_at`),
    INDEX `idx_api_endpoint` (`api_endpoint`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Inserimento dati di esempio per le tipologie strutturali
INSERT IGNORE INTO `inertial_mass_cache` (`cache_key`, `input_hash`, `response_data`, `expires_at`) VALUES
('structural_types', MD5('structural_types'), JSON_OBJECT(
    'residential', JSON_OBJECT('density', 1.8, 'description', 'Edificio residenziale'),
    'office', JSON_OBJECT('density', 2.0, 'description', 'Edificio per uffici'),
    'commercial', JSON_OBJECT('density', 2.5, 'description', 'Edificio commerciale'),
    'industrial', JSON_OBJECT('density', 3.0, 'description', 'Edificio industriale'),
    'school', JSON_OBJECT('density', 2.2, 'description', 'Edificio scolastico'),
    'hospital', JSON_OBJECT('density', 2.8, 'description', 'Edificio ospedaliero')
), DATE_ADD(NOW(), INTERVAL 1 YEAR));

-- Commenti per la documentazione
/*
STRUTTURA DATABASE MODULO MASSA INERZIALE

1. inertial_mass_calculations:
   - Tabella principale per memorizzare i calcoli
   - Contiene dati di input, risultati e metadati
   - Collegata agli utenti tramite foreign key
   - Supporta progetti opzionali

2. inertial_mass_floor_details:
   - Dettagli specifici per ogni piano
   - Collegata ai calcoli tramite foreign key
   - Memorizza massa, altezza, forza per piano

3. inertial_mass_cache:
   - Cache per le risposte LLM
   - Riduce chiamate API duplicate
   - Gestione automatica scadenza

4. inertial_mass_api_logs:
   - Log completo delle chiamate API
   - Monitoraggio performance e errori
   - Tracciabilità per debugging
*/