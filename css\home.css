.top-bar {
    background-color: #1E1E1E;
    padding: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.logo-container {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.logo-container img {
    width: 40px;
    height: auto;
}

.app-title {
    color: #FFFFFF;
    font-size: 1.2rem;
    font-weight: 500;
    margin: 0;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.user-avatar {
    width: 36px;
    height: 36px;
    background-color: #FF7043;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: normal;
    color: white;
}

.username {
    color: #FFFFFF;
    font-size: 0.9rem;
}

.logout-btn-top {
    background-color: transparent;
    color: #FF7043;
    border: 2px solid #FF7043;
    border-radius: 4px;
    padding: 0.5rem 1rem;
    cursor: pointer;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
    transition: all 0.2s ease;
}

.logout-btn-top:hover {
    background-color: #FF7043;
    color: #FFFFFF;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.logout-btn-top i {
    font-size: 1rem;
}

.sidebar {
    background-color: #1E1E1E;
    width: 70px;
    left: 0;
    position: fixed;
    top: 70px;
    bottom: 0;
    padding: 1rem 0;
    box-shadow: 2px 0 4px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    overflow: hidden;
    border-right: 1px solid #333333;
}

.nav-menu {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    padding: 0.5rem;
}

.nav-item {
    position: relative;
    color: #FFFFFF;
    text-decoration: none;
    padding: 0.75rem;
    min-width: 70px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
}

.nav-text, 
.nav-badge,
.nav-section {
    display: none;
}

.nav-icon {
    width: 24px;
    height: 24px;
    stroke: #888888;
    flex-shrink: 0;
    transition: all 0.2s ease;
    cursor: pointer;
}

.main-content {
    margin-left: 70px;
    padding: 2rem;
    height: calc(100vh - 70px);
    overflow: hidden;
}

.footer {
    left: 70px;
}

.main-content.sidebar-open {
    margin-left: 250px;
}

h1 {
    color: #FFFFFF;
    font-size: 1.5rem;
    margin-bottom: 1.5rem;
    font-weight: 500;
}

.search-container {
    margin: 1rem 0;
    margin-left: 0;
}

.search-box-container {
    position: relative;
    max-width: 800px;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.search-label {
    color: #888888;
    margin-bottom: 1rem;
    font-size: 0.9rem;
    display: block;
}

.search-box {
    width: 100%;
    padding: 0.75rem;
    background-color: #1E1E1E;
    border: 1px solid #333333;
    border-radius: 4px;
    color: #FFFFFF;
    font-size: 1rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.search-box::placeholder {
    color: #888888;
    opacity: 1;
}

.search-box:focus {
    outline: none;
    border-color: #FF7043;
    box-shadow: 0 0 0 2px rgba(255, 112, 67, 0.2);
}

.clear-search {
    background: none;
    border: none;
    color: #FF7043;
    cursor: pointer;
    padding: 8px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.clear-search:hover {
    background-color: rgba(255, 112, 67, 0.1);
    transform: scale(1.1);
}

.clear-search svg {
    width: 20px;
    height: 20px;
    stroke-width: 2.5;
}

.content-grid {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr;
    gap: 1rem;
    height: calc(100vh - 220px);
    margin-top: 1rem;
    overflow: hidden;
    padding-bottom: 3.125rem;
}

@media (max-width: 1400px) {
    .content-grid {
        height: calc(100vh - 200px);
        padding-bottom: 3.125rem;
    }
}

@media (max-width: 992px) {
    .content-grid {
        grid-template-columns: 1fr;
        height: auto;
        gap: 1rem;
        overflow-y: auto;
    }

    .map-container {
        height: 400px;
    }

    .info-panel {
        height: 400px;
    }

    .main-content {
        height: auto;
        overflow-y: auto;
    }

    .app-title {
        font-size: 1rem;
    }

    .logo-container {
        gap: 0.5rem;
    }

    .search-box-container {
        max-width: 100%;
    }
}

@media (max-width: 768px) {
    .app-title {
        display: none;
    }

    .sidebar-toggle {
        top: 78px;
        padding: 8px 4px;
    }
    
    .toggle-icon {
        width: 16px;
        height: 16px;
    }
}

.map-container {
    position: relative;
    height: 100%;
    border-radius: 4px;
    overflow: hidden;
}

#map {
    width: 100%;
    height: 100%;
    border-radius: 4px;
}

#leaflet-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.info-panel {
    background-color: #1E1E1E;
    border-radius: 8px;
    padding: 1rem;
    height: 100%;
    overflow-y: auto;
    font-size: 0.9rem;
    scrollbar-width: thin;
    scrollbar-color: #FF7043 #1E1E1E;
}

.info-panel::-webkit-scrollbar {
    width: 6px;
}

.info-panel::-webkit-scrollbar-track {
    background: #1E1E1E;
    border-radius: 3px;
}

.info-panel::-webkit-scrollbar-thumb {
    background-color: #FF7043;
    border-radius: 3px;
}

.info-row {
    margin-bottom: 0.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #333333;
}

.info-row:last-child {
    border-bottom: none;
}

.info-row label {
    color: #888888;
    font-size: 0.8rem;
    margin-bottom: 0.25rem;
    display: block;
}

.info-row div {
    color: #FFFFFF;
    font-size: 0.9rem;
}

.description div {
    font-size: 0.8rem;
    line-height: 1.3;
    color: #CCCCCC;
}

.seismic-params {
    margin-top: 1rem;
}

.seismic-params h3 {
    color: #FF7043;
    font-size: 1rem;
    margin-bottom: 0.75rem;
    padding-bottom: 0.25rem;
    border-bottom: 2px solid #FF7043;
}

.param-group {
    margin-bottom: 0.75rem;
    padding: 0.75rem;
    background-color: #2A2A2A;
    border-radius: 6px;
    border: 1px solid #333333;
}

.param-group h4 {
    color: #CCCCCC;
    font-size: 0.8rem;
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.param-row {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 0.5rem;
}

.param {
    text-align: center;
}

.param label {
    display: block;
    color: #888888;
    font-size: 0.7rem;
    margin-bottom: 0.2rem;
}

.param span {
    color: #FF7043;
    font-size: 0.9rem;
    font-weight: 500;
    background-color: #242424;
    padding: 0.2rem 0.5rem;
    border-radius: 3px;
    display: inline-block;
}

@media (max-width: 992px) {
    .param-row {
        grid-template-columns: repeat(2, 1fr);
    }
}

.info-row input[type="text"] {
    width: 100%;
    background-color: #2A2A2A;
    border: 1px solid #333333;
    color: #FFFFFF;
    padding: 0.5rem;
    border-radius: 4px;
    font-size: 0.9rem;
}

.info-row input[type="text"]:disabled,
.info-row input[type="text"][readonly] {
    background-color: #2A2A2A;
    color: #CCCCCC;
    cursor: not-allowed;
}

.seismic-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 1rem;
    background-color: #2A2A2A;
    border-radius: 4px;
    overflow: hidden;
}

.seismic-table th,
.seismic-table td {
    padding: 0.5rem;
    text-align: center;
    border: 1px solid #333333;
}

.seismic-table th {
    background-color: #1E1E1E;
    color: #FF7043;
    font-weight: 500;
}

.seismic-table td {
    color: #FFFFFF;
}

.altitude-container {
    position: relative;
    display: flex;
    align-items: center;
}

.altitude-container input {
    padding-right: 2rem;
}

.altitude-container .unit {
    position: absolute;
    right: 0.5rem;
    color: #888888;
    font-size: 0.8rem;
}

/* Stile unificato per i tooltip */
.nav-tooltip {
    position: fixed;
    left: 80px;
    top: auto;
    transform: none;
    background: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 14px;
    white-space: nowrap;
    pointer-events: none;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 9999;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

/* Freccia del tooltip */
.nav-tooltip::before {
    content: '';
    position: absolute;
    left: -6px;
    top: 50%;
    transform: translateY(-50%);
    border-width: 6px 6px 6px 0;
    border-style: solid;
    border-color: transparent rgba(0, 0, 0, 0.9) transparent transparent;
}

/* Mostra tooltip al passaggio del mouse */
.nav-item:hover .nav-tooltip {
    opacity: 1;
    visibility: visible;
}

/* Stile specifico per tooltip di elementi disabilitati */
.nav-item.disabled .nav-tooltip {
    background: #dc3545;
    max-width: 250px;
}

.nav-item.disabled .nav-tooltip::before {
    border-right-color: #dc3545;
}

/* Stile per elementi disabilitati */
.nav-item.disabled {
    opacity: 0.6;
    cursor: not-allowed;
    pointer-events: all;
}

.nav-item.disabled:hover .nav-icon {
    stroke: #888888;
    transform: none;
}

.nav-icon-container {
    display: flex;
    align-items: center;
}

.cadastral-data,
.location-data {
    margin-top: 1.5rem;
}

.cadastral-data h3,
.location-data h3 {
    color: #FF7043;
    font-size: 1rem;
    margin-bottom: 0.75rem;
    padding-bottom: 0.25rem;
    border-bottom: 2px solid #FF7043;
}

.location-data .info-row {
    margin-bottom: 0.75rem;
    padding: 0.5rem;
    background-color: #2A2A2A;
    border-radius: 6px;
    border: 1px solid #333333;
}

.location-data .info-row div {
    color: #FF7043;
    font-size: 0.9rem;
    font-weight: 500;
    background-color: #242424;
    padding: 0.4rem 0.75rem;
    border-radius: 3px;
    display: inline-block;
    width: 100%;
}

.location-data .description div {
    font-size: 0.85rem;
    line-height: 1.4;
    color: #CCCCCC;
    font-weight: normal;
}

@media (max-width: 992px) {
    .param-row {
        grid-template-columns: repeat(2, 1fr);
    }
}

.info-panel h3 {
    color: #FF7043;
    font-size: 1rem;
    margin-bottom: 0.75rem;
    padding-bottom: 0.25rem;
    border-bottom: 2px solid #FF7043;
}

/* Stili per il footer */
.footer {
    position: fixed;
    bottom: 0;
    left: 70px;
    right: 0;
    background-color: #1E1E1E;
    padding: 0.5rem 1rem;
    font-size: 0.75rem;
    color: #888888;
    border-top: 1px solid #333333;
    transition: left 0.3s ease;
    z-index: 999;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.footer.sidebar-open {
    left: 70px;
}

.footer-links {
    display: flex;
    gap: 1rem;
}

.footer a {
    color: #FF7043;
    text-decoration: none;
    transition: color 0.2s ease;
}

.footer a:hover {
    color: #F4511E;
}

.footer-divider {
    color: #444444;
    margin: 0 0.5rem;
}

@media (max-width: 992px) {
    .footer {
        flex-direction: column;
        gap: 0.5rem;
        padding: 0.75rem;
        text-align: center;
    }

    .footer-links {
        flex-wrap: wrap;
        justify-content: center;
    }
}

/* Aggiusto il padding del content per il footer */
.main-content {
    padding-bottom: 3rem;
}

/* Stili base per i popup */
.popup {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    justify-content: center;
    align-items: center;
}

.popup-content {
    background-color: #1E1E1E;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.2);
    position: relative;
    width: 90%;
    max-width: 600px;
}

.popup-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    border-bottom: 1px solid #333;
}

.close-btn {
    background: none;
    border: none;
    color: #888888;
    cursor: pointer;
    font-size: 1.2rem;
    padding: 0.5rem;
    transition: color 0.2s ease;
}

.close-btn:hover {
    color: #FF7043;
}

.recalculate-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px 20px;
    background-color: #2196F3;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.recalculate-btn:hover {
    background-color: #1976D2;
}

.recalculate-btn:disabled {
    background-color: #BDBDBD;
    cursor: not-allowed;
}

.recalculate-btn.loading {
    position: relative;
    padding-right: 40px;
}

.recalculate-btn.loading::after {
    content: '';
    position: absolute;
    right: 10px;
    width: 20px;
    height: 20px;
    border: 2px solid #FFF;
    border-top-color: transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.recalculate-btn.success {
    background-color: #4CAF50;
}

.recalculate-btn.error {
    background-color: #F44336;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Stile hover per le icone attive */
.nav-item:not(.disabled):hover .nav-icon,
.nav-item.active .nav-icon {
    stroke: #FF7043;
    transform: scale(1.1);
}

.nav-item:not(.disabled):hover,
.nav-item.active {
    background-color: rgba(255, 112, 67, 0.1);
}

/* Stile per il testo al passaggio del mouse */
.nav-item:not(.disabled):hover .nav-text,
.nav-item.active .nav-text {
    color: #FF7043;
}