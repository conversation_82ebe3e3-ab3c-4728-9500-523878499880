<?php
require_once '../includes/db_config.php';

function executeSQL($conn, $sql) {
    try {
        $conn->exec($sql);
        echo "Query eseguita con successo: " . $sql . "<br>";
        return true;
    } catch (PDOException $e) {
        echo "Errore nell'esecuzione della query: " . $sql . "<br>";
        echo "Errore: " . $e->getMessage() . "<br>";
        return false;
    }
}

try {
    echo "<h2>Verifica e correzione del database</h2>";

    // 1. Verifica tabella users
    $sql = "SHOW TABLES LIKE 'users'";
    $result = $conn->query($sql);
    if ($result->rowCount() == 0) {
        echo "Tabella users non trovata. Creazione...<br>";
        $sql = "CREATE TABLE users (
            id INT AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(50) UNIQUE NOT NULL,
            password VARCHAR(255) NOT NULL,
            email VARCHAR(100) UNIQUE NOT NULL,
            nome VARCHAR(50) NOT NULL,
            cognome VARCHAR(50) NOT NULL,
            role ENUM('user', 'admin') NOT NULL DEFAULT 'user',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )";
        executeSQL($conn, $sql);
    } else {
        echo "Tabella users trovata.<br>";
    }

    // 2. Verifica colonna role
    $sql = "SHOW COLUMNS FROM users LIKE 'role'";
    $result = $conn->query($sql);
    if ($result->rowCount() == 0) {
        echo "Colonna role non trovata. Aggiunta...<br>";
        $sql = "ALTER TABLE users ADD COLUMN role ENUM('user', 'admin') NOT NULL DEFAULT 'user'";
        executeSQL($conn, $sql);
    } else {
        echo "Colonna role trovata.<br>";
    }

    // 3. Verifica utente admin
    $stmt = $conn->prepare("SELECT * FROM users WHERE username = ?");
    $stmt->execute(['admin']);
    $admin = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$admin) {
        echo "Utente admin non trovato. Creazione...<br>";
        $hashedPassword = password_hash('admin123', PASSWORD_DEFAULT);
        $stmt = $conn->prepare("INSERT INTO users (username, password, email, nome, cognome, role) VALUES (?, ?, ?, ?, ?, ?)");
        $stmt->execute(['admin', $hashedPassword, '<EMAIL>', 'Admin', 'ASDP', 'admin']);
        echo "Utente admin creato.<br>";
    } else {
        echo "Utente admin trovato. Verifica ruolo...<br>";
        if ($admin['role'] !== 'admin') {
            $stmt = $conn->prepare("UPDATE users SET role = 'admin' WHERE username = ?");
            $stmt->execute(['admin']);
            echo "Ruolo admin aggiornato.<br>";
        } else {
            echo "Ruolo admin corretto.<br>";
        }
    }

    // 4. Verifica tabella access_logs
    $sql = "SHOW TABLES LIKE 'access_logs'";
    $result = $conn->query($sql);
    if ($result->rowCount() == 0) {
        echo "Tabella access_logs non trovata. Creazione...<br>";
        $sql = "CREATE TABLE access_logs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT,
            action VARCHAR(255) NOT NULL,
            ip_address VARCHAR(45),
            user_agent TEXT,
            timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
        )";
        executeSQL($conn, $sql);
    } else {
        echo "Tabella access_logs trovata.<br>";
    }

    // 5. Mostra utenti e ruoli
    echo "<h3>Utenti nel database:</h3>";
    $stmt = $conn->query("SELECT username, role FROM users");
    while ($user = $stmt->fetch(PDO::FETCH_ASSOC)) {
        echo "Username: " . htmlspecialchars($user['username']) . " - Ruolo: " . htmlspecialchars($user['role']) . "<br>";
    }

    echo "<br><strong>Processo completato.</strong><br>";
    echo "Puoi ora accedere con:<br>";
    echo "Username: admin<br>";
    echo "Password: admin123<br>";

} catch (PDOException $e) {
    echo "Errore nel database: " . $e->getMessage();
}
?>
