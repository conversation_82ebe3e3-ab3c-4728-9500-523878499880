.help-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.7);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 2000;
}

.help-overlay.active {
    display: flex;
}

.help-popup {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: #1E1E1E;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    width: 90%;
    max-width: 800px;
    max-height: 90vh;
    overflow-y: auto;
    z-index: 1000;
    scrollbar-width: thin;
    scrollbar-color: #FF7043 #1E1E1E;
}

.help-popup::-webkit-scrollbar {
    width: 6px;
}

.help-popup::-webkit-scrollbar-track {
    background: #1E1E1E;
    border-radius: 3px;
}

.help-popup::-webkit-scrollbar-thumb {
    background-color: #FF7043;
    border-radius: 3px;
}

.help-header {
    background-color: #2A2A2A;
    padding: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #333333;
}

.help-header h2 {
    color: #FF7043;
    font-size: 1.2rem;
    margin: 0;
}

.close-help {
    background: none;
    border: none;
    color: #888888;
    cursor: pointer;
    padding: 0.5rem;
    transition: color 0.2s ease;
}

.close-help:hover {
    color: #FF7043;
}

.close-help svg {
    width: 20px;
    height: 20px;
}

.help-content {
    padding: 1.5rem;
    overflow-y: auto;
    flex: 1;
}

.help-section {
    margin-bottom: 2rem;
}

.help-section:last-child {
    margin-bottom: 0;
}

.help-section h3 {
    color: #FF7043;
    font-size: 1.1rem;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #333333;
}

.help-section ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.help-section li {
    color: #FFFFFF;
    margin-bottom: 0.75rem;
    padding-left: 1.5rem;
    position: relative;
    line-height: 1.4;
}

.help-section li::before {
    content: "•";
    color: #FF7043;
    position: absolute;
    left: 0;
    font-size: 1.2rem;
}

.help-section p {
    color: #CCCCCC;
    margin-bottom: 1rem;
    line-height: 1.4;
}

@media (max-width: 768px) {
    .help-popup {
        width: 95%;
        max-height: 95vh;
    }

    .help-header h2 {
        font-size: 1.1rem;
    }

    .help-section h3 {
        font-size: 1rem;
    }

    .help-content {
        padding: 1rem;
    }
} 