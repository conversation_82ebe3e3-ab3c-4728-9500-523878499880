<?php
session_start();

// Imposta gli header CORS se necessario
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// Gestisci le richieste OPTIONS per CORS
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Verifica se la richiesta è POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    header('Content-Type: application/json');
    echo json_encode([
        'success' => false,
        'message' => 'Metodo non consentito. Utilizzare POST.'
    ]);
    exit;
}

try {
    // Distruggi tutte le variabili di sessione
    $_SESSION = array();

    // Distruggi il cookie di sessione se esiste
    if (isset($_COOKIE[session_name()])) {
        setcookie(session_name(), '', time()-3600, '/');
    }

    // Distruggi la sessione
    session_destroy();

    // Verifica che la sessione sia stata effettivamente distrutta
    if (session_status() === PHP_SESSION_NONE || empty($_SESSION)) {
        http_response_code(200);
        header('Content-Type: application/json');
        echo json_encode([
            'success' => true,
            'message' => 'Logout effettuato con successo'
        ]);
    } else {
        throw new Exception('Errore nella distruzione della sessione');
    }
} catch (Exception $e) {
    error_log("Errore durante il logout: " . $e->getMessage());
    http_response_code(500);
    header('Content-Type: application/json');
    echo json_encode([
        'success' => false,
        'message' => 'Errore durante il logout: ' . $e->getMessage()
    ]);
}
?>