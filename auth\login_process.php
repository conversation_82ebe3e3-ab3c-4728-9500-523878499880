<?php
header('Content-Type: application/json');
error_reporting(E_ALL);
ini_set('display_errors', 0);

// Verifica il percorso del file
if (!file_exists('../includes/db_config.php')) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'File di configurazione non trovato',
        'debug' => 'Percorso: ' . realpath('../includes/db_config.php')
    ]);
    exit;
}

try {
    require_once '../includes/db_config.php';
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Errore nel caricamento della configurazione',
        'debug' => $e->getMessage()
    ]);
    exit;
}

session_start();

try {
    // Verifica che la richiesta sia POST e in formato JSON
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Metodo non consentito');
    }

    // Leggi il JSON dal body della richiesta
    $json = file_get_contents('php://input');
    $data = json_decode($json, true);

    if (!$data) {
        throw new Exception('Dati non validi');
    }

    // Estrai username e password
    $username = $data['username'] ?? '';
    $password = $data['password'] ?? '';

    if (empty($username) || empty($password)) {
        throw new Exception('Username e password sono richiesti');
    }

    // Query al database con log
    $stmt = $conn->prepare("SELECT id, password, nome, cognome FROM users WHERE username = ?");
    $stmt->execute([$username]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$user) {
        throw new Exception('Utente non trovato');
    }

    if (!password_verify($password, $user['password'])) {
        throw new Exception('Password non valida');
    }

    // Login successful
    $_SESSION['user_id'] = $user['id'];
    $_SESSION['nome'] = $user['nome'];
    $_SESSION['cognome'] = $user['cognome'];
    $_SESSION['username'] = $username;

    echo json_encode([
        'success' => true,
        'message' => 'Login effettuato con successo',
        'redirect' => 'home.php'
    ]);

} catch (PDOException $e) {
    error_log("Errore database: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Errore del server. Riprova più tardi.',
        'debug' => $e->getMessage()
    ]);
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}