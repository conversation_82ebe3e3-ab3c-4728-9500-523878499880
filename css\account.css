.popup-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.7);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 2000;
}

.popup-content {
    background: #1E1E1E;
    border-radius: 8px;
    width: 90%;
    max-width: 800px;
    max-height: 90vh;
    overflow-y: auto;
    position: relative;
    scrollbar-width: thin;
    scrollbar-color: #FF7043 #1E1E1E;
}

.popup-content::-webkit-scrollbar {
    width: 6px;
}

.popup-content::-webkit-scrollbar-track {
    background: #1E1E1E;
    border-radius: 3px;
}

.popup-content::-webkit-scrollbar-thumb {
    background-color: #FF7043;
    border-radius: 3px;
}

.popup-header {
    background-color: #2A2A2A;
    padding: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #333333;
}

.popup-header h2 {
    color: #FF7043;
    font-size: 1.2rem;
    margin: 0;
}

.close-btn {
    background: none;
    border: none;
    color: #888888;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: color 0.2s ease;
}

.close-btn:hover {
    color: #FF7043;
}

.popup-body {
    padding: 1.5rem;
}

.account-section {
    margin-bottom: 2rem;
    padding: 1rem;
    background-color: #2A2A2A;
    border-radius: 8px;
    border: 1px solid #333333;
}

.account-section:last-child {
    margin-bottom: 0;
}

.account-section h3 {
    color: #FF7043;
    font-size: 1.1rem;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #333333;
}

.form-group {
    margin-bottom: 1rem;
}

.form-group label {
    display: block;
    color: #888888;
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
}

.form-group input {
    width: 100%;
    padding: 0.75rem;
    background-color: #1E1E1E;
    border: 1px solid #333333;
    border-radius: 4px;
    color: #FFFFFF;
    font-size: 0.9rem;
}

.form-group input:read-only {
    background-color: #242424;
    cursor: not-allowed;
}

.btn-primary {
    background-color: #FF7043;
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: background-color 0.2s ease;
}

.btn-primary:hover {
    background-color: #F4511E;
}

.history-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 1rem;
}

.history-table th,
.history-table td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid #333333;
}

.history-table th {
    color: #888888;
    font-weight: normal;
    font-size: 0.8rem;
}

.history-table td {
    color: #FFFFFF;
    font-size: 0.9rem;
}

.loading {
    color: #888888;
    text-align: center;
    padding: 1rem;
}

.error {
    color: #ff5252;
    text-align: center;
    padding: 1rem;
}

@media (max-width: 768px) {
    .popup-content {
        width: 95%;
        max-height: 95vh;
    }

    .popup-body {
        padding: 1rem;
    }

    .account-section {
        padding: 0.75rem;
    }

    .history-table {
        font-size: 0.8rem;
    }
} 