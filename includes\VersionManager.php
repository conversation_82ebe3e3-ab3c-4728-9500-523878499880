<?php

class VersionManager {
    private static $instance = null;
    private $version = '';
    private $updateFile = __DIR__ . '/../docs/12_aggiornamenti.md';
    private $cacheFile = __DIR__ . '/../cache/version.txt';
    private $fallbackCacheFile = __DIR__ . '/cache/version_fallback.txt';
    private $cacheDuration = 3600; // 1 ora in secondi
    private $logger;

    private function __construct() {
        // Inizializza il logger
        require_once __DIR__ . '/logger.php';
        $this->logger = Logger::getInstance();
        $this->loadVersion();
    }

    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    private function loadVersion() {
        try {
            // Prova a caricare dal cache principale
            if ($this->loadFromCache()) {
                $this->logger->info('Versione caricata dalla cache principale');
                return;
            }

            // Prova a caricare dal cache di fallback
            if ($this->loadFromFallbackCache()) {
                $this->logger->info('Versione caricata dalla cache di fallback');
                return;
            }

            // Se entrambe le cache falliscono, carica dal file
            if (!file_exists($this->updateFile)) {
                $this->version = 'v1.0.0';
                $this->logger->warning('File aggiornamenti non trovato, usando versione di default');
            } else {
                $content = file_get_contents($this->updateFile);
                if (preg_match('/^## Versione\s+([0-9.]+)\s+\(/m', $content, $matches)) {
                    $this->version = 'v' . $matches[1];
                    $this->logger->info('Versione caricata dal file aggiornamenti: ' . $this->version);
                } else {
                    $this->version = 'v1.0.0';
                    $this->logger->warning('Pattern versione non trovato, usando versione di default');
                }
            }

            // Salva in entrambe le cache
            $this->saveToCache();
            $this->saveToFallbackCache();

        } catch (Exception $e) {
            $this->logger->error('Errore nel caricamento della versione: ' . $e->getMessage());
            $this->version = 'v1.0.0'; // Versione di fallback in caso di errori
        }
    }

    private function loadFromFallbackCache() {
        try {
            if (!file_exists($this->fallbackCacheFile)) {
                return false;
            }

            $cacheData = json_decode(file_get_contents($this->fallbackCacheFile), true);
            if (!$cacheData || 
                !isset($cacheData['version']) || 
                !isset($cacheData['timestamp']) ||
                (time() - $cacheData['timestamp']) > $this->cacheDuration * 2) { // Il fallback ha durata doppia
                return false;
            }

            $this->version = $cacheData['version'];
            $this->logger->info('Versione caricata dalla cache di fallback: ' . $this->version);
            return true;

        } catch (Exception $e) {
            $this->logger->error('Errore nel caricamento della cache di fallback: ' . $e->getMessage());
            return false;
        }
    }

    private function saveToFallbackCache() {
        try {
            // Crea la directory di fallback se non esiste
            $fallbackDir = dirname($this->fallbackCacheFile);
            if (!file_exists($fallbackDir)) {
                mkdir($fallbackDir, 0777, true);
            }

            $cacheData = [
                'version' => $this->version,
                'timestamp' => time()
            ];
            
            if (file_put_contents($this->fallbackCacheFile, json_encode($cacheData)) === false) {
                throw new Exception('Impossibile scrivere la cache di fallback');
            }
            
            $this->logger->info('Cache di fallback aggiornata con successo');

        } catch (Exception $e) {
            $this->logger->error('Errore nel salvataggio della cache di fallback: ' . $e->getMessage());
        }
    }

    private function saveToCache() {
        try {
            // Crea la directory cache se non esiste
            $cacheDir = dirname($this->cacheFile);
            if (!file_exists($cacheDir)) {
                mkdir($cacheDir, 0777, true);
            }

            $cacheData = [
                'version' => $this->version,
                'timestamp' => time()
            ];
            
            if (file_put_contents($this->cacheFile, json_encode($cacheData)) === false) {
                throw new Exception('Impossibile scrivere la cache principale');
            }
            
            $this->logger->info('Cache principale aggiornata con successo');

        } catch (Exception $e) {
            $this->logger->error('Errore nel salvataggio della cache principale: ' . $e->getMessage());
            // Prova a salvare nella cache di fallback
            $this->saveToFallbackCache();
        }
    }

    private function loadFromCache() {
        if (!file_exists($this->cacheFile)) {
            return false;
        }

        $cacheData = json_decode(file_get_contents($this->cacheFile), true);
        if (!$cacheData || 
            !isset($cacheData['version']) || 
            !isset($cacheData['timestamp']) ||
            (time() - $cacheData['timestamp']) > $this->cacheDuration) {
            return false;
        }

        $this->version = $cacheData['version'];
        return true;
    }

    public function getVersion() {
        return $this->version;
    }

    public function refreshVersion() {
        try {
            // Forza il ricaricamento dal file
            if (file_exists($this->cacheFile)) {
                unlink($this->cacheFile);
            }
            if (file_exists($this->fallbackCacheFile)) {
                unlink($this->fallbackCacheFile);
            }
            
            $this->loadVersion();
            $this->logger->info('Versione aggiornata con successo: ' . $this->version);
            return $this->version;

        } catch (Exception $e) {
            $this->logger->error('Errore nell\'aggiornamento della versione: ' . $e->getMessage());
            return $this->version; // Ritorna la versione corrente in caso di errori
        }
    }
} 