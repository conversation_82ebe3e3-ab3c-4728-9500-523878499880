class UI {
    static init() {
        this.setupMenuToggle();
        this.setupLogout();
        this.setupSearchClear();
        this.setupNavigation();
    }

    static setupMenuToggle() {
        const menuToggle = document.getElementById('menuToggle');
        const sidebar = document.getElementById('sidebar');
        const overlay = document.getElementById('overlay');

        if (menuToggle && sidebar && overlay) {
            const toggleMenu = () => {
                sidebar.classList.toggle('active');
                overlay.classList.toggle('active');
                document.body.classList.toggle('no-scroll');
            };

            menuToggle.addEventListener('click', toggleMenu);
            overlay.addEventListener('click', toggleMenu);
        }
    }

    static setupLogout() {
        const logoutBtn = document.getElementById('logoutBtn');
        if (logoutBtn) {
            logoutBtn.addEventListener('click', async () => {
                try {
                    // Ottieni il percorso base del sito
                    const basePath = window.location.pathname.substring(0, window.location.pathname.lastIndexOf('/') + 1);
                    
                    // Prima chiamiamo auth/logout.php per distruggere la sessione
                    const response = await fetch(basePath + 'auth/logout.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        credentials: 'same-origin'
                    });

                    if (!response.ok) {
                        throw new Error('Errore nella risposta del server');
                    }

                    const data = await response.json();
                    
                    if (data.success) {
                        // Se il logout è avvenuto con successo, redirect alla pagina di arrivederci
                        window.location.href = basePath + 'logout.php';
                    } else {
                        this.showError(data.message || 'Errore durante il logout');
                    }
                } catch (error) {
                    console.error('Errore:', error);
                    this.showError('Errore durante il logout. Riprova.');
                }
            });
        }
    }

    static setupSearchClear() {
        const clearBtn = document.getElementById('clearSearch');
        const searchInput = document.getElementById('addressSearch');
        
        if (clearBtn && searchInput) {
            clearBtn.addEventListener('click', () => {
                searchInput.value = '';
                searchInput.focus();
            });
        }
    }

    static setupNavigation() {
        const navItems = document.querySelectorAll('.nav-item');
        
        navItems.forEach(item => {
            // Gestione posizionamento tooltip
            item.addEventListener('mouseenter', (e) => {
                const tooltip = item.querySelector('.nav-tooltip');
                if (tooltip) {
                    const rect = item.getBoundingClientRect();
                    tooltip.style.top = `${rect.top}px`;
                }
            });

            // Gestione click
            item.addEventListener('click', (e) => {
                // Se l'elemento è disabilitato, previeni l'azione di default e il cambio di sezione
                if (item.classList.contains('disabled')) {
                    e.preventDefault();
                    return;
                }
                
                e.preventDefault();
                
                // Rimuovi la classe active da tutti gli elementi
                navItems.forEach(i => i.classList.remove('active'));
                
                // Aggiungi la classe active all'elemento cliccato
                item.classList.add('active');
                
                // Gestisci il cambio di sezione qui
                const section = item.getAttribute('data-section');
                this.changeSection(section);
            });
        });
    }

    static changeSection(section) {
        // Implementa il cambio di sezione qui
        console.log('Cambio sezione:', section);
    }

    static showError(message) {
        // Mostra un alert con il messaggio di errore
        const errorDiv = document.createElement('div');
        errorDiv.className = 'error-message';
        errorDiv.textContent = message;
        
        // Stili per il messaggio di errore
        errorDiv.style.position = 'fixed';
        errorDiv.style.top = '20px';
        errorDiv.style.left = '50%';
        errorDiv.style.transform = 'translateX(-50%)';
        errorDiv.style.backgroundColor = '#ff3333';
        errorDiv.style.color = 'white';
        errorDiv.style.padding = '10px 20px';
        errorDiv.style.borderRadius = '5px';
        errorDiv.style.zIndex = '9999';
        
        document.body.appendChild(errorDiv);
        
        // Rimuovi il messaggio dopo 3 secondi
        setTimeout(() => {
            errorDiv.remove();
        }, 3000);
    }
}

// Inizializzazione
document.addEventListener('DOMContentLoaded', () => {
    UI.init();
});