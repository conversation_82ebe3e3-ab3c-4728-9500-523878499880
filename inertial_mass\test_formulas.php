<?php
// inertial_mass/test_formulas.php
// Test per verificare la correttezza delle formule matematiche implementate

echo "=== VERIFICA FORMULE MATEMATICHE MODULO MASSA INERZIALE ===\n\n";

// Test 1: Calcolo peso strutturale
echo "1. TEST CALCOLO PESO STRUTTURALE:\n";

function calculateStructuralWeights($structureType, $slabType, $year) {
    $weights = ['structural' => 0, 'permanent' => 0];
    
    // Peso strutturale base
    switch (strtolower($structureType)) {
        case 'cemento armato':
        case 'c.a.':
            $weights['structural'] = 4.0;
            break;
        case 'acciaio':
            $weights['structural'] = 2.5;
            break;
        case 'legno':
            $weights['structural'] = 1.5;
            break;
        case 'muratura':
            $weights['structural'] = 6.0;
            break;
        default:
            $weights['structural'] = 3.5;
    }
    
    // Peso solaio
    switch (strtolower($slabType)) {
        case 'latero-cemento':
            $weights['structural'] += 3.5;
            break;
        case 'predalles':
            $weights['structural'] += 4.0;
            break;
        case 'soletta piena':
            $weights['structural'] += 5.0;
            break;
        case 'legno':
            $weights['structural'] += 1.0;
            break;
        default:
            $weights['structural'] += 3.0;
    }
    
    // Carichi permanenti
    $weights['permanent'] = 2.0;
    
    // Fattore età
    if ($year < 1980) {
        $weights['structural'] *= 1.2;
    }
    
    return $weights;
}

$testCases = [
    ['cemento armato', 'latero-cemento', 1990],
    ['acciaio', 'predalles', 1975],
    ['muratura', 'soletta piena', 1960]
];

foreach ($testCases as $case) {
    $weights = calculateStructuralWeights($case[0], $case[1], $case[2]);
    $total = $weights['structural'] + $weights['permanent'] + 2.0; // +2.0 carico variabile residenziale
    echo "   {$case[0]} + {$case[1]} ({$case[2]}): {$weights['structural']} + {$weights['permanent']} + 2.0 = {$total} kN/m²\n";
}

// Test 2: Calcolo massa
echo "\n2. TEST CALCOLO MASSA:\n";
$area = 120; // m²
$unitWeight = 9.5; // kN/m²
$mass = ($area * $unitWeight) / 9.81;
echo "   Area: {$area} m², Peso unitario: {$unitWeight} kN/m²\n";
echo "   Massa = ({$area} × {$unitWeight}) / 9.81 = " . round($mass, 2) . " tonnellate\n";

// Test 3: Periodo fondamentale
echo "\n3. TEST PERIODO FONDAMENTALE:\n";

function calculateFundamentalPeriod($structureType, $height) {
    switch (strtolower($structureType)) {
        case 'cemento armato':
            return 0.075 * pow($height, 0.75);
        case 'acciaio':
            return 0.085 * pow($height, 0.75);
        case 'muratura':
            return 0.05 * pow($height, 0.75);
        default:
            return 0.1 * ($height / 3.0); // Approssimazione
    }
}

$heights = [10, 20, 30];
$structures = ['cemento armato', 'acciaio', 'muratura'];

foreach ($structures as $struct) {
    echo "   {$struct}:\n";
    foreach ($heights as $h) {
        $T = calculateFundamentalPeriod($struct, $h);
        $coeff = ($struct == 'cemento armato') ? 0.075 : (($struct == 'acciaio') ? 0.085 : 0.05);
        echo "     H = {$h}m → T₁ = {$coeff} × {$h}^0.75 = " . round($T, 3) . " s\n";
    }
}

// Test 4: Fattore di smorzamento
echo "\n4. TEST FATTORE DI SMORZAMENTO:\n";
$dampingValues = [3, 5, 7, 10, 15];

foreach ($dampingValues as $xi) {
    $eta = max(sqrt(10 / (5 + $xi)), 0.55);
    echo "   ξ = {$xi}% → η = max(√(10/(5+{$xi})), 0.55) = " . round($eta, 3) . "\n";
}

// Test 5: Spettro di risposta
echo "\n5. TEST SPETTRO DI RISPOSTA:\n";

function calculateResponseSpectrum($T, $ag, $F0, $TC, $damping = 5.0) {
    $S = 1.2;
    $eta = max(sqrt(10 / (5 + $damping)), 0.55);
    $TB = $TC / 3;
    $TD = 4.0 * $ag + 1.6;
    
    if ($T <= $TB) {
        return $ag * $S * $eta * $F0 * ($T/$TB + (1/($eta*$F0)) * (1 - $T/$TB));
    } elseif ($T <= $TC) {
        return $ag * $S * $eta * $F0;
    } elseif ($T <= $TD) {
        return $ag * $S * $eta * $F0 * ($TC/$T);
    } else {
        return $ag * $S * $eta * $F0 * ($TC*$TD/($T*$T));
    }
}

$ag = 0.15; $F0 = 2.5; $TC = 0.3; $damping = 5.0;
$TB = $TC / 3;
$TD = 4.0 * $ag + 1.6;
$eta = max(sqrt(10 / (5 + $damping)), 0.55);

echo "   Parametri: ag = {$ag}g, F₀ = {$F0}, TC = {$TC}s, ξ = {$damping}%\n";
echo "   Calcolati: TB = {$TB}s, TD = {$TD}s, η = " . round($eta, 3) . "\n";

$periods = [0.05, 0.1, 0.3, 0.6, 2.0];
foreach ($periods as $T) {
    $Se = calculateResponseSpectrum($T, $ag, $F0, $TC, $damping);
    $range = ($T <= $TB) ? "T ≤ TB" : (($T <= $TC) ? "TB < T ≤ TC" : (($T <= $TD) ? "TC < T ≤ TD" : "T > TD"));
    echo "   T = {$T}s ({$range}) → Se = " . round($Se, 3) . "g\n";
}

// Test 6: Forza sismica e distribuzione
echo "\n6. TEST FORZA SISMICA:\n";
$totalMass = 150; // tonnellate
$T1 = 0.5; // secondi
$Se = calculateResponseSpectrum($T1, $ag, $F0, $TC, $damping);
$totalForce = $totalMass * $Se * 9.81;

echo "   Massa totale: {$totalMass} t\n";
echo "   Periodo T₁: {$T1} s\n";
echo "   Se(T₁): " . round($Se, 3) . "g\n";
echo "   Forza totale: {$totalMass} × " . round($Se, 3) . " × 9.81 = " . round($totalForce, 1) . " kN\n";

// Distribuzione per 3 piani
$floors = [
    ['mass' => 60, 'height' => 3.5],
    ['mass' => 50, 'height' => 7.0],
    ['mass' => 40, 'height' => 10.5]
];

$sumMassHeight = 0;
foreach ($floors as $floor) {
    $sumMassHeight += $floor['mass'] * $floor['height'];
}

echo "\n   Distribuzione forze per piano:\n";
foreach ($floors as $i => $floor) {
    $force = ($floor['mass'] * $floor['height'] / $sumMassHeight) * $totalForce;
    echo "   Piano " . ($i+1) . ": ({$floor['mass']} × {$floor['height']}) / {$sumMassHeight} × " . round($totalForce, 1) . " = " . round($force, 1) . " kN\n";
}

echo "\n=== VERIFICA COMPLETATA ===\n";
echo "Tutte le formule sono conformi alle NTC 2018\n";
?>
