document.addEventListener('DOMContentLoaded', () => {
    // Aggiungi event listener per il link privacy nella footbar
    const privacyLink = document.querySelector('.privacy-link');
    if (privacyLink) {
        privacyLink.addEventListener('click', openPrivacyPopup);
    }
});

function openPrivacyPopup() {
    const popup = document.getElementById('privacy-popup');
    if (popup) {
        popup.style.display = 'flex';
        
        // Aggiungi event listener per chiudere con ESC
        document.addEventListener('keydown', handleEscKeyPress);
        
        // Aggiungi event listener per chiudere cliccando fuori
        popup.addEventListener('click', handleOutsideClick);
    }
}

function closePrivacyPopup() {
    const popup = document.getElementById('privacy-popup');
    if (popup) {
        popup.style.display = 'none';
        
        // Rimuovi gli event listener
        document.removeEventListener('keydown', handleEscKeyPress);
        popup.removeEventListener('click', handleOutsideClick);
    }
}

function handleEscKeyPress(event) {
    if (event.key === 'Escape') {
        closePrivacyPopup();
    }
}

function handleOutsideClick(event) {
    if (event.target.id === 'privacy-popup') {
        closePrivacyPopup();
    }
} 