<?php
require_once '../includes/db_config.php';
session_start();

// Verifica se l'utente è loggato e ha il ruolo di admin
if (!isset($_SESSION['user_id']) || !isset($_SESSION['role']) || $_SESSION['role'] !== 'admin') {
    header("Location: ../index.php");
    exit;
}

// Funzione per ottenere le impostazioni correnti
function getSettings() {
    try {
        $conn = getConnection();
        $stmt = $conn->query("SELECT * FROM settings");
        return $stmt->fetchAll(PDO::FETCH_KEY_PAIR);
    } catch (PDOException $e) {
        error_log("Errore nel recupero delle impostazioni: " . $e->getMessage());
        return false;
    }
}

$settings = getSettings();

// Includi l'header admin
include '../includes/admin_header.php';
?>

<div class="admin-wrapper">
    <div class="content">
        <h1>Impostazioni Sistema</h1>
        <p class="subtitle">Gestisci le impostazioni generali del sistema</p>

        <div class="settings-section">
            <!-- Sezione Impostazioni Generali -->
            <div class="settings-card general-settings">
                <h2><i class="fas fa-cog"></i> Impostazioni Generali</h2>
                <div class="settings-content">
                    <form id="settingsForm">
                        <div class="form-group">
                            <label for="site_name">Nome Sito</label>
                            <input type="text" id="site_name" name="site_name" 
                                   value="<?php echo htmlspecialchars($settings['site_name'] ?? 'ASDP'); ?>">
                        </div>
                        <div class="form-group">
                            <label for="site_description">Descrizione Sito</label>
                            <textarea id="site_description" name="site_description"><?php 
                                echo htmlspecialchars($settings['site_description'] ?? ''); 
                            ?></textarea>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Sezione Email -->
            <div class="settings-card email-settings">
                <h2><i class="fas fa-envelope"></i> Impostazioni Email</h2>
                <div class="settings-content">
                    <div class="form-group">
                        <label for="smtp_host">SMTP Host</label>
                        <input type="text" id="smtp_host" name="smtp_host" 
                               value="<?php echo htmlspecialchars($settings['smtp_host'] ?? ''); ?>">
                    </div>
                    <div class="form-group">
                        <label for="smtp_port">SMTP Porta</label>
                        <input type="number" id="smtp_port" name="smtp_port" 
                               value="<?php echo htmlspecialchars($settings['smtp_port'] ?? '587'); ?>">
                    </div>
                    <div class="form-group">
                        <label for="smtp_user">SMTP Username</label>
                        <input type="text" id="smtp_user" name="smtp_user" 
                               value="<?php echo htmlspecialchars($settings['smtp_user'] ?? ''); ?>">
                    </div>
                    <div class="form-group">
                        <label for="smtp_pass">SMTP Password</label>
                        <input type="password" id="smtp_pass" name="smtp_pass" 
                               value="<?php echo htmlspecialchars($settings['smtp_pass'] ?? ''); ?>">
                    </div>
                </div>
            </div>

            <!-- Sezione Backup -->
            <div class="settings-card backup-settings">
                <h2><i class="fas fa-database"></i> Impostazioni Backup</h2>
                <div class="settings-content">
                    <div class="form-group">
                        <label for="backup_retention">Giorni di Conservazione Backup</label>
                        <input type="number" id="backup_retention" name="backup_retention" 
                               value="<?php echo htmlspecialchars($settings['backup_retention'] ?? '30'); ?>">
                    </div>
                    <div class="form-group">
                        <label for="backup_time">Ora Backup Automatico (24h)</label>
                        <input type="time" id="backup_time" name="backup_time" 
                               value="<?php echo htmlspecialchars($settings['backup_time'] ?? '02:00'); ?>">
                    </div>
                </div>
            </div>

            <!-- Sezione Log -->
            <div class="settings-card log-settings">
                <h2><i class="fas fa-list"></i> Impostazioni Log</h2>
                <div class="settings-content">
                    <div class="form-group">
                        <label for="log_level">Livello Log</label>
                        <select id="log_level" name="log_level">
                            <?php
                            $logLevels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL'];
                            $currentLevel = $settings['log_level'] ?? 'INFO';
                            foreach ($logLevels as $level) {
                                $selected = ($level === $currentLevel) ? 'selected' : '';
                                echo "<option value=\"$level\" $selected>$level</option>";
                            }
                            ?>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="log_retention">Giorni di Conservazione Log</label>
                        <input type="number" id="log_retention" name="log_retention" 
                               value="<?php echo htmlspecialchars($settings['log_retention'] ?? '90'); ?>">
                    </div>
                </div>
            </div>
        </div>

        <div class="actions-bar">
            <span id="lastUpdate" class="last-update">
                <?php
                try {
                    $conn = getConnection();
                    $stmt = $conn->query("SELECT MAX(updated_at) as last_update FROM settings");
                    $lastUpdate = $stmt->fetch(PDO::FETCH_ASSOC)['last_update'];
                    if ($lastUpdate) {
                        echo "Ultima modifica: " . date('d/m/Y H:i', strtotime($lastUpdate));
                    }
                } catch (PDOException $e) {
                    error_log("Errore nel recupero della data di ultima modifica: " . $e->getMessage());
                }
                ?>
            </span>
            <button class="settings-btn" onclick="saveSettings()">
                <i class="fas fa-save"></i> Salva Modifiche
            </button>
        </div>
    </div>

    <?php include 'admin_footer.php'; ?>
</div>

<style>
.admin-wrapper {
    min-height: calc(100vh - 50px);
    display: flex;
    flex-direction: column;
    background-color: #121212;
}

.content {
    max-width: 1200px;
    margin: 0.5rem auto;
    padding: 0 1rem;
    height: calc(100vh - 120px);
    overflow: hidden;
    width: 100%;
    position: relative;
    background-color: #121212;
}

h1 {
    color: #FF7043;
    font-size: 1.5rem;
    margin: 0 0 0.25rem 0;
    font-weight: 500;
}

.subtitle {
    color: #BDBDBD;
    margin-bottom: 1rem;
    font-size: 0.9rem;
}

.settings-section {
    display: grid;
    grid-template-columns: repeat(12, 1fr);
    gap: 1rem;
    height: calc(100vh - 250px);
    overflow-y: auto;
}

/* Stile Scrollbar per Webkit (Chrome, Safari, Edge) */
.settings-section::-webkit-scrollbar {
    width: 8px;
}

.settings-section::-webkit-scrollbar-track {
    background: #1a1a1a;
    border-radius: 4px;
}

.settings-section::-webkit-scrollbar-thumb {
    background: #FF7043;
    border-radius: 4px;
}

.settings-section::-webkit-scrollbar-thumb:hover {
    background: #F4511E;
}

/* Stile Scrollbar per Firefox */
.settings-section {
    scrollbar-width: thin;
    scrollbar-color: #FF7043 #1a1a1a;
}

.settings-card {
    background: #1E1E1E;
    border-radius: 8px;
    padding: 1rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.2);
}

/* Layout delle card */
.general-settings {
    grid-column: span 6;
}

.email-settings {
    grid-column: span 6;
}

.backup-settings {
    grid-column: span 6;
}

.log-settings {
    grid-column: span 6;
}

.settings-card h2 {
    color: #FF7043;
    font-size: 1.1rem;
    margin: 0 0 0.75rem 0;
    padding-bottom: 0.4rem;
    border-bottom: 2px solid #FF7043;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.settings-content {
    color: #FFFFFF;
}

.form-group {
    margin-bottom: 1rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.25rem;
    color: #BDBDBD;
    font-size: 0.9rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid #333;
    border-radius: 4px;
    background: #242424;
    color: #FFFFFF;
    font-size: 0.9rem;
}

.form-group textarea {
    min-height: 80px;
    resize: vertical;
}

.actions-bar {
    position: fixed;
    bottom: 50px;
    left: 50%;
    transform: translateX(-50%);
    background: #1a1a1a;
    padding: 0.75rem 1rem;
    border-radius: 8px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
    z-index: 10;
    width: calc(100% - 2rem);
    max-width: 1168px;
}

.last-update {
    color: #BDBDBD;
    font-size: 0.9rem;
}

.settings-btn {
    background: #FF7043;
    color: #FFFFFF;
    border: none;
    border-radius: 4px;
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
}

.settings-btn:hover {
    background: #F4511E;
    transform: translateY(-2px);
}

/* Responsive */
@media (max-width: 1200px) {
    .general-settings,
    .email-settings,
    .backup-settings,
    .log-settings {
        grid-column: span 12;
    }
}

@media (max-width: 768px) {
    .settings-section {
        height: auto;
    }

    .actions-bar {
        flex-direction: column;
        gap: 1rem;
        padding: 1rem;
    }

    .settings-btn {
        width: 100%;
        justify-content: center;
    }
}
</style>

<script>
function saveSettings() {
    const form = document.getElementById('settingsForm');
    const formData = new FormData(form);
    
    // Aggiungi altri campi al FormData
    document.querySelectorAll('input, select, textarea').forEach(element => {
        if (!formData.has(element.name)) {
            formData.append(element.name, element.value);
        }
    });

    // Mostra un messaggio di conferma
    if (confirm('Sei sicuro di voler salvare le modifiche?')) {
        // Implementare la logica di salvataggio
        console.log('Saving settings...');
        
        // Simula un salvataggio riuscito
        setTimeout(() => {
            alert('Impostazioni salvate con successo!');
            // Aggiorna la data di ultima modifica
            document.getElementById('lastUpdate').textContent = 
                'Ultima modifica: ' + new Date().toLocaleString('it-IT');
        }, 500);
    }
}
</script>
