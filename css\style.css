@import 'variables.css';

/* Reset e stili base */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Arial, sans-serif;
    background-color: var(--background-dark);
    color: var(--text-primary);
    line-height: 1.6;
}

/* Layout principale */
.app-container {
    display: flex;
    min-height: 100vh;
}

/* Sidebar */
.sidebar {
    width: 280px;
    background-color: var(--surface);
    border-right: 1px solid var(--border-color);
    padding: var(--spacing-md);
    display: flex;
    flex-direction: column;
    position: fixed;
    height: 100vh;
    left: 0;
    top: 0;
    z-index: 1000;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    transition: transform var(--transition-speed) var(--transition-timing);
}

.logo-container {
    text-align: center;
    padding: var(--spacing-md) 0;
    margin-bottom: var(--spacing-lg);
}

.logo {
    max-width: 150px;
    height: auto;
}

/* Profilo utente */
.user-profile {
    text-align: center;
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
}

.user-avatar {
    width: 60px;
    height: 60px;
    background-color: var(--primary-color);
    color: var(--text-on-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: 500;
    margin: 0 auto var(--spacing-sm);
}

.user-name {
    font-size: 1.1rem;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.user-role {
    font-size: 0.9rem;
    color: var(--text-secondary);
}

/* Menu di navigazione */
.nav-menu {
    flex-grow: 1;
    margin-bottom: var(--spacing-lg);
}

.nav-item {
    display: block;
    padding: var(--spacing-md);
    color: var(--text-secondary);
    text-decoration: none;
    border-radius: var(--border-radius);
    margin-bottom: var(--spacing-xs);
    transition: all var(--transition-speed) var(--transition-timing);
}

.nav-item:hover {
    background-color: var(--background-lighter);
    color: var(--text-primary);
}

.nav-item.active {
    background-color: var(--primary-color);
    color: var(--text-on-primary);
}

/* Bottone logout */
.logout-btn {
    width: 100%;
    padding: var(--spacing-md);
    background-color: transparent;
    border: 1px solid var(--border-color);
    color: var(--danger-color);
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: all var(--transition-speed) var(--transition-timing);
    font-size: 1rem;
}

.logout-btn:hover {
    background-color: var(--danger-color);
    color: var(--text-primary);
    border-color: var(--danger-color);
}

/* Contenuto principale */
.main-content {
    flex-grow: 1;
    margin-left: 280px;
    padding: var(--spacing-lg);
    background-color: var(--background-dark);
}

.page-header {
    margin-bottom: var(--spacing-xl);
}

.page-header h1 {
    color: var(--text-primary);
    font-size: 1.8rem;
    margin-bottom: var(--spacing-sm);
}

.page-header p {
    color: var(--text-secondary);
}

/* Barra di ricerca */
.search-container {
    margin-bottom: var(--spacing-lg);
}

.search-box-container {
    position: relative;
    max-width: 600px;
}

.search-box {
    width: 100%;
    padding: var(--spacing-md) var(--spacing-lg);
    background-color: var(--surface);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    color: var(--text-primary);
    font-size: 1rem;
    transition: all var(--transition-speed) var(--transition-timing);
}

.search-box:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(255, 140, 0, 0.1);
}

.clear-search {
    position: absolute;
    right: var(--spacing-sm);
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: var(--spacing-xs);
    transition: color var(--transition-speed) var(--transition-timing);
}

.clear-search:hover {
    color: var(--primary-color);
}

/* Container mappa e info */
.map-info-container {
    display: grid;
    grid-template-columns: 1fr 400px;
    gap: var(--spacing-lg);
    background-color: var(--surface);
    border-radius: var(--border-radius);
    overflow: hidden;
    border: 1px solid var(--border-color);
}

/* Pannelli informativi */
.info-panel {
    padding: var(--spacing-md);
    background-color: var(--surface);
    border-left: 1px solid var(--border-color);
}

.panel-section {
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--border-color);
}

.panel-section:last-child {
    border-bottom: none;
}

.section-title {
    color: var(--primary-color);
    font-size: 1.2rem;
    margin-bottom: var(--spacing-md);
}

/* Tabelle */
.seismic-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 10px;
    background-color: transparent;
}

.seismic-table th,
.seismic-table td {
    padding: 4px 8px;
    text-align: center;
    border: 1px solid var(--border-color);
    font-size: 0.9em;
}

.seismic-table th {
    background-color: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
    font-weight: 500;
}

.seismic-table td {
    color: var(--text-primary);
}

.seismic-table tr:nth-child(even) {
    background-color: var(--background-darker);
}

.seismic-table tr:hover {
    background-color: var(--background-lighter);
}

/* Sezione parametri sismici */
.seismic-section {
    height: auto;
    display: flex;
    flex-direction: column;
}

/* Container dei parametri sismici */
.param-group {
    margin-bottom: 15px;
}

.info-row {
    margin-bottom: 8px;
}

.info-row input,
.info-row select {
    height: 30px;
    font-size: 0.9em;
}

/* Bottone calcola */
.action-button {
    padding: 8px 15px;
    margin-top: 10px;
    font-size: 0.9em;
}

/* Stili base per il container delle informazioni sismiche */
.seismic-info {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

/* Base styles per tutte le zone sismiche */
.zona-sismica {
    display: inline-block;
    padding: 4px 12px;
    border-radius: 4px;
    font-weight: bold;
    color: #FFFFFF !important;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.6);
    text-align: center;
    min-width: 80px;
    border: 1px solid rgba(0,0,0,0.2);
}

/* Colori specifici per ogni zona */
.zona-1 { background-color: #FF0000 !important; }
.zona-2 { background-color: #FF6600 !important; }
.zona-2A { background-color: #FF8800 !important; }
.zona-2B { background-color: #FFAA00 !important; }
.zona-3 { background-color: #FFCC00 !important; }
.zona-3B { background-color: #FFD700 !important; }
.zona-4 { background-color: #FFEE00 !important; }

/* Forza il colore bianco per tutte le zone */
.zona-1, .zona-2, .zona-2A, .zona-2B, .zona-3, .zona-3B, .zona-4 {
    color: #FFFFFF !important;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.6) !important;
}

/* Stili aggiuntivi per zone chiare */
.zona-2B, .zona-3, .zona-3B, .zona-4 {
    border: 2px solid rgba(0,0,0,0.3) !important;
    text-shadow: 1px 1px 3px rgba(0,0,0,0.8) !important;
}

/* Stili per i dettagli sismici */
.seismic-details {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.categoria, .rischio {
    font-size: 0.9em;
    color: #666;
}

/* Stili per il container delle informazioni */
.info-container {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    padding: 20px;
    margin-top: 20px;
}

.info-header {
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
    margin-bottom: 15px;
}

.info-header h2 {
    margin: 0;
    color: #333;
    font-size: 1.2em;
}

.info-row {
    display: flex;
    padding: 8px 0;
    border-bottom: 1px solid #f5f5f5;
}

.info-label {
    flex: 0 0 40%;
    color: #666;
    font-weight: 500;
}

.info-value {
    flex: 0 0 60%;
    color: #333;
}

.info-value.description {
    font-size: 0.9em;
    line-height: 1.4;
}

.info-value.date {
    font-size: 0.9em;
    color: #888;
}

/* Responsive design */
@media (max-width: 768px) {
    .info-row {
        flex-direction: column;
        gap: 4px;
    }

    .info-label, .info-value {
        flex: 1;
    }
}

/* Aggiungi questi stili al tuo CSS esistente */
.loading-indicator {
    display: none;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0, 0, 0, 0.8);
    padding: 20px;
    border-radius: 8px;
    z-index: 1000;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--primary-color);
    border-top: 4px solid transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.error-container {
    display: none;
    margin-top: 10px;
    padding: 10px;
}

.error-message {
    background-color: #ff5252;
    color: white;
    padding: 10px;
    border-radius: 4px;
    margin-bottom: 15px;
    font-size: 0.9em;
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.action-button {
    background-color: var(--primary-color);
    color: var(--text-on-primary);
    border: none;
    padding: 10px 20px;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
    width: 100%;
    margin-top: 15px;
    transition: background-color 0.3s ease;
}

.action-button:hover {
    background-color: var(--primary-dark);
}

.action-button:active {
    background-color: var(--primary-color);
}

/* Stili per i titoli delle sezioni */
.section-title {
    color: var(--primary-color);
    font-size: 1.2em;
    margin-bottom: 10px;
    padding-bottom: 8px;
    border-bottom: 1px solid var(--border-color);
}

.section-subtitle {
    color: var(--text-color);
    font-size: 0.9em;
    margin-bottom: 15px;
    opacity: 0.8;
    text-align: center;
    font-style: italic;
}

.section-divider {
    height: 1px;
    background: var(--border-color);
    margin: 20px 0;
}

.seismic-section, .cadastral-section {
    padding: 15px;
    background: var(--background-color);
    border-radius: 8px;
}

/* Stili Admin Dashboard */
.admin-container {
    max-width: 1200px;
    margin: 2rem auto;
    padding: 2rem;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: rgba(0, 0, 0, 0.8);
    border: 1px solid #ff8c00;
    border-radius: 8px;
    padding: 1.5rem;
    text-align: center;
}

.stat-card i {
    font-size: 2rem;
    color: #ff8c00;
    margin-bottom: 1rem;
}

.stat-value {
    font-size: 1.8rem;
    font-weight: bold;
    color: #fff;
    margin: 0.5rem 0;
}

.stat-label {
    color: #999;
    font-size: 0.9rem;
}

.section-title {
    color: #ff8c00;
    margin: 2rem 0 1rem;
    font-size: 1.5rem;
}

.log-container {
    background: rgba(0, 0, 0, 0.8);
    border: 1px solid #ff8c00;
    border-radius: 8px;
    padding: 1rem;
    margin-top: 1rem;
    max-height: 400px;
    overflow-y: auto;
}

.log-entry {
    padding: 0.5rem;
    border-bottom: 1px solid rgba(255, 140, 0, 0.2);
    color: #fff;
}

.log-entry:last-child {
    border-bottom: none;
}

.log-timestamp {
    color: #999;
    font-size: 0.8rem;
    margin-right: 1rem;
}

.log-user {
    color: #ff8c00;
    margin-right: 1rem;
}

.log-action {
    color: #fff;
}

@media (max-width: 768px) {
    .admin-container {
        padding: 1rem;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }
}

/* Responsive Design */
@media screen and (max-width: 1024px) {
    .map-info-container {
        grid-template-columns: 1fr;
    }
}

@media screen and (max-width: 768px) {
    .menu-toggle {
        display: block;
    }

    .sidebar {
        transform: translateX(-100%);
    }

    .sidebar.active {
        transform: translateX(0);
    }

    .main-content {
        margin-left: 0;
        padding-top: calc(var(--spacing-lg) + 48px);
    }

    .overlay.active {
        display: block;
    }
}

/* Menu toggle mobile */
.menu-toggle {
    display: none;
    position: fixed;
    top: var(--spacing-md);
    left: var(--spacing-md);
    z-index: 1001;
    background: var(--surface);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
    padding: var(--spacing-sm);
    border-radius: var(--border-radius);
    cursor: pointer;
}

.overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 999;
}

.info-message {
    margin-top: 10px;
    padding: 8px;
    border-radius: 4px;
    background-color: #2A2A2A;
    font-size: 0.9em;
}

#cadastralMessage {
    color: #FF7043;
    display: block;
    text-align: center;
    font-style: italic;
}

/* Stili per i messaggi */
.message {
    padding: 10px;
    margin-bottom: 15px;
    border-radius: 4px;
    font-size: 14px;
}

.message.info {
    background-color: #e3f2fd;
    color: #0d47a1;
    border: 1px solid #bbdefb;
}

.message.success {
    background-color: #e8f5e9;
    color: #1b5e20;
    border: 1px solid #c8e6c9;
}

.message.warning {
    background-color: #fff3e0;
    color: #e65100;
    border: 1px solid #ffe0b2;
}

.message.error {
    background-color: #ffebee;
    color: #b71c1c;
    border: 1px solid #ffcdd2;
}

/* Stili per il form dei dati catastali */
#dati-catastali form {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
    margin-top: 15px;
}

#dati-catastali .form-group {
    margin-bottom: 10px;
}

#dati-catastali label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #666;
}

#dati-catastali input[type="text"] {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: #f5f5f5;
    font-size: 14px;
    color: #333;
}

#dati-catastali input[readonly] {
    cursor: default;
}

.map-container {
    position: relative;
    width: 100%;
    height: 100%;
}

#map, #leaflet-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

#leaflet-overlay {
    pointer-events: none;
    z-index: 1;
}

#leaflet-overlay.active {
    pointer-events: auto;
}
