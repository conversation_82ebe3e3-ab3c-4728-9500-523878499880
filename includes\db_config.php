<?php
error_reporting(E_ALL);
ini_set('display_errors', 0);

// Configurazione del database
define('DB_HOST', 'localhost');
define('DB_USER', 'root');     // Utente di default di XAMPP
define('DB_PASS', '');         // Password vuota di default in XAMPP
define('DB_NAME', 'asdp_db');  // Nome del database

// Funzione per ottenere la connessione al database
function getConnection() {
    static $pdo = null;
    
    if ($pdo === null) {
        try {
            // Connessione PDO
            $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4";
            $pdo = new PDO($dsn, DB_USER, DB_PASS);
            
            // Imposta modalità di errore
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            
            // Verifica/crea tabella users
            createUsersTable($pdo);
            
            // Verifica/crea tabella access_logs
            createAccessLogsTable($pdo);
            
            // Verifica/crea tabella user_settings
            createUserSettingsTable($pdo);
            
            // Verifica/crea tabella backups
            createBackupsTable($pdo);
            
        } catch (PDOException $e) {
            error_log("Errore di connessione al database: " . $e->getMessage());
            die("Errore di connessione al database. Controlla il file di log per i dettagli.");
        }
    }
    
    return $pdo;
}

// Funzione per creare la tabella users se non esiste
function createUsersTable($pdo) {
    $query = "CREATE TABLE IF NOT EXISTS users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(50) UNIQUE NOT NULL,
        email VARCHAR(100) UNIQUE NOT NULL,
        password VARCHAR(255) NOT NULL,
        nome VARCHAR(50),
        cognome VARCHAR(50),
        role VARCHAR(20) DEFAULT 'user',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )";
    
    try {
        $pdo->exec($query);
    } catch (PDOException $e) {
        error_log("Errore nella creazione della tabella users: " . $e->getMessage());
        throw $e;
    }
}

// Funzione per creare la tabella access_logs se non esiste
function createAccessLogsTable($pdo) {
    try {
        $sql = "CREATE TABLE IF NOT EXISTS access_logs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            action VARCHAR(255) NOT NULL,
            ip_address VARCHAR(45),
            user_agent TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id)
        )";
        
        $pdo->exec($sql);
        return true;
    } catch (PDOException $e) {
        error_log("Errore nella creazione della tabella access_logs: " . $e->getMessage());
        return false;
    }
}

// Funzione per creare la tabella user_settings se non esiste
function createUserSettingsTable($pdo) {
    try {
        $sql = "CREATE TABLE IF NOT EXISTS user_settings (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            map_type VARCHAR(50) DEFAULT 'satellite',
            language VARCHAR(10) DEFAULT 'it',
            notifications_enabled BOOLEAN DEFAULT true,
            auto_save BOOLEAN DEFAULT true,
            theme VARCHAR(20) DEFAULT 'dark',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id),
            UNIQUE KEY unique_user_settings (user_id)
        )";
        
        $pdo->exec($sql);
        return true;
    } catch (PDOException $e) {
        error_log("Errore nella creazione della tabella user_settings: " . $e->getMessage());
        return false;
    }
}

// Funzione per creare la tabella backups se non esiste
function createBackupsTable($pdo) {
    try {
        $sql = "CREATE TABLE IF NOT EXISTS backups (
            id INT(11) NOT NULL AUTO_INCREMENT,
            filename VARCHAR(255) NOT NULL,
            created_at DATETIME NOT NULL,
            size BIGINT(20) NOT NULL,
            PRIMARY KEY (id),
            UNIQUE KEY filename (filename)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
        
        $pdo->exec($sql);
        return true;
    } catch (PDOException $e) {
        error_log("Errore nella creazione della tabella backups: " . $e->getMessage());
        return false;
    }
}

// Funzione per ottenere i dati sismici
function getSismicData($pdo, $query, $params = []) {
    try {
        $stmt = $pdo->prepare($query);
        $stmt->execute($params);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        error_log("Errore nell'esecuzione della query: " . $e->getMessage());
        throw $e;
    }
}
?>
