<?php
require_once '../../includes/db_config.php';
session_start();

// Verifica accesso admin
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Accesso non autorizzato']);
    exit;
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $data = json_decode(file_get_contents('php://input'), true);
        
        if (!isset($data['id'])) {
            throw new Exception('ID utente mancante');
        }

        $userId = intval($data['id']);

        // Non permettere l'eliminazione dell'admin principale
        if ($userId === 2) { // ID dell'admin principale
            throw new Exception('Non è possibile eliminare l\'amministratore principale');
        }

        // Non permettere l'auto-eliminazione
        if ($userId === intval($_SESSION['user_id'])) {
            throw new Exception('Non puoi eliminare il tuo stesso account');
        }

        $conn = getConnection();
        
        // Verifica se l'utente esiste
        $stmt = $conn->prepare("SELECT id, username, role FROM users WHERE id = ?");
        $stmt->execute([$userId]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$user) {
            throw new Exception('Utente non trovato');
        }

        // Elimina l'utente
        $stmt = $conn->prepare("DELETE FROM users WHERE id = ?");
        $result = $stmt->execute([$userId]);

        if (!$result) {
            throw new Exception('Errore durante l\'eliminazione dell\'utente');
        }

        // Log dell'operazione
        $adminUsername = $_SESSION['username'];
        $deletedUsername = $user['username'];
        error_log("Utente $deletedUsername eliminato da $adminUsername");

        echo json_encode([
            'success' => true, 
            'message' => "Utente {$user['username']} eliminato con successo"
        ]);
    } catch (Exception $e) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
} else {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Metodo non consentito']);
} 