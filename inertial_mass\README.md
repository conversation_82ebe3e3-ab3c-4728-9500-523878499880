# Modulo Massa Inerziale - ASDP

## Panoramica
Il modulo Massa Inerziale è un componente integrato in ASDP per il calcolo della massa inerziale sismica degli edifici secondo NTC 2018, utilizzando l'intelligenza artificiale (Deepseek LLM) per calcoli avanzati. Il modulo è ora completamente integrato nell'interfaccia principale di ASDP con un pulsante dedicato nella sezione parametri sismici.

## Caratteristiche Principali
- 🏗️ **Calcolo massa inerziale** per edifici multipiano
- 🤖 **Integrazione AI** con Deepseek per analisi avanzate
- 📊 **Visualizzazione risultati** dettagliata per piano con scroll verticale
- 💾 **Salvataggio integrato** nel database ASDP
- 🔒 **Sicurezza** con autenticazione e rate limiting
- ⚡ **Cache intelligente** per ottimizzare le performance
- 🎨 **Interfaccia migliorata** con prevenzione duplicazioni e animazioni fluide

## Requisiti
- PHP 7.4 o superiore
- MySQL 5.7 o superiore
- Chiave API Deepseek
- Sessione utente ASDP attiva

## Configurazione

### 1. Chiave API Deepseek
Imposta la chiave API nel file `.env` della root ASDP:
```
DEEPSEEK_API_KEY=your_api_key_here
```

### 2. Database
Le tabelle del database sono già state create automaticamente. Il modulo utilizza quattro tabelle principali:

#### Tabelle Database
1. **inertial_mass_calculations** - Calcoli principali
2. **inertial_mass_floor_details** - Dettagli per piano
3. **inertial_mass_cache** - Cache per ottimizzazione
4. **inertial_mass_api_logs** - Log delle chiamate API

Per creare manualmente le tabelle, eseguire:
```bash
mysql -u root -p asdp_db < /c:/xampp/htdocs/progetti/asdp/sql/create_inertial_mass_tables.sql
```

#### Struttura Tabelle
```sql
-- Tabella principale calcoli
CREATE TABLE inertial_mass_calculations (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    project_id INT,
    building_data JSON NOT NULL,
    seismic_data JSON NOT NULL,
    calculation_results JSON,
    ai_analysis TEXT,
    total_mass DECIMAL(10,3),
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Dettagli per piano
CREATE TABLE inertial_mass_floor_details (
    id INT PRIMARY KEY AUTO_INCREMENT,
    calculation_id INT NOT NULL,
    floor_number INT NOT NULL,
    area DECIMAL(8,2) NOT NULL,
    height DECIMAL(5,2) NOT NULL,
    use_type VARCHAR(50) NOT NULL,
    calculated_mass DECIMAL(10,3),
    load_details JSON,
    FOREIGN KEY (calculation_id) REFERENCES inertial_mass_calculations(id) ON DELETE CASCADE
);

-- Cache per performance
CREATE TABLE inertial_mass_cache (
    id INT PRIMARY KEY AUTO_INCREMENT,
    cache_key VARCHAR(255) UNIQUE NOT NULL,
    cache_data JSON NOT NULL,
    expires_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Log API per monitoraggio
CREATE TABLE inertial_mass_api_logs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT,
    api_endpoint VARCHAR(255) NOT NULL,
    request_data JSON,
    response_data JSON,
    response_time INT,
    status VARCHAR(20) NOT NULL,
    error_message TEXT,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
);
```

## Utilizzo

### Integrazione in ASDP
Il modulo è già integrato nell'applicazione principale ASDP. Un pulsante "Calcolo Massa Inerziale" è disponibile nella sezione parametri sismici, sotto il pulsante "Ricalcola Parametri".

L'integrazione è gestita dal file `js/inertial_mass_integration.js` che carica dinamicamente la modale e i relativi script quando necessario.

### Integrazione Manuale (per sviluppatori)
Se hai bisogno di integrare la modale in una nuova pagina ASDP:

```php
<!-- Include CSS -->
<link rel="stylesheet" href="/progetti/asdp/inertial_mass/assets/css/modal.css">

<!-- Include JavaScript di integrazione -->
<script src="/progetti/asdp/js/inertial_mass_integration.js"></script>

<!-- Apertura modale con dati sismici -->
<script>
function openInertialMassCalculation(seismicData) {
    openInertialMassModal(seismicData);
}
</script>
```

### Formato Dati Sismici
```javascript
const seismicData = {
    lat: 41.9028,        // Latitudine
    lon: 12.4964,        // Longitudine
    zone: '3',           // Zona sismica
    ag: 0.062,           // Accelerazione orizzontale massima (g)
    F0: 2.604,           // Fattore amplificazione spettrale
    TC: 0.268,           // Periodo inizio tratto velocità costante (s)
    soil_category: 'C'   // Categoria sottosuolo
};
```

## API Endpoints

### 1. Servizio LLM
`POST /inertial_mass/api/llm_service.php`

Calcola la massa inerziale utilizzando Deepseek.

**Request:**
```json
{
    "location": {
        "lat": 41.9028,
        "lon": 12.4964
    },
    "seismic_params": {
        "zone": "3",
        "ag": 0.062,
        "F0": 2.604,
        "TC": 0.268,
        "soil_category": "C"
    },
    "building": {
        "structure_type": "concrete",
        "slab_type": "hollow_brick",
        "construction_year": 1990,
        "floors": [
            {
                "level": 1,
                "area": 150,
                "height": 3.5,
                "use": "residential"
            }
        ]
    }
}
```

### 2. Recupero Dati Sismici
`GET /inertial_mass/api/data_service.php?lat={lat}&lon={lon}`

Recupera i dati sismici per le coordinate specificate.

### 3. Salvataggio Risultati
`POST /inertial_mass/api/save_results.php`

Salva i risultati del calcolo nel database ASDP.

## Struttura File
```
inertial_mass/
├── api/                    # Servizi API
│   ├── data_service.php    # Recupero dati sismici
│   ├── llm_service.php     # Integrazione Deepseek
│   └── save_results.php    # Salvataggio risultati
├── assets/                 # Risorse frontend
│   ├── css/
│   │   └── modal.css       # Stili modale
│   └── js/
│       └── modal.js        # Logica JavaScript
├── includes/               # File di supporto
│   ├── config.php          # Configurazione
│   └── utils.php           # Funzioni utilità
├── cache/                  # Cache temporanea
├── modal.php               # Componente modale
├── test_integration.php    # Pagina di test (convertita da HTML a PHP)
└── README.md               # Questa documentazione
```

## Sicurezza
- ✅ Autenticazione sessione richiesta
- ✅ Validazione input lato client e server
- ✅ Sanitizzazione dati
- ✅ Rate limiting (10 richieste/minuto)
- ✅ HTTPS raccomandato per API calls

## Troubleshooting

### Errore "Chiave API Deepseek non configurata"
Verifica che la variabile `DEEPSEEK_API_KEY` sia impostata nel file `.env`. La chiave API attualmente configurata è: `sk-88a36ae53c724ffa98572bde336ac865`.

### Errore "Non autorizzato"
Assicurati che l'utente sia autenticato in ASDP prima di aprire la modale.

### Calcoli lenti
- Verifica la connessione internet
- Controlla se la cache è abilitata
- Monitora il rate limiting

## Bug Fix e Miglioramenti Recenti

### v2.1.0 (Gennaio 2025) - Correzioni Critiche
- ✅ **RISOLTO: Raddoppio icone nei risultati**
  - **Problema**: Le icone nei titoli delle sezioni (📊 Distribuzione Forze, 🤖 Analisi AI) apparivano duplicate
  - **Causa**: Mancanza di pulizia del contenuto HTML esistente prima di inserire nuovi risultati
  - **Soluzione**: Aggiunta di `resultsContent.innerHTML = '';` nella funzione `displayResults()`
  - **File modificato**: `assets/js/modal.js` (righe 858, 907-962)

- ✅ **MIGLIORATO: Sistema di rendering risultati**
  - Aggiunto logging dettagliato per debug
  - Ottimizzate le icone della tabella per maggiore chiarezza
  - Prevenzione completa delle duplicazioni HTML

- ✅ **OTTIMIZZATO: Performance interfaccia**
  - Ridotto il tempo di rendering dei risultati
  - Migliorata la fluidità delle animazioni
  - Stabilizzato il sistema di scroll verticale

### Dettagli Tecnici delle Correzioni

#### Problema Raddoppio Icone
```javascript
// PRIMA (problematico)
resultsContent.innerHTML = generateResultsHTML(results);

// DOPO (corretto)
resultsContent.innerHTML = ''; // Pulizia contenuto esistente
const newHTML = generateResultsHTML(results);
resultsContent.innerHTML = newHTML;
```

#### Icone Ottimizzate
- **Tabella Distribuzione Forze**: 🏢 PIANO, ⚖️ MASSA, 📏 ALTEZZA, ⭐ FORZA
- **Sezioni Principali**: 📋 Riepilogo, 📊 Distribuzione, 🤖 Analisi AI

## Stato Implementazione
- ✅ **Database completo**: Tutte le tabelle create e funzionanti
- ✅ **Integrazione ASDP**: Pulsante dedicato nella sezione parametri sismici
- ✅ **API Deepseek**: Configurazione completa e testata
- ✅ **Cache intelligente**: Sistema di cache per ottimizzazione performance
- ✅ **Rate limiting**: Protezione contro abusi API (10 req/min)
- ✅ **Logging completo**: Monitoraggio chiamate API e errori
- ✅ **Gestione errori**: Sistema robusto di error handling
- ✅ **Interfaccia modale**: Caricamento dinamico e responsive
- ✅ **Salvataggio risultati**: Persistenza dati nel database ASDP
- ✅ **Test integrazione**: Validazione completa del modulo
- ✅ **Bug fix critici**: Risolti problemi di duplicazione UI (v2.1.0)

## Sviluppo Futuro
- [ ] Integrazione con altri LLM (OpenAI, Claude)
- [ ] Export risultati in PDF
- [ ] Visualizzazione 3D edificio
- [ ] Calcolo automatico vulnerabilità
- [ ] Integrazione con modulo dissipatori

## Supporto
Per problemi o domande, contattare il team di sviluppo ASDP.
