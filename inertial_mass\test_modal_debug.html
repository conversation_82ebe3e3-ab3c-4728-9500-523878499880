<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Debug Modale Massa Inerziale</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        select, input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .debug-info {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 15px 0;
            font-family: monospace;
            font-size: 12px;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <h1>Test Debug Modale Massa Inerziale</h1>
    
    <div class="debug-info" id="debug-output">
        <strong>Debug Output:</strong><br>
        Inizializzazione in corso...
    </div>
    
    <div class="form-group">
        <label for="im-construction-category">Tipologia Costruttiva *</label>
        <select id="im-construction-category" name="construction_category" required>
            <option value="">Seleziona tipologia...</option>
            <option value="bridge">Ponte/Viadotto</option>
            <option value="building">Edificio</option>
            <option value="prefab_building">Edificio Prefabbricato</option>
        </select>
    </div>

    <div class="form-group" id="structure-subcategory" style="display: none;">
        <label for="im-structure-type">Tipologia Strutturale *</label>
        <select id="im-structure-type" name="structure_type">
            <option value="">Seleziona...</option>
        </select>
    </div>

    <div class="form-group" id="slab-subcategory" style="display: none;">
        <label for="im-slab-type">Tipologia Solaio/Impalcato *</label>
        <select id="im-slab-type" name="slab_type">
            <option value="">Seleziona...</option>
        </select>
    </div>

    <button onclick="testManualChange()">Test Cambio Manuale</button>
    <button onclick="testProgrammaticChange()">Test Cambio Programmatico</button>
    <button onclick="checkEventListeners()">Verifica Event Listeners</button>
    <button onclick="reinitializeHandlers()">Reinizializza Handlers</button>

    <script>
        let debugOutput = document.getElementById('debug-output');
        
        function log(message) {
            console.log(message);
            debugOutput.innerHTML += '<br>' + new Date().toLocaleTimeString() + ': ' + message;
            debugOutput.scrollTop = debugOutput.scrollHeight;
        }

        // Funzioni simulate dal modulo massa inerziale
        function updateStructureSubcategories(category) {
            log('updateStructureSubcategories chiamata con categoria: ' + category);
            
            const subcategoryDiv = document.getElementById('structure-subcategory');
            const structureSelect = document.getElementById('im-structure-type');
            
            if (!subcategoryDiv || !structureSelect) {
                log('ERRORE: Elementi sottocategoria non trovati');
                return;
            }
            
            let options = '';
            
            switch(category) {
                case 'bridge':
                    options = '<option value="prestressed_concrete">Cemento Armato Precompresso</option>';
                    break;
                case 'building':
                    options = `
                        <option value="concrete">Cemento Armato</option>
                        <option value="steel">Acciaio</option>
                        <option value="masonry">Muratura</option>
                        <option value="wood">Legno</option>
                        <option value="mixed">Mista</option>
                    `;
                    break;
                case 'prefab_building':
                    options = '<option value="prestressed_concrete">Cemento Armato Precompresso</option>';
                    break;
                default:
                    log('Nessuna categoria selezionata');
            }
            
            structureSelect.innerHTML = '<option value="">Seleziona...</option>' + options;
            subcategoryDiv.style.display = category ? 'block' : 'none';
            
            if (category) {
                structureSelect.setAttribute('required', 'required');
            } else {
                structureSelect.removeAttribute('required');
            }
            
            structureSelect.value = '';
            log('Sottocategorie strutturali aggiornate - visibile: ' + (category ? 'SI' : 'NO'));
        }

        function updateSlabTypes(category) {
            log('updateSlabTypes chiamata con categoria: ' + category);
            
            const slabDiv = document.getElementById('slab-subcategory');
            const slabSelect = document.getElementById('im-slab-type');
            
            if (!slabDiv || !slabSelect) {
                log('ERRORE: Elementi solaio non trovati');
                return;
            }
            
            let options = '';
            let labelText = 'Tipologia Solaio/Impalcato *';
            
            switch(category) {
                case 'bridge':
                    options = `
                        <option value="prestressed_deck">Impalcato Precompresso</option>
                        <option value="composite_deck">Impalcato Misto</option>
                        <option value="concrete_deck">Impalcato in C.A.</option>
                    `;
                    labelText = 'Tipologia Impalcato *';
                    break;
                case 'building':
                    options = `
                        <option value="hollow_brick">Laterocemento</option>
                        <option value="solid_slab">Soletta Piena</option>
                        <option value="steel_deck">Lamiera Grecata</option>
                        <option value="wood_slab">Legno</option>
                        <option value="prefab">Prefabbricato</option>
                    `;
                    labelText = 'Tipologia Solaio *';
                    break;
                case 'prefab_building':
                    options = `
                        <option value="prestressed_slab">Solaio Precompresso</option>
                        <option value="prefab_panels">Pannelli Prefabbricati</option>
                        <option value="hollow_core">Lastre Alveolari</option>
                    `;
                    labelText = 'Tipologia Solaio *';
                    break;
                default:
                    log('Nessuna categoria selezionata per solai');
            }
            
            slabSelect.innerHTML = '<option value="">Seleziona...</option>' + options;
            slabDiv.style.display = category ? 'block' : 'none';
            
            if (category) {
                slabSelect.setAttribute('required', 'required');
            } else {
                slabSelect.removeAttribute('required');
            }
            
            const label = slabDiv.querySelector('label');
            if (label) {
                label.textContent = labelText;
            }
            
            slabSelect.value = '';
            log('Tipologie solaio aggiornate - visibile: ' + (category ? 'SI' : 'NO'));
        }

        function handleCategoryChange(event) {
            const selectedCategory = event.target.value;
            log('handleCategoryChange: categoria selezionata = ' + selectedCategory);
            updateStructureSubcategories(selectedCategory);
            updateSlabTypes(selectedCategory);
        }

        function initConstructionTypeHandlers() {
            log('initConstructionTypeHandlers: INIZIO');
            
            const categorySelect = document.getElementById('im-construction-category');
            if (categorySelect) {
                // Rimuovi eventuali listener precedenti
                categorySelect.removeEventListener('change', handleCategoryChange);
                
                // Aggiungi il nuovo listener
                categorySelect.addEventListener('change', handleCategoryChange);
                log('Event listener aggiunto con successo');
                return true;
            } else {
                log('ERRORE: Elemento im-construction-category non trovato');
                return false;
            }
        }

        // Funzioni di test
        function testManualChange() {
            log('=== TEST CAMBIO MANUALE ===');
            log('Seleziona manualmente "Edificio" dal menu a tendina');
        }

        function testProgrammaticChange() {
            log('=== TEST CAMBIO PROGRAMMATICO ===');
            const categorySelect = document.getElementById('im-construction-category');
            if (categorySelect) {
                categorySelect.value = 'building';
                categorySelect.dispatchEvent(new Event('change', { bubbles: true }));
                log('Cambio programmatico eseguito');
            } else {
                log('ERRORE: Elemento non trovato per test programmatico');
            }
        }

        function checkEventListeners() {
            log('=== VERIFICA EVENT LISTENERS ===');
            const categorySelect = document.getElementById('im-construction-category');
            if (categorySelect) {
                log('Elemento trovato: ' + categorySelect.id);
                log('Valore corrente: ' + categorySelect.value);
                log('Visibile: ' + (categorySelect.offsetParent !== null));
                
                // Test se l'event listener risponde
                const originalValue = categorySelect.value;
                categorySelect.value = 'building';
                const changeEvent = new Event('change', { bubbles: true });
                categorySelect.dispatchEvent(changeEvent);
                
                setTimeout(() => {
                    const structureDiv = document.getElementById('structure-subcategory');
                    const isVisible = structureDiv && structureDiv.style.display !== 'none';
                    log('Sottocategorie visibili dopo test: ' + (isVisible ? 'SI' : 'NO'));
                    
                    // Ripristina valore originale
                    categorySelect.value = originalValue;
                    categorySelect.dispatchEvent(new Event('change', { bubbles: true }));
                }, 100);
            } else {
                log('ERRORE: Elemento non trovato');
            }
        }

        function reinitializeHandlers() {
            log('=== REINIZIALIZZAZIONE HANDLERS ===');
            initConstructionTypeHandlers();
        }

        // Inizializzazione
        document.addEventListener('DOMContentLoaded', function() {
            log('DOM caricato, inizializzando handlers...');
            initConstructionTypeHandlers();
            log('Inizializzazione completata');
        });
    </script>
</body>
</html>
