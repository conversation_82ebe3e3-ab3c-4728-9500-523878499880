<?php
require_once 'db_config.php';

function logAccess($userId, $action) {
    try {
        $pdo = getConnection();
        
        // Assicurati che la tabella access_logs esista
        createAccessLogsTable($pdo);
        
        $stmt = $pdo->prepare("
            INSERT INTO access_logs (user_id, action, ip_address, user_agent) 
            VALUES (?, ?, ?, ?)
        ");
        
        $stmt->execute([
            $userId,
            $action,
            $_SERVER['REMOTE_ADDR'],
            $_SERVER['HTTP_USER_AGENT']
        ]);
        
        return true;
    } catch (Exception $e) {
        error_log("Errore nella registrazione dell'accesso: " . $e->getMessage());
        return false;
    }
} 