document.addEventListener('DOMContentLoaded', () => {
    const searchBox = document.getElementById('addressSearch');
    const searchContainer = document.querySelector('.search-container');
    const clearSearchBtn = document.getElementById('clearSearch');
    const geolocateBtn = document.getElementById('geolocateBtn');
    const loadingIndicator = document.querySelector('.search-loading');
    
    let searchTimeout;

    // Applico debounce alla funzione di ricerca
    const debouncedSearch = debounce(function(query) {
        // Implementazione della ricerca
        if (query.length < 3) return;
        
        fetch(`api/search.php?q=${encodeURIComponent(query)}`)
            .then(response => response.json())
            .then(data => {
                updateSearchResults(data);
            })
            .catch(error => console.error('Errore nella ricerca:', error));
    }, 300);

    // Event listener per l'input di ricerca
    searchBox.addEventListener('input', (e) => {
        const query = e.target.value.trim();
        debouncedSearch(query);
    });

    // Geolocalizzazione
    geolocateBtn.addEventListener('click', () => {
        if (!navigator.geolocation) {
            alert('La geolocalizzazione non è supportata dal tuo browser.');
            return;
        }
        
        geolocateBtn.disabled = true;
        loadingIndicator.classList.add('show');
        
        navigator.geolocation.getCurrentPosition(
            (position) => {
                const location = {
                    lat: position.coords.latitude,
                    lng: position.coords.longitude
                };
                
                // Geocodifica inversa per ottenere l'indirizzo
                const geocoder = new google.maps.Geocoder();
                geocoder.geocode({ location }, (results, status) => {
                    loadingIndicator.classList.remove('show');
                    geolocateBtn.disabled = false;
                    
                    if (status === google.maps.GeocoderStatus.OK && results[0]) {
                        searchBox.value = results[0].formatted_address;
                        // Emetti evento per la mappa
                        const event = new CustomEvent('addressSelected', {
                            detail: {
                                lat: location.lat,
                                lng: location.lng,
                                address: results[0].formatted_address
                            }
                        });
                        document.dispatchEvent(event);
                    }
                });
            },
            (error) => {
                loadingIndicator.classList.remove('show');
                geolocateBtn.disabled = false;
                
                let message = 'Errore nella geolocalizzazione.';
                switch(error.code) {
                    case error.PERMISSION_DENIED:
                        message = 'Permesso di geolocalizzazione negato.';
                        break;
                    case error.POSITION_UNAVAILABLE:
                        message = 'Posizione non disponibile.';
                        break;
                    case error.TIMEOUT:
                        message = 'Timeout nella richiesta di posizione.';
                        break;
                }
                alert(message);
            }
        );
    });

    // Pulisci ricerca
    clearSearchBtn.addEventListener('click', () => {
        searchBox.value = '';
        searchBox.focus();
    });

    // Chiudi suggerimenti quando si clicca fuori
    document.addEventListener('click', (e) => {
        if (!searchContainer.contains(e.target)) {
            // I suggerimenti sono gestiti da Google Maps
        }
    });

    // Gestione tasti
    searchBox.addEventListener('keydown', (e) => {
        if (e.key === 'Escape') {
            // La chiusura dei suggerimenti è gestita da Google Maps
        }
    });
}); 