<?php
namespace App\Services;

class WMSCatastoService {
    private const BASE_URL = 'https://wms.cartografia.agenziaentrate.gov.it/inspire/wms/ows01.php';
    private const DEFAULT_VERSION = '1.3.0';
    private const DEFAULT_CRS = 'EPSG:4258';
    
    private $logger;
    private $cache;
    
    public function __construct(Logger $logger, CacheService $cache) {
        $this->logger = $logger;
        $this->cache = $cache;
    }
    
    /**
     * Genera URL WMS per GetMap
     */
    public function getMapUrl(array $params): string {
        $defaultParams = [
            'SERVICE' => 'WMS',
            'VERSION' => self::DEFAULT_VERSION,
            'REQUEST' => 'GetMap',
            'CRS' => self::DEFAULT_CRS,
            'STYLES' => 'default',
            'FORMAT' => 'image/png',
            'DPI' => '96',
            'MAP_RESOLUTION' => '96',
            'FORMAT_OPTIONS' => 'dpi:96',
            'TRANSPARENT' => 'TRUE',
            'LAYERS' => 'CP.CadastralZoning,CP.CadastralParcel,fabbricati'
        ];
        
        $params = array_merge($defaultParams, $params);
        return self::BASE_URL . '?' . http_build_query($params);
    }
    
    /**
     * Ottiene informazioni particella
     */
    public function getFeatureInfo(float $lat, float $lon, int $width = 1, int $height = 1): ?array {
        $bbox = $this->calculateBBox($lat, $lon);
        
        $params = [
            'SERVICE' => 'WMS',
            'VERSION' => self::DEFAULT_VERSION,
            'REQUEST' => 'GetFeatureInfo',
            'BBOX' => implode(',', $bbox),
            'CRS' => self::DEFAULT_CRS,
            'WIDTH' => $width,
            'HEIGHT' => $height,
            'QUERY_LAYERS' => 'CP.CadastralParcel',
            'INFO_FORMAT' => 'application/json',
            'I' => 0,
            'J' => 0
        ];
        
        return $this->makeRequest($params);
    }
    
    private function calculateBBox(float $lat, float $lon, float $buffer = 0.0001): array {
        return [
            $lat - $buffer,
            $lon - $buffer,
            $lat + $buffer,
            $lon + $buffer
        ];
    }
    
    private function makeRequest(array $params): ?array {
        $cacheKey = 'catasto:' . md5(serialize($params));
        
        if ($cached = $this->cache->get($cacheKey)) {
            return $cached;
        }
        
        $url = $this->getMapUrl($params);
        
        try {
            $ch = curl_init();
            curl_setopt_array($ch, [
                CURLOPT_URL => $url,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_SSL_VERIFYPEER => true,
                CURLOPT_SSL_VERIFYHOST => 2,
                CURLOPT_TIMEOUT => 10
            ]);
            
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);
            
            if ($httpCode === 200) {
                $data = json_decode($response, true);
                $this->cache->set($cacheKey, $data, 3600);
                return $data;
            }
            
            $this->logger->error('Errore WMS Catasto', [
                'httpCode' => $httpCode,
                'params' => $params
            ]);
            
            return null;
            
        } catch (\Exception $e) {
            $this->logger->error('Eccezione WMS Catasto', [
                'message' => $e->getMessage(),
                'params' => $params
            ]);
            return null;
        }
    }
} 