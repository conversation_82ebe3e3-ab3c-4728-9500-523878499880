<?php
require_once __DIR__ . '/../includes/db_config.php';

// La password che vuoi usare per l'admin
$password = "Admin123!"; // Questa è solo una password di esempio, dovresti cambiarla

// Hash della password
$hashedPassword = password_hash($password, PASSWORD_DEFAULT);

try {
    $conn = getConnection();
    
    // Aggiorna la password dell'admin
    $stmt = $conn->prepare("UPDATE users SET password = ? WHERE username = 'admin'");
    $stmt->execute([$hashedPassword]);
    
    if ($stmt->rowCount() > 0) {
        echo "Password admin aggiornata con successo!\n";
        echo "Username: admin\n";
        echo "Password: " . $password . "\n";
        echo "\nPuoi ora accedere con queste credenziali. Ricordati di cambiare la password al primo accesso!";
    } else {
        echo "Nessun utente admin trovato. Creo un nuovo utente admin...\n";
        
        // Se l'admin non esiste, lo creiamo
        $stmt = $conn->prepare("INSERT INTO users (username, password, email, nome, cognome, role) VALUES (?, ?, ?, ?, ?, 'admin')");
        $stmt->execute(['admin', $hashedPassword, '<EMAIL>', 'Admin', 'ASDP']);
        
        echo "Utente admin creato con successo!\n";
        echo "Username: admin\n";
        echo "Password: " . $password . "\n";
        echo "\nPuoi ora accedere con queste credenziali. Ricordati di cambiare la password al primo accesso!";
    }
} catch (PDOException $e) {
    echo "Errore durante la creazione/aggiornamento dell'admin: " . $e->getMessage();
}
?>
