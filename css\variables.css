:root {
    /* Colors */
    --primary-color: #FF7043;
    --primary-dark: #F4511E;
    --primary-light: #FF8A65;
    --accent-color: #FF5722;
    
    /* Background Colors */
    --background-primary: #FFFFFF;
    --background-secondary: #F5F5F5;
    --background-elevated: #FFFFFF;
    --background-dark: #FAFAFA;
    
    /* Text Colors */
    --text-primary: #333333;
    --text-secondary: #666666;
    --text-on-primary: #FFFFFF;
    
    /* Border & Shadow */
    --border-color: #DDDDDD;
    --shadow-color: rgba(0, 0, 0, 0.1);
    
    /* Spacing */
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 16px;
    --spacing-lg: 24px;
    --spacing-xl: 32px;
    
    /* Border Radius */
    --border-radius-sm: 4px;
    --border-radius: 8px;
    --border-radius-lg: 12px;
    
    /* Transitions */
    --transition-speed: 0.2s;
    --transition-timing: ease-in-out;
    
    /* Stato */
    --success-color: #4caf50;
    --warning-color: #ff9800;
    --danger-color: #f44336;
    --info-color: #2196f3;
    
    /* UI Elements */
    --header-height: 64px;
    --sidebar-width: 280px;
}

/* Dark Theme */
[data-theme="dark"] {
    /* Colors - Keep primary colors for consistency */
    --primary-color: #FF7043;
    --primary-dark: #F4511E;
    --primary-light: #BF360C;
    --accent-color: #FF5722;
    
    /* Background Colors */
    --background-primary: #121212;
    --background-secondary: #1E1E1E;
    --background-elevated: #242424;
    --background-dark: #121212;
    
    /* Text Colors */
    --text-primary: #FAFAFA;
    --text-secondary: #BDBDBD;
    --text-on-primary: #FFFFFF;
    
    /* Border & Shadow */
    --border-color: #333333;
    --shadow-color: rgba(0, 0, 0, 0.3);
}
