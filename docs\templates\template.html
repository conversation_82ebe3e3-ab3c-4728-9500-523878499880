<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Documentazione ASDP</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    {{styles}}
</head>
<body>
    <div class="container">
        {{content}}
    </div>
    <script>
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            securityLevel: 'loose',
            flowchart: {
                curve: 'basis',
                padding: 15,
                nodeSpacing: 50,
                rankSpacing: 50,
                htmlLabels: true,
                useMaxWidth: true
            },
            themeVariables: {
                fontFamily: 'Arial, sans-serif',
                fontSize: '16px',
                primaryColor: '#326ce5',
                primaryTextColor: '#ffffff',
                primaryBorderColor: '#1a56c4',
                lineColor: '#666666',
                secondaryColor: '#f1f1f1',
                tertiaryColor: '#fff'
            }
        });
        
        // Inizializza i diagrammi dopo il caricamento della pagina
        document.addEventListener('DOMContentLoaded', function() {
            mermaid.init(undefined, '.mermaid');
        });
    </script>
    {{scripts}}
</body>
</html> 