<?php
require_once '../includes/db_config.php';
session_start();

// Verifica se l'utente è loggato e ha il ruolo di admin
if (!isset($_SESSION['user_id']) || !isset($_SESSION['role']) || $_SESSION['role'] !== 'admin') {
    header("Location: ../index.php");
    exit;
}

// Funzione per ottenere la lista degli utenti
function getUsers() {
    try {
        $conn = getConnection();
        $stmt = $conn->query("SELECT id, username, nome, cognome, email, role, created_at FROM users ORDER BY created_at DESC");
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        error_log("Errore nel recupero degli utenti: " . $e->getMessage());
        return false;
    }
}

$users = getUsers();

// Includi l'header admin
include '../includes/admin_header.php';
?>

<div class="admin-wrapper">
    <div class="content">
        <h1>Gestione Utenti</h1>
        <p class="subtitle">Gestisci gli utenti del sistema e i loro permessi</p>

        <div class="users-section">
            <!-- Card Controlli -->
            <div class="users-card controls-card">
                <h2><i class="fas fa-user-plus"></i> Gestione Utenti</h2>
                <div class="controls-content">
                    <button class="user-btn" onclick="showAddUserModal()">
                        <i class="fas fa-plus"></i> Aggiungi Nuovo Utente
        </button>
                </div>
    </div>

            <!-- Card Lista Utenti -->
            <div class="users-card">
                <h2><i class="fas fa-users"></i> Lista Utenti</h2>
                <div class="users-content">
        <?php if ($users): ?>
                        <div class="table-container">
            <table class="users-table">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Username</th>
                        <th>Nome</th>
                        <th>Cognome</th>
                        <th>Email</th>
                        <th>Ruolo</th>
                        <th>Data Registrazione</th>
                        <th>Azioni</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($users as $user): ?>
                                        <tr data-user-id="<?php echo htmlspecialchars($user['id']); ?>">
                            <td><?php echo htmlspecialchars($user['id']); ?></td>
                            <td><?php echo htmlspecialchars($user['username']); ?></td>
                            <td><?php echo htmlspecialchars($user['nome']); ?></td>
                            <td><?php echo htmlspecialchars($user['cognome']); ?></td>
                            <td><?php echo htmlspecialchars($user['email']); ?></td>
                            <td><?php echo htmlspecialchars($user['role']); ?></td>
                            <td><?php echo date('d/m/Y H:i', strtotime($user['created_at'])); ?></td>
                            <td class="actions">
                                                <button class="btn-icon edit" onclick="editUser(<?php echo $user['id']; ?>)">
                                    <i class="fas fa-edit"></i>
                                </button>
                                                <?php if ($user['id'] != 2): ?>
                                                <button class="btn-icon delete" onclick="deleteUser(<?php echo $user['id']; ?>)">
                                    <i class="fas fa-trash"></i>
                                </button>
                                                <?php endif; ?>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
                        </div>
        <?php else: ?>
            <div class="no-data">
                <p>Nessun utente trovato</p>
            </div>
        <?php endif; ?>
    </div>
            </div>
        </div>

<!-- Modal Aggiungi Utente -->
<div id="addUserModal" class="modal">
    <div class="modal-content">
                <div class="modal-header">
                    <h2><i class="fas fa-user-plus"></i> Aggiungi Nuovo Utente</h2>
                    <span class="close-modal" onclick="closeModal()">&times;</span>
                </div>
                <div class="modal-body">
        <form id="addUserForm">
            <div class="form-group">
                <label for="username">Username</label>
                <input type="text" id="username" name="username" required>
            </div>
            <div class="form-group">
                <label for="password">Password</label>
                <input type="password" id="password" name="password" required>
            </div>
            <div class="form-group">
                <label for="nome">Nome</label>
                <input type="text" id="nome" name="nome" required>
            </div>
            <div class="form-group">
                <label for="cognome">Cognome</label>
                <input type="text" id="cognome" name="cognome" required>
            </div>
            <div class="form-group">
                <label for="email">Email</label>
                <input type="email" id="email" name="email" required>
            </div>
            <div class="form-group">
                <label for="role">Ruolo</label>
                <select id="role" name="role" required>
                    <option value="user">Utente</option>
                    <option value="admin">Amministratore</option>
                </select>
            </div>
            <div class="form-actions">
                            <button type="submit" class="btn primary">
                                <i class="fas fa-save"></i> Salva
                            </button>
                            <button type="button" class="btn secondary" onclick="closeModal()">
                                <i class="fas fa-times"></i> Annulla
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Modal Modifica Utente -->
        <div id="editUserModal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h2><i class="fas fa-user-edit"></i> Modifica Utente</h2>
                    <span class="close-modal" onclick="closeModal()">&times;</span>
                </div>
                <div class="modal-body">
                    <form id="editUserForm">
                        <input type="hidden" id="edit-id" name="id">
                        <div class="form-group">
                            <label for="edit-username">Username</label>
                            <input type="text" id="edit-username" name="username" required>
                        </div>
                        <div class="form-group">
                            <label for="edit-password">Password (lascia vuoto per non modificare)</label>
                            <input type="password" id="edit-password" name="password">
                        </div>
                        <div class="form-group">
                            <label for="edit-nome">Nome</label>
                            <input type="text" id="edit-nome" name="nome" required>
                        </div>
                        <div class="form-group">
                            <label for="edit-cognome">Cognome</label>
                            <input type="text" id="edit-cognome" name="cognome" required>
                        </div>
                        <div class="form-group">
                            <label for="edit-email">Email</label>
                            <input type="email" id="edit-email" name="email" required>
                        </div>
                        <div class="form-group">
                            <label for="edit-role">Ruolo</label>
                            <select id="edit-role" name="role" required>
                                <option value="user">Utente</option>
                                <option value="admin">Amministratore</option>
                            </select>
                        </div>
                        <div class="form-actions">
                            <button type="submit" class="btn primary">
                                <i class="fas fa-save"></i> Salva Modifiche
                            </button>
                            <button type="button" class="btn secondary" onclick="closeModal()">
                                <i class="fas fa-times"></i> Annulla
                            </button>
            </div>
        </form>
                </div>
            </div>
        </div>
    </div>

    <?php include 'admin_footer.php'; ?>
</div>

<style>
.admin-wrapper {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    background-color: #121212;
}

.content {
    max-width: 1200px;
    margin: 0.5rem auto;
    padding: 0 1rem;
    height: calc(100vh - 120px);
    overflow-y: auto;
    width: 100%;
    position: relative;
    background-color: #121212;
    margin-bottom: 2rem;
}

h1 {
    color: #FF7043;
    font-size: 1.5rem;
    margin: 0 0 0.5rem 0;
    font-weight: 500;
}

.subtitle {
    color: #BDBDBD;
    margin-bottom: 2rem;
    font-size: 0.9rem;
}

.users-section {
    display: grid;
    gap: 1.5rem;
}

.users-card {
    background: #1E1E1E;
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.2);
}

.users-card h2 {
    color: #FF7043;
    font-size: 1.1rem;
    margin: 0 0 1rem 0;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #FF7043;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.users-content {
    color: #FFFFFF;
}

.controls-content {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
}

.user-btn {
    background: #FF7043;
    color: #FFFFFF;
    border: none;
    border-radius: 4px;
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: background-color 0.2s ease;
}

.user-btn:hover {
    background: #F4511E;
}

.table-container {
    overflow-x: auto;
    margin-top: 1rem;
}

.users-table {
    width: 100%;
    border-collapse: collapse;
}

.users-table th,
.users-table td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid #333;
}

.users-table th {
    background: #242424;
    color: #FF7043;
    font-weight: 500;
}

.users-table tr:hover {
    background: #242424;
}

.actions {
    display: flex;
    gap: 0.5rem;
}

.btn-icon {
    background: none;
    border: none;
    color: #BDBDBD;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.btn-icon.edit:hover {
    color: #4CAF50;
    background: rgba(76, 175, 80, 0.1);
}

.btn-icon.delete:hover {
    color: #F44336;
    background: rgba(244, 67, 54, 0.1);
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(18, 18, 18, 0.95);
    z-index: 1000;
}

.modal-content {
    position: relative;
    background: #1E1E1E;
    margin: 2% auto;
    width: 80%;
    max-width: 600px;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.6);
    border: 1px solid #333;
}

.modal-header {
    padding: 1.5rem;
    background: #1a1a1a;
    border-bottom: 2px solid #FF7043;
    border-radius: 8px 8px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h2 {
    color: #FF7043;
    margin: 0;
    font-size: 1.5rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    border: none;
    padding: 0;
}

.close-modal {
    color: #BDBDBD;
    font-size: 1.8rem;
    font-weight: normal;
    cursor: pointer;
    transition: all 0.2s ease;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

.close-modal:hover {
    color: #FF7043;
    background-color: rgba(255, 112, 67, 0.1);
    transform: rotate(90deg);
}

.modal-body {
    padding: 2rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    color: #BDBDBD;
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #333;
    border-radius: 4px;
    background: #242424;
    color: #FFFFFF;
    font-size: 1rem;
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    margin-top: 2rem;
}

.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 4px;
    font-size: 1rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.2s ease;
}

.btn.primary {
    background: #FF7043;
    color: #FFFFFF;
}

.btn.primary:hover {
    background: #F4511E;
}

.btn.secondary {
    background: #424242;
    color: #FFFFFF;
}

.btn.secondary:hover {
    background: #616161;
}

.alert {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    padding: 1rem 2rem;
    border-radius: 4px;
    color: #FFFFFF;
    z-index: 1100;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.2);
}

.alert-success {
    background: #4CAF50;
}

.alert-error {
    background: #F44336;
}

/* Stile della scrollbar */
.content::-webkit-scrollbar {
    width: 8px;
}

.content::-webkit-scrollbar-track {
    background: #1a1a1a;
    border-radius: 4px;
}

.content::-webkit-scrollbar-thumb {
    background: #FF7043;
    border-radius: 4px;
}

.content::-webkit-scrollbar-thumb:hover {
    background: #F4511E;
}

/* Stili per Firefox */
.content {
    scrollbar-width: thin;
    scrollbar-color: #FF7043 #1a1a1a;
}

@media (max-width: 768px) {
    .content {
        height: calc(100vh - 160px);
        padding: 0.5rem;
        margin-bottom: 1rem;
    }

    .users-card {
        padding: 1rem;
    }

    .modal-content {
        width: 95%;
        margin: 5% auto;
    }

    .modal-header {
        padding: 1rem;
    }

    .modal-body {
        padding: 1.5rem;
    }

    .form-actions {
        flex-direction: column;
    }

    .btn {
        width: 100%;
        justify-content: center;
    }
}
</style>

<script>
function showAddUserModal() {
    document.getElementById('addUserModal').style.display = 'block';
    document.getElementById('addUserForm').reset();
}

function closeModal() {
    document.getElementById('addUserModal').style.display = 'none';
    document.getElementById('editUserModal').style.display = 'none';
}

function showMessage(message, isError = false) {
    const messageDiv = document.createElement('div');
    messageDiv.className = `alert ${isError ? 'alert-error' : 'alert-success'}`;
    messageDiv.textContent = message;
    document.body.appendChild(messageDiv);
    
    setTimeout(() => {
        messageDiv.remove();
    }, 5000);
}

function editUser(userId) {
    fetch(`handlers/get_user.php?id=${userId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const user = data.user;
                document.getElementById('edit-id').value = user.id;
                document.getElementById('edit-username').value = user.username;
                document.getElementById('edit-nome').value = user.nome;
                document.getElementById('edit-cognome').value = user.cognome;
                document.getElementById('edit-email').value = user.email;
                document.getElementById('edit-role').value = user.role;
                document.getElementById('editUserModal').style.display = 'block';
            } else {
                showMessage(data.message, true);
            }
        })
        .catch(error => showMessage('Errore durante il recupero dei dati utente', true));
}

function deleteUser(userId) {
    if (confirm('Sei sicuro di voler eliminare questo utente?')) {
        fetch('handlers/delete_user.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ id: userId })
        })
        .then(response => response.json())
        .then(data => {
            showMessage(data.message, !data.success);
            if (data.success) {
                const row = document.querySelector(`tr[data-user-id="${userId}"]`);
                if (row) {
                    row.remove();
                }
            }
        })
        .catch(error => {
            console.error('Errore:', error);
            showMessage('Errore durante l\'eliminazione dell\'utente', true);
        });
    }
}

// Gestione del form di aggiunta utente
document.getElementById('addUserForm').addEventListener('submit', function(e) {
    e.preventDefault();
    const formData = new FormData(this);
    const data = Object.fromEntries(formData.entries());
    
    fetch('handlers/add_user.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        showMessage(data.message, !data.success);
        if (data.success) {
            closeModal();
            location.reload();
        }
    })
    .catch(error => showMessage('Errore durante l\'aggiunta dell\'utente', true));
});

// Gestione del form di modifica utente
document.getElementById('editUserForm').addEventListener('submit', function(e) {
    e.preventDefault();
    const formData = new FormData(this);
    const data = Object.fromEntries(formData.entries());
    
    fetch('handlers/edit_user.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        showMessage(data.message, !data.success);
        if (data.success) {
            closeModal();
            location.reload();
        }
    })
    .catch(error => showMessage('Errore durante la modifica dell\'utente', true));
});

// Chiudi modal quando si clicca fuori
window.onclick = function(event) {
    if (event.target.classList.contains('modal')) {
        closeModal();
    }
}

// Chiudi modal con il tasto ESC
document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape') {
        closeModal();
    }
});
</script>
