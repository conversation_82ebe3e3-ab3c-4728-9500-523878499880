<?php declare(strict_types=1);
/*
 * This file is part of phpunit/php-code-coverage.
 *
 * (c) <PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace <PERSON>\CodeCoverage\Test\TestSize;

/**
 * @immutable
 */
abstract class Known extends TestSize
{
    public function isKnown(): true
    {
        return true;
    }

    abstract public function isGreaterThan(self $other): bool;
}
