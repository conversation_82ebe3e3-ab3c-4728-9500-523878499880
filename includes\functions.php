<?php

function loginUser($username, $password) {
    global $conn;
    
    try {
        // Prepara la query per cercare l'utente
        $stmt = $conn->prepare("SELECT id, username, password, role FROM users WHERE username = ?");
        $stmt->execute([$username]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        // Verifica se l'utente esiste e la password è corretta
        if ($user && password_verify($password, $user['password'])) {
            // Imposta le variabili di sessione
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['username'] = $user['username'];
            $_SESSION['role'] = $user['role'];
            
            // Registra l'accesso nel log
            $logStmt = $conn->prepare("INSERT INTO access_logs (user_id, action) VALUES (?, 'login')");
            $logStmt->execute([$user['id']]);
            
            return false; // Nessun errore
        }
        
        return "Username o password non validi";
    } catch (PDOException $e) {
        error_log("Errore durante il login: " . $e->getMessage());
        return "Si è verificato un errore durante il login";
    }
}

function logoutUser() {
    if (isset($_SESSION['user_id'])) {
        // Registra il logout nel log
        global $conn;
        try {
            $logStmt = $conn->prepare("INSERT INTO access_logs (user_id, action) VALUES (?, 'logout')");
            $logStmt->execute([$_SESSION['user_id']]);
        } catch (PDOException $e) {
            error_log("Errore durante la registrazione del logout: " . $e->getMessage());
        }
    }
    
    // Distrugge tutte le variabili di sessione
    session_unset();
    session_destroy();
}

function isAdmin() {
    return isset($_SESSION['role']) && $_SESSION['role'] === 'admin';
}

function isLoggedIn() {
    return isset($_SESSION['user_id']);
}

function getCurrentUser() {
    if (!isLoggedIn()) {
        return null;
    }
    
    global $conn;
    try {
        $stmt = $conn->prepare("SELECT id, username, role, created_at FROM users WHERE id = ?");
        $stmt->execute([$_SESSION['user_id']]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        error_log("Errore nel recupero dati utente: " . $e->getMessage());
        return null;
    }
}

function formatDateTime($datetime) {
    return date('d/m/Y H:i', strtotime($datetime));
}

function sanitizeInput($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);
    return $data;
}
