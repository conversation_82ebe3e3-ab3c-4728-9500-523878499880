document.addEventListener('DOMContentLoaded', function() {
    const navItems = document.querySelectorAll('.nav-item');
    
    // Attiva la sezione dashboard di default
    const dashboardItem = document.querySelector('[data-section="dashboard"]');
    if (dashboardItem) {
        navItems.forEach(i => i.classList.remove('active'));
        dashboardItem.classList.add('active');
        localStorage.setItem('activeSection', 'dashboard');
    }

    // Gestione click sulle voci del menu
    navItems.forEach(item => {
        item.addEventListener('click', function(e) {
            navItems.forEach(i => i.classList.remove('active'));
            this.classList.add('active');
            localStorage.setItem('activeSection', this.getAttribute('data-section'));
        });
    });
}); 