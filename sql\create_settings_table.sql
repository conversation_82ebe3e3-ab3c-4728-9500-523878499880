-- Creazione della tabella settings
CREATE TABLE IF NOT EXISTS settings (
    `key` VARCHAR(50) PRIMARY KEY,
    `value` TEXT,
    `description` TEXT,
    `type` ENUM('text', 'number', 'boolean', 'json', 'password') DEFAULT 'text',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Inserimento delle impostazioni di default
INSERT INTO settings (`key`, `value`, `description`, `type`) VALUES
('site_name', 'ASDP', 'Nome del sito web', 'text'),
('site_description', 'Sistema di Gestione ASDP', 'Descrizione del sito web', 'text'),
('smtp_host', '', 'Host del server SMTP', 'text'),
('smtp_port', '587', 'Porta del server SMTP', 'number'),
('smtp_user', '', 'Username SMTP', 'text'),
('smtp_pass', '', 'Password SMTP', 'password'),
('backup_retention', '30', 'Giorni di conservazione dei backup', 'number'),
('backup_time', '02:00', 'Ora del backup automatico (formato 24h)', 'text'),
('log_level', 'INFO', 'Livello di logging del sistema', 'text'),
('log_retention', '90', 'Giorni di conservazione dei log', 'number'),
('maintenance_mode', 'false', 'Modalità manutenzione', 'boolean'),
('debug_mode', 'false', 'Modalità debug', 'boolean'),
('max_upload_size', '5242880', 'Dimensione massima upload in bytes', 'number'),
('allowed_file_types', '["jpg","jpeg","png","pdf","doc","docx"]', 'Tipi di file permessi per upload', 'json')
ON DUPLICATE KEY UPDATE 
    `description` = VALUES(`description`),
    `type` = VALUES(`type`);
