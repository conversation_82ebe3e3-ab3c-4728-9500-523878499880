.report-container {
    padding: 20px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.report-section {
    margin-bottom: 25px;
}

.report-title {
    font-size: 1.2em;
    color: #333;
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 2px solid #eee;
}

.report-row {
    display: flex;
    padding: 12px 0;
    border-bottom: 1px solid #f0f0f0;
}

.report-label {
    width: 40%;
    font-weight: 600;
    color: #666;
}

.report-value {
    width: 60%;
    color: #333;
}

/* Stili per evidenziare le zone sismiche */
.zona-sismica {
    padding: 5px 10px;
    border-radius: 4px;
    font-weight: 600;
    margin-top: 5px;
}

.zona-1 { background-color: #ffebee; color: #c62828; }
.zona-2 { background-color: #fff3e0; color: #ef6c00; }
.zona-3 { background-color: #f1f8e9; color: #558b2f; }
.zona-4 { background-color: #e3f2fd; color: #1565c0; }

/* Aggiungi questi stili al tuo file CSS esistente */
.loading {
    padding: 20px;
    text-align: center;
    color: #666;
}

.error-message {
    padding: 15px;
    background-color: #fff3f3;
    border-left: 4px solid #dc3545;
    color: #dc3545;
    margin: 10px 0;
    border-radius: 4px;
}

.error-message i {
    margin-right: 8px;
} 