# Documentazione API
Ultimo aggiornamento: 20/01/2024

## Endpoint Principali

### Calcolo Parametri Sismici
```http
POST /api/calculate_seismic_params
Content-Type: application/json

{
    "lat": number,          // Latitudine
    "lng": number,          // Longitudine
    "nominal_life": number, // Vita nominale
    "use_class": string,    // Classe d'uso
    "soil_category": string,// Categoria sottosuolo
    "topographic_category": string // Categoria topografica
}
```

### Risposta
```json
{
    "success": boolean,
    "data": {
        "ag": number,      // Accelerazione orizzontale massima
        "F0": number,      // Fattore amplificazione spettrale
        "TC": number,      // Periodo inizio tratto velocità costante
        "SS": number,      // Coefficiente amplificazione stratigrafica
        "ST": number,      // Coefficiente amplificazione topografica
        "S": number,       // Coefficiente che tiene conto categoria sottosuolo
        "spectrum": {
            "elastic": [...], // Spettro elastico
            "design": [...]  // Spettro di progetto
        }
    }
}
```

### Gestione Backup
```http
POST /api/backup/create
Content-Type: application/json

{
    "type": string,     // "full" o "partial"
    "description": string
}
```

### Sistema Cache
```http
POST /api/cache/clear
GET /api/cache/status
DELETE /api/cache/{key}
```

## Autenticazione
- Tutte le richieste richiedono un token JWT valido
- Il token va incluso nell'header Authorization
- Formato: `Authorization: Bearer {token}`

## Rate Limiting
- 100 richieste/minuto per IP
- 1000 richieste/giorno per utente
- Header risposta: X-RateLimit-*

## Gestione Errori
### Codici di Errore
- 400: Parametri mancanti o invalidi
- 401: Non autorizzato
- 403: Accesso negato
- 404: Risorsa non trovata
- 429: Troppe richieste
- 500: Errore interno server

### Formato Errori
```json
{
    "success": false,
    "error": {
        "code": number,
        "message": string,
        "details": object
    }
}
```

## Versioning
- Versione corrente: v1
- Base URL: `/api/v1/`
- Deprecation notice via header

## Best Practices
1. Utilizzare HTTPS
2. Implementare retry con exponential backoff
3. Cachare le risposte quando possibile
4. Gestire tutti gli errori
5. Validare input lato client

## Note Tecniche
- Content-Type: application/json
- Charset: UTF-8
- Timezone: UTC
- Date format: ISO 8601

## Esempi
### Calcolo Sismico Base
```javascript
fetch('/api/v1/calculate_seismic_params', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + token
    },
    body: JSON.stringify({
        lat: 41.902782,
        lng: 12.496366,
        nominal_life: 50,
        use_class: "II",
        soil_category: "B",
        topographic_category: "T1"
    })
})
```

## Changelog API
### v1.1.0 (20/01/2024)
- Aggiunto endpoint gestione backup
- Implementato sistema cache
- Migliorato rate limiting
- Aggiunta documentazione errori

### v1.0.9 (15/01/2024)
- Ottimizzato calcolo sismico
- Aggiunto sistema report
- Migliorata gestione errori

## Endpoint Disponibili

### 1. Calcolo Parametri Sismici
```
POST /api/calculate_seismic_params.php

Request:
{
    "lat": number,          // Latitudine del punto
    "lng": number,          // Longitudine del punto
    "nominalLife": number,  // Vita nominale (anni)
    "buildingClass": string,// Classe edificio (I, II, III, IV)
    "soilCategory": string, // Categoria terreno (A, B, C, D, E)
    "topographicCategory": string // Categoria topografica (T1, T2, T3, T4)
}

Response:
{
    "success": boolean,
    "data": {
        "SLO": {
            "TR": number,   // Tempo di ritorno
            "ag": number,   // Accelerazione al suolo
            "F0": number,   // Fattore amplificazione
            "TC": number    // Periodo TC*
        },
        "SLD": {...},
        "SLV": {...},
        "SLC": {...}
    },
    "message": string      // Messaggio di errore (se success = false)
}

### Endpoint: `/api/cadastral_data.php`

#### Richiesta
```http
POST /api/cadastral_data.php
Content-Type: application/json

{
    "lat": 41.809926,
    "lng": 12.613875
}
```

#### Parametri
- **lat** (float): Latitudine del punto
- **lng** (float): Longitudine del punto

#### Risposta
```json
{
    "comune": "H501",
    "sezione": "C", 
    "foglio": "1012",
    "allegato": "B",
    "sviluppo": "0",
    "particella": "1936",
    "codice_completo": "H501C1012B0.1936"
}
```

#### Note
- L'endpoint utilizza il servizio WMS dell'Agenzia delle Entrate
- Le coordinate devono essere nel sistema WGS84 (EPSG:4326)
- Il servizio restituisce i dati catastali della particella selezionata
- In caso di errore o particella non trovata, i campi avranno valore "-"

### Endpoint: `/api/catasto_proxy.php`

#### Richiesta
```http
GET /api/catasto_proxy.php?SERVICE=WMS&VERSION=1.3.0&REQUEST=GetFeatureInfo&...
```

#### Parametri
- Tutti i parametri standard del servizio WMS GetFeatureInfo
- Le coordinate devono essere nel sistema EPSG:4258 (ETRS89)

#### Risposta
- Formato HTML con i dati catastali come restituiti dal servizio WMS
- In caso di errore, viene restituito un messaggio di errore appropriato

### Endpoint: `/api/get_boundaries.php`

#### Richiesta
```http
GET /api/get_boundaries.php?comune=Roma
```

#### Parametri
- **comune** (string): Nome del comune

#### Risposta
```json
{
    "success": true,
    "data": {
        "type": "Feature",
        "properties": {
            "nome": "Roma",
            "provincia": "RM",
            "regione": "Lazio"
        },
        "geometry": {
            "type": "Polygon",
            "coordinates": [
                [
                    [12.234, 41.789],
                    [12.567, 41.923],
                    [12.789, 41.856],
                    [12.234, 41.789]
                ]
            ]
        }
    }
}
```

## Note Tecniche

### Autenticazione
- Tutte le API richiedono un token JWT valido
- Il token va passato nell'header `Authorization`
- Formato: `Bearer <token>`

### Rate Limiting
- Max 100 richieste/minuto per IP
- Max 1000 richieste/ora per utente
- Status 429 se limite superato

### Caching
- Risultati cachati per 1 ora
- Header `Cache-Control` gestito automaticamente
- Possibilità di forzare refresh

### CORS
- Abilitato per domini autorizzati
- Metodi permessi: GET, POST
- Credenziali accettate 