import SeismicCalculator from './SeismicCalculator.js';

/**
 * Classe per gestire l'interfaccia utente del calcolo sismico
 */
class SeismicUI {
    constructor() {
        this.calculator = new SeismicCalculator();
        this.setupEventListeners();
        console.log('SeismicUI inizializzato');
    }

    /**
     * Inizializza gli event listener
     */
    setupEventListeners() {
        // Ascolta i cambiamenti nei parametri di input
        const inputs = [
            'nominal-life',
            'building-class',
            'soil-category',
            'topographic-category',
            'damping',
            'q-factor'
        ];

        inputs.forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                element.addEventListener('change', (e) => {
                    console.log(`Parametro ${id} cambiato:`, e.target.value);
                    this.updateResults();
                });
            } else {
                console.warn(`Elemento ${id} non trovato`);
            }
        });

        // Pulsante di ricalcolo
        const recalcBtn = document.getElementById('recalculateBtn');
        if (recalcBtn) {
            recalcBtn.addEventListener('click', () => {
                console.log('Avvio ricalcolo...');
                this.recalculate();
            });
        } else {
            console.warn('Pulsante ricalcolo non trovato');
        }
    }

    /**
     * Recupera i parametri di input dall'interfaccia
     */
    getInputParameters() {
        try {
            const params = {
                nominalLife: parseInt(document.getElementById('nominal-life')?.value),
                buildingClass: document.getElementById('building-class')?.value,
                soilCategory: document.getElementById('soil-category')?.value,
                topographicCategory: document.getElementById('topographic-category')?.value,
                damping: parseFloat(document.getElementById('damping')?.value || '5.0'),
                qFactor: parseFloat(document.getElementById('q-factor')?.value || '1.0'),
                lat: parseFloat(document.getElementById('latitude')?.value),
                lng: parseFloat(document.getElementById('longitude')?.value)
            };

            console.log('Parametri di input recuperati:', params);

            // Validazione
            const validations = [
                { field: 'nominalLife', message: 'Seleziona la vita nominale' },
                { field: 'buildingClass', message: 'Seleziona la classe d\'uso' },
                { field: 'soilCategory', message: 'Seleziona la categoria di sottosuolo' },
                { field: 'topographicCategory', message: 'Seleziona la categoria topografica' }
            ];

            for (const validation of validations) {
                if (!params[validation.field]) {
                    console.error(`Validazione fallita: ${validation.message}`);
                    throw new Error(validation.message);
                }
            }

            if (isNaN(params.lat) || isNaN(params.lng)) {
                console.error('Coordinate non valide:', { lat: params.lat, lng: params.lng });
                throw new Error('Seleziona un punto sulla mappa');
            }

            return params;
        } catch (error) {
            console.error('Errore nel recupero dei parametri:', error);
            throw error;
        }
    }

    /**
     * Aggiorna i risultati nell'interfaccia
     */
    updateUI(results) {
        try {
            console.log('Aggiornamento UI con risultati:', results);

            // Aggiorna i parametri sismici per ogni stato limite
            ['SLO', 'SLD', 'SLV', 'SLC'].forEach(state => {
                const params = results[state];
                if (!params) {
                    console.warn(`Parametri mancanti per lo stato ${state}`);
                    return;
                }

                console.log(`Aggiornamento parametri per ${state}:`, params);

                // Aggiorna i valori nella tabella
                const fields = ['tr', 'ag', 'f0', 'tc'];
                fields.forEach(field => {
                    const element = document.getElementById(`${state.toLowerCase()}-${field}`);
                    if (element) {
                        const value = field === 'tr' ? params.TR : params[field.toUpperCase()];
                        element.textContent = typeof value === 'number' ? value.toFixed(3) : value;
                    } else {
                        console.warn(`Elemento ${state.toLowerCase()}-${field} non trovato`);
                    }
                });
            });

            // Aggiorna il grafico dello spettro se presente
            this.updateSpectrumPlot(results);

            console.log('Interfaccia aggiornata con successo');
        } catch (error) {
            console.error('Errore nell\'aggiornamento dell\'interfaccia:', error);
            throw error;
        }
    }

    /**
     * Aggiorna il grafico dello spettro
     * @param {Object} results - Risultati del calcolo
     */
    updateSpectrumPlot(results) {
        // TODO: Implementare il plotting dello spettro
        console.log('Aggiornamento grafico spettro:', results);
    }

    /**
     * Ricalcola i parametri sismici
     */
    async recalculate() {
        try {
            const params = this.getInputParameters();
            console.log('Avvio calcolo con parametri:', params);
            
            // Effettua la chiamata API per il calcolo
            const response = await fetch('api/calculate_seismic_params.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(params)
            });

            const results = await response.json();
            console.log('Risposta API:', results);
            
            if (results.success) {
                this.updateUI(results.data);
                console.log('Ricalcolo completato con successo');
            } else {
                console.error('Errore API:', results.message);
                throw new Error(results.message || 'Errore nel ricalcolo dei parametri sismici');
            }
        } catch (error) {
            console.error('Errore nel ricalcolo:', error);
            alert(`Errore nel ricalcolo: ${error.message}`);
            throw error;
        }
    }
}

// Esporta la classe per l'uso in altri moduli
export default SeismicUI; 