-- <PERSON><PERSON> per i parametri di calcolo
CREATE TABLE IF NOT EXISTS `calculation_parameters` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `calculation_id` int(11) DEFAULT NULL,
  `parameter_type` varchar(10) DEFAULT NULL,
  `tr` int(11) DEFAULT NULL,
  `ag` decimal(10,6) DEFAULT NULL,
  `f0` decimal(10,6) DEFAULT NULL,
  `tc_star` decimal(10,6) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `calculation_id` (`calculation_id`),
  CONSTRAINT `calculation_parameters_ibfk_1` FOREIGN KEY (`calculation_id`) REFERENCES `calculation_log` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- <PERSON><PERSON> per gli spettri di calcolo
CREATE TABLE IF NOT EXISTS `calculation_spectra` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `calculation_id` int(11) DEFAULT NULL,
  `parameter_type` varchar(10) DEFAULT NULL,
  `spectrum_data` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`spectrum_data`)),
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `calculation_id` (`calculation_id`),
  CONSTRAINT `calculation_spectra_ibfk_1` FOREIGN KEY (`calculation_id`) REFERENCES `calculation_log` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci; 