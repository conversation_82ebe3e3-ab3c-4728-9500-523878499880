<?php
// Script per minificare i file JavaScript

require_once __DIR__ . '/../vendor/autoload.php';

use JShrink\Minifier;

function minifyJS($source, $destination) {
    if (!file_exists($source)) {
        echo "ERRORE: File sorgente non trovato: $source\n";
        return false;
    }

    // Crea la directory di destinazione se non esiste
    $destDir = dirname($destination);
    if (!file_exists($destDir)) {
        mkdir($destDir, 0777, true);
    }

    try {
        $js = file_get_contents($source);
        $minified = Minifier::minify($js, ['flaggedComments' => false]);
        file_put_contents($destination, $minified);
        
        $originalSize = filesize($source);
        $minifiedSize = filesize($destination);
        $savings = round((($originalSize - $minifiedSize) / $originalSize) * 100, 2);
        
        echo "Minificato: $source\n";
        echo "- Dimensione originale: " . round($originalSize/1024, 2) . " KB\n";
        echo "- Dimensione minificata: " . round($minifiedSize/1024, 2) . " KB\n";
        echo "- Risparmio: $savings%\n";
        echo "✓ Salvato in: $destination\n\n";
        return true;
    } catch (Exception $e) {
        echo "ERRORE durante la minificazione di $source: " . $e->getMessage() . "\n";
        return false;
    }
}

// File da minificare
$files = [
    ['source' => __DIR__ . '/../js/helpers.js', 'dest' => __DIR__ . '/../js/min/helpers.min.js'],
    ['source' => __DIR__ . '/../js/map.js', 'dest' => __DIR__ . '/../js/min/map.min.js'],
    ['source' => __DIR__ . '/../js/ui.js', 'dest' => __DIR__ . '/../js/min/ui.min.js'],
    ['source' => __DIR__ . '/../js/search.js', 'dest' => __DIR__ . '/../js/min/search.min.js'],
    ['source' => __DIR__ . '/../js/account.js', 'dest' => __DIR__ . '/../js/min/account.min.js']
];

echo "=== Inizio Minificazione JavaScript ===\n\n";

// Crea la directory min se non esiste
if (!file_exists(__DIR__ . '/../js/min')) {
    mkdir(__DIR__ . '/../js/min', 0777, true);
    echo "✓ Creata directory js/min\n\n";
}

$allSuccess = true;
foreach ($files as $file) {
    if (!minifyJS($file['source'], $file['dest'])) {
        $allSuccess = false;
    }
}

if ($allSuccess) {
    echo "\n✓ Tutti i file sono stati minificati con successo!\n";
} else {
    echo "\n✗ Si sono verificati degli errori durante la minificazione.\n";
}

echo "\n=== Fine Minificazione ===\n"; 