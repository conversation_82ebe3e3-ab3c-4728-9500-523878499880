{"name": "erusev/parsedown-extra", "description": "An extension of Parsedown that adds support for Markdown Extra.", "keywords": ["markdown", "markdown extra", "parser", "parsedown"], "homepage": "https://github.com/erusev/parsedown-extra", "type": "library", "license": "MIT", "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://erusev.com"}], "require": {"erusev/parsedown": "^1.7.4"}, "require-dev": {"phpunit/phpunit": "^4.8.35"}, "autoload": {"psr-0": {"ParsedownExtra": ""}}, "autoload-dev": {"psr-0": {"TestParsedown": "test/", "ParsedownExtraTest": "test/", "ParsedownTest": "vendor/erusev/parsedown/test/", "CommonMarkTest": "vendor/erusev/parsedown/test/", "CommonMarkTestWeak": "vendor/erusev/parsedown/test/"}}}