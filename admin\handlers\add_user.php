<?php
require_once '../../includes/db_config.php';
session_start();

// Verifica accesso admin
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Accesso non autorizzato']);
    exit;
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $data = json_decode(file_get_contents('php://input'), true);
        
        // Validazione dati
        if (!isset($data['username']) || !isset($data['password']) || !isset($data['email'])) {
            throw new Exception('Dati mancanti');
        }

        $conn = getConnection();
        
        // Verifica se username esiste già
        $stmt = $conn->prepare("SELECT id FROM users WHERE username = ?");
        $stmt->execute([$data['username']]);
        if ($stmt->fetch()) {
            throw new Exception('Username già in uso');
        }

        // Inserimento nuovo utente
        $stmt = $conn->prepare("INSERT INTO users (username, password, nome, cognome, email, role) VALUES (?, ?, ?, ?, ?, ?)");
        $hashedPassword = password_hash($data['password'], PASSWORD_DEFAULT);
        
        $stmt->execute([
            $data['username'],
            $hashedPassword,
            $data['nome'],
            $data['cognome'],
            $data['email'],
            $data['role'] ?? 'user'
        ]);

        echo json_encode(['success' => true, 'message' => 'Utente creato con successo']);
    } catch (Exception $e) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
} 