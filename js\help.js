document.addEventListener('DOMContentLoaded', function() {
    // Elementi DOM
    const helpLink = document.querySelector('[data-section="help"]');
    const overlay = document.createElement('div');
    overlay.className = 'help-overlay';
    document.body.appendChild(overlay);

    // Carica il contenuto della guida
    function loadHelpContent() {
        fetch('help.php')
            .then(response => response.text())
            .then(html => {
                overlay.innerHTML = html;
                overlay.classList.add('active');
                
                // Gestione chiusura
                const closeBtn = overlay.querySelector('#closeHelp');
                if (closeBtn) {
                    closeBtn.addEventListener('click', closeHelp);
                }

                // Chiudi con ESC
                document.addEventListener('keydown', handleEscKey);
                
                // <PERSON>udi cliccando fuori dal popup
                overlay.addEventListener('click', handleOverlayClick);
            })
            .catch(error => console.error('Errore nel caricamento della guida:', error));
    }

    // Funzione per chiudere il popup
    function closeHelp() {
        overlay.classList.remove('active');
        document.removeEventListener('keydown', handleEscKey);
        overlay.removeEventListener('click', handleOverlayClick);
    }

    // Gestione tasto ESC
    function handleEscKey(e) {
        if (e.key === 'Escape') {
            closeHelp();
        }
    }

    // Gestione click fuori dal popup
    function handleOverlayClick(e) {
        if (e.target === overlay) {
            closeHelp();
        }
    }

    // Event listener per il link della guida
    if (helpLink) {
        helpLink.addEventListener('click', function(e) {
            e.preventDefault();
            loadHelpContent();
        });
    }
}); 