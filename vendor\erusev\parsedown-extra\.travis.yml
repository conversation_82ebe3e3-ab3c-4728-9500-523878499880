language: php

dist: trusty
sudo: false

matrix:
  include:
    - php: 5.3
      dist: precise
    - php: 5.4
    - php: 5.5
    - php: 5.6
    - php: 7.0
    - php: 7.1
    - php: 7.2
    - php: 7.3
    - php: 7.4
    - php: nightly
  fast_finish: true
  allow_failures:
    - php: nightly
    - php: hhvm-nightly

install:
  - composer install --prefer-source

script:
  - vendor/bin/phpunit
  - vendor/bin/phpunit vendor/erusev/parsedown/test/CommonMarkTestWeak.php || true
  - '[ -z "$TRAVIS_TAG" ] || [ "$TRAVIS_TAG" == "$(php -r "require(\"ParsedownExtra.php\"); echo ParsedownExtra::version;")" ]'
