# 13. Flussi di Lavoro Comuni

## 1. Nuovo Calcolo Sismico

### Accesso Dashboard
```php
// Verifica sessione
session_start();
if (!isset($_SESSION['user_id'])) {
    header('Location: /login.php');
    exit;
}

// Verifica permessi
$user = new User($db);
if (!$user->hasPermission('calculate_seismic')) {
    throw new PermissionException('Accesso non autorizzato');
}
```

### Selezione Punto
```javascript
// Inizializzazione mappa
const map = L.map('map').setView([41.9028, 12.4964], 6);
L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png').addTo(map);

// Click sulla mappa
map.on('click', async e => {
    const { lat, lng } = e.latlng;
    
    // Aggiorna marker
    if (currentMarker) map.removeLayer(currentMarker);
    currentMarker = L.marker([lat, lng]).addTo(map);
    
    // Recupera informazioni punto
    const info = await getPointInfo(lat, lng);
    updateInfoPanel(info);
});
```

### Calcolo Parametri
```javascript
// Form submit
document.getElementById('calcForm').addEventListener('submit', async e => {
    e.preventDefault();
    
    // Raccolta dati
    const data = {
        lat: document.getElementById('lat').value,
        lon: document.getElementById('lon').value,
        vitaNominale: document.getElementById('vn').value,
        classeUso: document.getElementById('cu').value,
        categoriaTerreno: document.getElementById('ct').value,
        categoriaTopografica: document.getElementById('ctop').value
    };
    
    try {
        // Calcolo
        const result = await calculateSeismicParams(data);
        
        // Visualizza risultati
        showResults(result);
        
        // Salva storico
        saveCalculation(result);
    } catch (error) {
        showError('Errore nel calcolo: ' + error.message);
    }
});
```

## 2. Gestione Backup

### Backup Database
```powershell
# Script backup.ps1
$date = Get-Date -Format "yyyyMMdd"
$backupPath = "C:\xampp\htdocs\asdp\backup"
$dbName = "asdp_db"

# Backup database
mysqldump -u root -p $dbName > "$backupPath\db_$date.sql"

# Compressione
Compress-Archive -Path "$backupPath\db_$date.sql" -DestinationPath "$backupPath\db_$date.zip"

# Pulizia vecchi backup
Get-ChildItem $backupPath -Filter "db_*.zip" | 
    Where-Object { $_.CreationTime -lt (Get-Date).AddDays(-30) } |
    Remove-Item
```

### Backup File
```powershell
# Script backup_files.ps1
$date = Get-Date -Format "yyyyMMdd"
$sourcePath = "C:\xampp\htdocs\asdp"
$backupPath = "C:\backup\asdp"

# Cartelle da escludere
$exclude = @(
    "node_modules",
    "vendor",
    "temp",
    "logs"
)

# Backup incrementale
robocopy $sourcePath "$backupPath\$date" /MIR /XD $exclude /LOG:"$backupPath\backup_$date.log"
```

### Restore
```powershell
# Script restore.ps1
param(
    [string]$backupDate,
    [string]$backupType
)

if ($backupType -eq "db") {
    # Restore database
    mysql -u root -p asdp_db < "backup\db_$backupDate.sql"
} elseif ($backupType -eq "files") {
    # Restore files
    robocopy "C:\backup\asdp\$backupDate" "C:\xampp\htdocs\asdp" /E /IS /IT
}
```

## 3. Gestione Errori Comuni

### Database Connection
```php
// Gestione connessione
try {
    $db = new PDO(
        "mysql:host={$config['db_host']};dbname={$config['db_name']}",
        $config['db_user'],
        $config['db_pass']
    );
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    // Log errore
    error_log("DB Connection Error: " . $e->getMessage());
    
    // Notifica admin
    $mailer = new Mailer();
    $mailer->sendAlert(
        "Database Error",
        "Connection failed: " . $e->getMessage()
    );
    
    // Mostra errore utente
    throw new DatabaseException("Errore di connessione al database");
}
```

### File Permissions
```php
// Verifica permessi
function checkPermissions($path) {
    $required = [
        'logs' => 0777,
        'temp' => 0777,
        'uploads' => 0777
    ];
    
    foreach ($required as $dir => $perm) {
        $fullPath = $path . DIRECTORY_SEPARATOR . $dir;
        $currentPerm = fileperms($fullPath) & 0777;
        
        if ($currentPerm !== $perm) {
            throw new PermissionException(
                "Permessi errati per $dir: " . 
                decoct($currentPerm) . " invece di " . decoct($perm)
            );
        }
    }
}
```

### API Errors
```php
// Middleware gestione errori
class ErrorMiddleware {
    public function handle($request, $next) {
        try {
            return $next($request);
        } catch (ValidationException $e) {
            return $this->error(400, $e->getMessage());
        } catch (AuthException $e) {
            return $this->error(401, $e->getMessage());
        } catch (NotFoundException $e) {
            return $this->error(404, $e->getMessage());
        } catch (Exception $e) {
            error_log($e->getMessage());
            return $this->error(500, "Errore interno del server");
        }
    }
    
    private function error($code, $message) {
        http_response_code($code);
        return ['error' => true, 'message' => $message];
    }
}
```

## 4. Manutenzione Periodica

### Giornaliera
```powershell
# Script daily_maintenance.ps1

# 1. Pulizia log
Get-ChildItem "logs" -Filter "*.log" |
    Where-Object { $_.LastWriteTime -lt (Get-Date).AddDays(-7) } |
    Remove-Item

# 2. Verifica spazio
$drive = Get-PSDrive C
if ($drive.Free/1GB -lt 5) {
    Send-MailMessage -Subject "Spazio disco insufficiente"
}

# 3. Backup
.\backup.ps1
```

### Settimanale
```sql
-- Script weekly_maintenance.sql

-- 1. Ottimizzazione tabelle
OPTIMIZE TABLE users, comuni, catasto_info;

-- 2. Analisi tabelle
ANALYZE TABLE users, comuni, catasto_info;

-- 3. Pulizia dati temporanei
DELETE FROM temp_calculations WHERE created_at < DATE_SUB(NOW(), INTERVAL 7 DAY);
```

### Mensile
```php
// Script monthly_maintenance.php

// 1. Verifica integrità
function checkIntegrity() {
    global $db;
    
    // Check foreign keys
    $stmt = $db->query("
        SELECT TABLE_NAME, CONSTRAINT_NAME
        FROM information_schema.TABLE_CONSTRAINTS
        WHERE CONSTRAINT_TYPE = 'FOREIGN KEY'
    ");
    
    while ($row = $stmt->fetch()) {
        // Verifica vincoli
        $db->query("
            ALTER TABLE {$row['TABLE_NAME']}
            CHECK CONSTRAINT {$row['CONSTRAINT_NAME']}
        ");
    }
}

// 2. Report statistiche
function generateStats() {
    global $db;
    
    $stats = [
        'users' => $db->query("SELECT COUNT(*) FROM users")->fetchColumn(),
        'calculations' => $db->query("SELECT COUNT(*) FROM calculations")->fetchColumn(),
        'disk_usage' => disk_free_space('/') / disk_total_space('/') * 100
    ];
    
    // Invia report
    $mailer = new Mailer();
    $mailer->sendReport('Monthly Stats', $stats);
}
```

## 5. Best Practices

### Logging
```php
// Logger configurabile
class Logger {
    private static $instance;
    private $handlers = [];
    
    public static function getInstance() {
        if (!self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    public function addHandler($handler) {
        $this->handlers[] = $handler;
    }
    
    public function log($level, $message, array $context = []) {
        $entry = [
            'timestamp' => date('Y-m-d H:i:s'),
            'level' => $level,
            'message' => $message,
            'context' => $context
        ];
        
        foreach ($this->handlers as $handler) {
            $handler->handle($entry);
        }
    }
}
```

### Security
```php
// Security helper
class Security {
    public static function sanitizeInput($input) {
        if (is_array($input)) {
            return array_map([self::class, 'sanitizeInput'], $input);
        }
        return htmlspecialchars($input, ENT_QUOTES, 'UTF-8');
    }
    
    public static function validateToken($token) {
        try {
            $decoded = JWT::decode($token, getenv('JWT_SECRET'), ['HS256']);
            return $decoded->exp > time();
        } catch (Exception $e) {
            return false;
        }
    }
    
    public static function generateCSRF() {
        if (empty($_SESSION['csrf_token'])) {
            $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
        }
        return $_SESSION['csrf_token'];
    }
}
```

### Performance
```php
// Cache helper
class Cache {
    private $redis;
    
    public function __construct() {
        $this->redis = new Redis();
        $this->redis->connect('127.0.0.1', 6379);
    }
    
    public function remember($key, $ttl, callable $callback) {
        if ($value = $this->redis->get($key)) {
            return unserialize($value);
        }
        
        $value = $callback();
        $this->redis->setex($key, $ttl, serialize($value));
        return $value;
    }
    
    public function flush($pattern = '*') {
        $keys = $this->redis->keys($pattern);
        foreach ($keys as $key) {
            $this->redis->del($key);
        }
    }
}
```

## 2. Ricerca Dati Catastali

### Inizializzazione Layer WMS
```javascript
// Configurazione layer catastale
const wmsUrl = 'https://wms.cartografia.agenziaentrate.gov.it/inspire/wms/ows01.php';
const wmsLayer = L.tileLayer.wms(wmsUrl, {
    layers: 'CP.CadastralParcel',
    styles: 'default',
    format: 'image/png',
    transparent: true,
    version: '1.3.0',
    crs: L.CRS.EPSG4326,
    minZoom: 16,
    maxZoom: 20
});

// Aggiunta layer alla mappa
map.addLayer(wmsLayer);
```

### Ricerca per Click
```javascript
// Gestione click sulla mappa
map.on('click', async e => {
    const { lat, lng } = e.latlng;
    
    try {
        // Richiesta dati catastali
        const data = await getCadastralInfo(lat, lng);
        
        // Aggiornamento form
        document.getElementById('catasto_comune').value = data.comune;
        document.getElementById('catasto_foglio').value = data.foglio;
        document.getElementById('catasto_particella').value = data.particella;
        
        // Log risultato
        console.log('Dati catastali:', data);
    } catch (error) {
        console.error('Errore recupero dati catastali:', error);
    }
});
```

### Funzione Recupero Dati
```javascript
// Recupero dati catastali
async function getCadastralInfo(lat, lng) {
    // Verifica zoom minimo
    if (map.getZoom() < 16) {
        throw new Error('Zoom insufficiente per dati catastali');
    }
    
    // Chiamata API
    const response = await fetch('/api/cadastral_data.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ lat, lng })
    });
    
    // Verifica risposta
    if (!response.ok) {
        throw new Error('Errore recupero dati catastali');
    }
    
    return await response.json();
}
```

### Gestione Errori
```javascript
// Gestione errori catastali
function handleCadastralError(error) {
    // Log errore
    console.error('Errore catastale:', error);
    
    // Messaggio utente
    const message = error.message || 'Errore recupero dati catastali';
    alert(message);
    
    // Reset form
    document.getElementById('catasto_comune').value = '';
    document.getElementById('catasto_foglio').value = '';
    document.getElementById('catasto_particella').value = '';
}
```

# Flussi di Lavoro

## Dashboard Amministrativa

### Monitoraggio Sistema
1. **Controllo Performance**
   - Verifica metriche sistema ogni 30 minuti
   - Analisi log errori
   - Monitoraggio spazio disco
   - Controllo stato servizi

2. **Gestione Backup**
   - Backup automatico giornaliero (03:00)
   - Verifica integrità backup
   - Pulizia backup obsoleti
   - Notifica risultati backup

3. **Sicurezza**
   - Monitoraggio accessi sospetti
   - Verifica tentativi login falliti
   - Controllo permessi file
   - Scan vulnerabilità

4. **Manutenzione**
   - Ottimizzazione database settimanale
   - Pulizia file temporanei
   - Aggiornamento cache
   - Verifica integrità dati

### Procedure di Emergenza
1. **Problemi Performance**
   - Analisi log errori
   - Restart servizi critici
   - Pulizia cache
   - Notifica amministratori

2. **Violazioni Sicurezza**
   - Blocco IP sospetti
   - Reset password compromesse
   - Backup dati critici
   - Report dettagliato
``` 