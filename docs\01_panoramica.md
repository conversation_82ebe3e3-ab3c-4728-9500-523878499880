# 1. Panoramica del Sistema

## Informazioni per Nuova Sessione Chat

### Ambiente di Sviluppo
- **OS**: Windows 10
- **Server**: XAMPP v3.3.0
- **Path Workspace**: /c%3A/xampp/htdocs/asdp
- **Shell**: C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe

### Regole Base
1. **Lingua**: Tutte le risposte devono essere in italiano
2. **Livello Tecnico**: Spiegazioni semplici, non sono un programmatore
3. **Modifiche**: 
   - Fare modifiche graduali
   - Non modificare tutto il codice insieme
   - Evitare problemi con l'editor

### Gestione File
1. **Prima di Creare File**:
   - Controllare se esiste nella root
   - Controllare nelle sottocartelle
   - Aggiornare app_map.md se si creano/eliminano file

2. **Layout**:
   - Non modificare il layout esistente
   - Rispettare la struttura attuale
   - Chiedere prima di fare modifiche al layout

### Cartelle Principali da Monitorare
1. `/js` - File JavaScript e funzioni
2. `/includes` - File PHP e configurazioni
3. `/api` - Endpoint e servizi
4. `/logs` - File di log per debug

### File Critici
1. `home.php` - Dashboard principale
2. `map.js` - Gestione mappa
3. `db_config.php` - Configurazione database
4. `.env` - Variabili ambiente

### Procedure di Debug
1. Controllare `/logs/error.log`
2. Verificare console browser (F12)
3. Testare API con Postman/cURL
4. Verificare permessi file

### Note sulla PWA
- In fase di sviluppo
- Non modificare manifest.json
- Service Worker in test
- Cache da implementare 