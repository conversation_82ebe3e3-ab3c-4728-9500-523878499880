<?php
require_once '../includes/db_config.php';

try {
    // Password corretta per l'admin
    $correctPassword = 'Admin123!';
    $hashedPassword = password_hash($correctPassword, PASSWORD_DEFAULT);

    // Verifica se l'utente admin esiste
    $stmt = $conn->prepare("SELECT id, username, role FROM users WHERE username = ?");
    $stmt->execute(['admin']);
    $admin = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($admin) {
        // Aggiorna la password e assicurati che il ruolo sia admin
        $stmt = $conn->prepare("UPDATE users SET password = ?, role = 'admin' WHERE username = ?");
        $stmt->execute([$hashedPassword, 'admin']);
        echo "Password dell'admin aggiornata correttamente.<br>";
        echo "Username: admin<br>";
        echo "Password: " . $correctPassword . "<br>";
    } else {
        // Crea l'utente admin se non esiste
        $stmt = $conn->prepare("INSERT INTO users (username, password, email, nome, cognome, role) VALUES (?, ?, ?, ?, ?, ?)");
        $stmt->execute(['admin', $hashedPassword, '<EMAIL>', 'Admin', 'ASDP', 'admin']);
        echo "Utente admin creato con successo.<br>";
        echo "Username: admin<br>";
        echo "Password: " . $correctPassword . "<br>";
    }

    // Mostra tutti gli utenti e i loro ruoli
    echo "<h2>Lista Utenti:</h2>";
    $stmt = $conn->query("SELECT username, role FROM users");
    while ($user = $stmt->fetch(PDO::FETCH_ASSOC)) {
        echo "Username: " . htmlspecialchars($user['username']) . " - Ruolo: " . htmlspecialchars($user['role']) . "<br>";
    }

} catch (PDOException $e) {
    echo "Errore: " . $e->getMessage();
}
?>
