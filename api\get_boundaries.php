<?php
require_once('../includes/db_config.php');

header('Content-Type: application/json');

try {
    // Ottieni la connessione al database
    $conn = getConnection();

    $query = "SELECT 
                z.comune,
                z.zona_sismica,
                z.prov_citta_metropolitana as provincia,
                z.regione,
                z.cod_istat_comune
              FROM zone_sismiche z";
              
    $stmt = $conn->prepare($query);
    $stmt->execute();
    
    $features = [];
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $features[] = [
            'type' => 'Feature',
            'properties' => [
                'comune' => $row['comune'],
                'zona_sismica' => $row['zona_sismica'],
                'provincia' => $row['provincia'],
                'regione' => $row['regione'],
                'cod_istat' => $row['cod_istat_comune']
            ],
            'geometry' => null // La geometria verrà aggiunta in futuro se necessario
        ];
    }
    
    $geojson = [
        'type' => 'FeatureCollection',
        'features' => $features
    ];
    
    echo json_encode([
        'success' => true,
        'geojson' => $geojson
    ]);

} catch (PDOException $e) {
    error_log("Errore nel recupero dei confini: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Errore nel recupero dei dati: ' . $e->getMessage()
    ]);
} catch (Exception $e) {
    error_log("Errore generico: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Errore interno del server'
    ]);
}