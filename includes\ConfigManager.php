<?php
class ConfigManager {
    private static $instance = null;
    private $env = [];

    private function __construct() {
        $this->loadEnvFile();
    }

    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    private function loadEnvFile() {
        $envFile = __DIR__ . '/../.env';
        if (!file_exists($envFile)) {
            error_log('File .env non trovato');
            return;
        }

        $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
        foreach ($lines as $line) {
            if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
                list($key, $value) = explode('=', $line, 2);
                $this->env[trim($key)] = trim($value);
            }
        }
    }

    public function get($key, $default = null) {
        return isset($this->env[$key]) ? $this->env[$key] : $default;
    }

    private function __clone() {}

    /**
     * Metodo magico __wakeup per la deserializzazione
     */
    public function __wakeup() {
        // Ricarica la configurazione quando l'oggetto viene deserializzato
        $this->loadConfig();
    }
}
?>
