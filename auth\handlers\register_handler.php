<?php
// register_handler.php
require_once 'includes/db_config.php';
header('Content-Type: application/json');

try {
    // Recupera il corpo della richiesta
    $input = file_get_contents('php://input');
    $data = json_decode($input, true);
    
    if (!$data) {
        throw new Exception('Dati non validi');
    }
    
    // Connessione al database
    $conn = getConnection();
    if (!$conn) {
        throw new Exception('Errore di connessione al database');
    }
    
    // Sanitizzazione input
    $nome = mysqli_real_escape_string($conn, trim($data['nome']));
    $cognome = mysqli_real_escape_string($conn, trim($data['cognome']));
    $email = mysqli_real_escape_string($conn, trim($data['email']));
    $username = mysqli_real_escape_string($conn, trim($data['username']));
    $password = $data['password'];
    
    // Validazione
    if (empty($nome) || empty($cognome) || empty($email) || empty($username) || empty($password)) {
        throw new Exception('Tutti i campi sono obbligatori');
    }
    
    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        throw new Exception('Email non valida');
    }
    
    if (strlen($password) < 6) {
        throw new Exception('La password deve essere di almeno 6 caratteri');
    }
    
    // Verifica se username o email esistono già
    $check_query = "SELECT id FROM users WHERE username = ? OR email = ?";
    $stmt = mysqli_prepare($conn, $check_query);
    mysqli_stmt_bind_param($stmt, "ss", $username, $email);
    mysqli_stmt_execute($stmt);
    mysqli_stmt_store_result($stmt);
    
    if (mysqli_stmt_num_rows($stmt) > 0) {
        throw new Exception('Username o email già registrati');
    }
    
    // Hash della password
    $hashed_password = password_hash($password, PASSWORD_DEFAULT);
    
    // Inserimento nuovo utente
    $insert_query = "INSERT INTO users (nome, cognome, email, username, password) VALUES (?, ?, ?, ?, ?)";
    $stmt = mysqli_prepare($conn, $insert_query);
    mysqli_stmt_bind_param($stmt, "sssss", $nome, $cognome, $email, $username, $hashed_password);
    
    if (!mysqli_stmt_execute($stmt)) {
        throw new Exception('Errore durante la registrazione');
    }
    
    echo json_encode([
        'success' => true,
        'message' => 'Registrazione completata con successo'
    ]);
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
