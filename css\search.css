.search-container {
    margin: 1rem 0;
    margin-left: 0;
    position: relative;
}

.search-box-container {
    position: relative;
    max-width: 800px;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.search-label {
    color: #888888;
    margin-bottom: 1rem;
    font-size: 0.9rem;
    display: block;
}

.search-box {
    width: 100%;
    padding: 0.75rem 1rem;
    background-color: #1E1E1E;
    border: 1px solid #333333;
    border-radius: 4px;
    color: #FFFFFF;
    font-size: 1rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.search-box::placeholder {
    color: #888888;
    opacity: 1;
}

.search-box:focus {
    outline: none;
    border-color: #FF7043;
    box-shadow: 0 0 0 2px rgba(255, 112, 67, 0.2);
}

.search-icon {
    position: static;
    color: #888888;
    transition: color 0.3s ease;
    font-size: 1.2rem;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    order: -1;
}

.search-box:focus + .search-icon {
    color: #FF7043;
}

.search-actions {
    display: flex;
    gap: 0.5rem;
}

.search-btn {
    background: none;
    border: none;
    color: #FF7043;
    cursor: pointer;
    padding: 8px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.search-btn:hover {
    background-color: rgba(255, 112, 67, 0.1);
    transform: scale(1.1);
}

.search-btn svg {
    width: 20px;
    height: 20px;
    stroke-width: 2.5;
}

.search-btn.geolocation {
    color: #4CAF50;
    position: relative;
}

.search-btn.geolocation:hover {
    background-color: rgba(76, 175, 80, 0.1);
}

.search-btn.geolocation.loading {
    pointer-events: none;
    opacity: 0.7;
}

.search-btn.geolocation.loading i {
    animation: rotate 1s linear infinite;
}

@keyframes rotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

.search-loading {
    position: absolute;
    right: 3.5rem;
    top: 50%;
    transform: translateY(-50%);
    width: 20px;
    height: 20px;
    border: 2px solid #FF7043;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
    display: none;
}

.search-loading.show {
    display: block;
}

@keyframes spin {
    to {
        transform: translateY(-50%) rotate(360deg);
    }
}

@media (max-width: 768px) {
    .search-box-container {
        max-width: 100%;
    }
    
    .search-box {
        font-size: 0.9rem;
        padding: 0.6rem 1rem;
    }
    
    .search-icon {
        font-size: 1.1rem;
        width: 18px;
        height: 18px;
    }
    
    .search-btn svg {
        width: 18px;
        height: 18px;
    }
} 