<?php
require_once '../includes/db_config.php';
session_start();

// Verifica se l'utente è loggato e ha il ruolo di admin
if (!isset($_SESSION['user_id']) || !isset($_SESSION['role']) || $_SESSION['role'] !== 'admin') {
    error_log("Accesso non autorizzato a get_stats.php");
    http_response_code(403);
    exit;
}

// Funzione per ottenere le statistiche del sistema
function getSystemStats($conn) {
    try {
        error_log("DEBUG: Inizio recupero statistiche");
        
        // Debug: Verifica la data corrente del sistema e del database
        $php_date = date('Y-m-d');
        $mysql_date = $conn->query("SELECT CURDATE() as today")->fetch(PDO::FETCH_ASSOC)['today'];
        error_log("DEBUG: Data PHP: " . $php_date . ", Data MySQL: " . $mysql_date);
        
        // Debug: Lista di tutti gli utenti
        $stmt = $conn->query("SELECT id, username, role FROM users ORDER BY role");
        $all_users = $stmt->fetchAll(PDO::FETCH_ASSOC);
        error_log("DEBUG: Lista utenti nel sistema: " . print_r($all_users, true));
        
        // Debug: Tutti gli accessi di oggi
        $stmt = $conn->query("SELECT al.id, al.user_id, al.timestamp, al.action, u.username, u.role 
                             FROM access_logs al 
                             JOIN users u ON al.user_id = u.id 
                             WHERE DATE(al.timestamp) = CURDATE()
                             ORDER BY al.timestamp DESC");
        $all_logs = $stmt->fetchAll(PDO::FETCH_ASSOC);
        error_log("DEBUG: Accessi di oggi: " . print_r($all_logs, true));
        
        // Numero totale utenti (solo ruolo 'user')
        $stmt = $conn->query("SELECT COUNT(*) as total_users FROM users WHERE role = 'user'");
        $totalUsers = $stmt->fetch(PDO::FETCH_ASSOC)['total_users'];
        error_log("DEBUG: Totale utenti (role='user'): " . $totalUsers);
        
        // Debug: Query per utenti attivi oggi
        $active_query = "SELECT COUNT(DISTINCT al.user_id) as active_today 
                        FROM access_logs al 
                        JOIN users u ON al.user_id = u.id 
                        WHERE DATE(al.timestamp) = CURDATE() 
                        AND u.role = 'user'
                        AND al.action = 'Login effettuato'
                        AND al.user_id NOT IN (SELECT id FROM users WHERE role = 'admin')";
        error_log("DEBUG: Query utenti attivi: " . $active_query);
        
        $stmt = $conn->query($active_query);
        $activeToday = $stmt->fetch(PDO::FETCH_ASSOC)['active_today'];
        error_log("DEBUG: Utenti attivi oggi: " . $activeToday);
        
        // Debug: Lista dettagliata utenti attivi oggi
        $stmt = $conn->query("SELECT DISTINCT u.id, u.username, u.role, COUNT(al.id) as login_count
                             FROM access_logs al
                             JOIN users u ON al.user_id = u.id
                             WHERE DATE(al.timestamp) = CURDATE()
                             AND al.action = 'Login effettuato'
                             GROUP BY u.id, u.username, u.role
                             ORDER BY u.role, login_count DESC");
        $active_users_detail = $stmt->fetchAll(PDO::FETCH_ASSOC);
        error_log("DEBUG: Dettaglio utenti attivi: " . print_r($active_users_detail, true));
        
        // Numero di accessi negli ultimi 7 giorni (solo utenti non admin)
        $stmt = $conn->query("SELECT COUNT(DISTINCT al.user_id) as active_week 
                            FROM access_logs al 
                            JOIN users u ON al.user_id = u.id 
                            WHERE al.timestamp >= DATE_SUB(NOW(), INTERVAL 7 DAY) 
                            AND u.role = 'user'
                            AND al.action = 'Login effettuato'
                            AND al.user_id NOT IN (SELECT id FROM users WHERE role = 'admin')");
        $activeWeek = $stmt->fetch(PDO::FETCH_ASSOC)['active_week'];

        // Recupera data ultimo backup
        $backupDir = __DIR__ . '/../backups';
        $lastBackupDate = null;
        if (is_dir($backupDir)) {
            $latest_ctime = 0;
            $latest_filename = '';    
            $d = dir($backupDir);
            while (false !== ($entry = $d->read())) {
                $filepath = "{$backupDir}/{$entry}";
                if (is_file($filepath) && filectime($filepath) > $latest_ctime) {
                    $latest_ctime = filectime($filepath);
                    $latest_filename = $filepath;
                }
            }
            $d->close();
            if ($latest_ctime > 0) {
                $lastBackupDate = date('Y-m-d H:i:s', $latest_ctime);
            }
        }

        // Calcola spazio backup
        $backupSize = 0;
        if (is_dir($backupDir)) {
            foreach (new RecursiveIteratorIterator(new RecursiveDirectoryIterator($backupDir)) as $file) {
                if ($file->isFile()) {
                    $backupSize += $file->getSize();
                }
            }
        }
        $backupSize = round($backupSize / (1024 * 1024 * 1024), 2); // Converti in GB
        
        $stats = [
            'total_users' => $totalUsers ?? 0,
            'active_today' => $activeToday ?? 0,
            'active_week' => $activeWeek ?? 0,
            'last_backup' => $lastBackupDate ?? date('Y-m-d H:i:s'),
            'disk_used' => $backupSize,
            'disk_total' => round(disk_total_space("/") / (1024 * 1024 * 1024), 2) // Converti in GB
        ];
        error_log("DEBUG: Statistiche finali: " . print_r($stats, true));
        
        return $stats;
    } catch (PDOException $e) {
        error_log("Errore nel recupero delle statistiche: " . $e->getMessage());
        return [
            'total_users' => 0,
            'active_today' => 0,
            'active_week' => 0,
            'last_backup' => null,
            'disk_used' => 0,
            'disk_total' => 0
        ];
    }
}

$stats = getSystemStats($conn);
if ($stats === false) {
    http_response_code(500);
    echo json_encode(['error' => 'Errore nel recupero delle statistiche']);
} else {
    header('Content-Type: application/json');
    echo json_encode($stats);
}
