<?php
session_start();

// Abilita la visualizzazione degli errori
ini_set('display_errors', 1);
error_reporting(E_ALL);

// Verifica se l'utente è autenticato
if (!isset($_SESSION['user_id'])) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'error' => 'Utente non autenticato']);
    exit;
}

// Importa le dipendenze
require_once '../includes/db_config.php';
require_once '../includes/Logger.php';

$logger = Logger::getInstance();
$logger->info('Avvio processo di backup');

// Funzione per verificare se mysqldump è disponibile
function findMysqldump() {
    $paths = [
        'C:\\xampp\\mysql\\bin\\mysqldump.exe',
        'C:\\wamp64\\bin\\mysql\\mysql8.0.31\\bin\\mysqldump.exe',
        'C:\\wamp\\bin\\mysql\\mysql5.7.36\\bin\\mysqldump.exe'
    ];

    foreach ($paths as $path) {
        if (file_exists($path)) {
            return $path;
        }
    }

    return false;
}

// Funzione per generare il report del backup
function generateBackupReport($stats) {
    $report = "ASDP - Report Backup\n";
    $report .= "===================\n\n";
    $report .= "Data backup: " . date('d/m/Y H:i:s') . "\n";
    $report .= "Utente: " . $_SESSION['nome'] . " " . $_SESSION['cognome'] . "\n\n";

    $report .= "Database\n";
    $report .= "--------\n";
    $report .= "Nome database: " . DB_NAME . "\n";
    $report .= "Dimensione backup: " . formatBytes($stats['database_size']) . "\n";
    $report .= "Tabelle incluse: " . $stats['tables_count'] . "\n\n";

    $report .= "File\n";
    $report .= "----\n";
    $report .= "Totale file: " . $stats['files_count'] . "\n";
    $report .= "Dimensione totale: " . formatBytes($stats['files_size']) . "\n";
    $report .= "Directory principali:\n";
    foreach ($stats['directories'] as $dir => $count) {
        $report .= "- $dir: $count file\n";
    }
    $report .= "\nFile esclusi:\n";
    foreach ($stats['excluded_patterns'] as $pattern) {
        $report .= "- $pattern\n";
    }

    $report .= "\nRiepilogo\n";
    $report .= "--------\n";
    $report .= "Dimensione totale backup: " . formatBytes($stats['total_size']) . "\n";
    $report .= "Stato: " . ($stats['success'] ? "Completato con successo" : "Errore") . "\n";
    if (!empty($stats['errors'])) {
        $report .= "\nErrori riscontrati:\n";
        foreach ($stats['errors'] as $error) {
            $report .= "- $error\n";
        }
    }

    return $report;
}

// Funzione per formattare i bytes in formato leggibile
function formatBytes($bytes, $precision = 2) {
    $units = ['B', 'KB', 'MB', 'GB', 'TB'];
    $bytes = max($bytes, 0);
    $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
    $pow = min($pow, count($units) - 1);
    return round($bytes / pow(1024, $pow), $precision) . ' ' . $units[$pow];
}

// Funzione per contare le tabelle nel database
function countDatabaseTables() {
    try {
        $conn = new PDO(
            "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME,
            DB_USER,
            DB_PASS
        );
        $stmt = $conn->query("SHOW TABLES");
        return $stmt->rowCount();
    } catch (PDOException $e) {
        return 0;
    }
}

// Funzione per il backup
function performBackup() {
    global $logger;
    
    try {
        $logger->info('Inizializzazione backup');
        
        // Inizializza le statistiche
        $stats = [
            'database_size' => 0,
            'files_size' => 0,
            'total_size' => 0,
            'files_count' => 0,
            'tables_count' => 0,
            'directories' => [],
            'excluded_patterns' => [
                'vendor/*',
                'node_modules/*',
                '.git/*',
                'temp/*',
                'tmp/*',
                'backups/*'
            ],
            'success' => false,
            'errors' => []
        ];

        // Verifica se mysqldump è disponibile
        $mysqldump_path = findMysqldump();
        $logger->debug('Percorso mysqldump', ['path' => $mysqldump_path]);
        
        if (!$mysqldump_path) {
            throw new Exception("mysqldump non trovato. Verifica che MySQL sia installato correttamente.");
        }

        // Crea una directory temporanea per il backup
        $backup_dir = sys_get_temp_dir() . '/asdp_backup_' . date('Y-m-d_H-i-s');
        $logger->debug('Directory temporanea creata', ['dir' => $backup_dir]);
        
        if (!mkdir($backup_dir, 0777, true)) {
            throw new Exception("Impossibile creare la directory temporanea: " . $backup_dir);
        }

        // Crea le sottodirectory
        mkdir($backup_dir . '/database');
        mkdir($backup_dir . '/files');

        // Backup del database
        $db_file = $backup_dir . '/database/' . DB_NAME . '.sql';
        $command = sprintf(
            '"%s" --host=%s --user=%s --password=%s --databases %s > %s 2>&1',
            $mysqldump_path,
            escapeshellarg(DB_HOST),
            escapeshellarg(DB_USER),
            escapeshellarg(DB_PASS),
            escapeshellarg(DB_NAME),
            escapeshellarg($db_file)
        );

        $logger->debug('Esecuzione comando mysqldump', ['command' => preg_replace('/--password=.*?(?=\s)/', '--password=***', $command)]);
        
        exec($command, $output, $return_var);
        if ($return_var !== 0) {
            $error_msg = implode("\n", $output);
            $logger->error('Errore mysqldump', ['error' => $error_msg]);
            throw new Exception("Errore durante il backup del database: " . $error_msg);
        }

        $logger->info('Backup database completato');

        // Aggiorna le statistiche del database
        $stats['database_size'] = filesize($db_file);
        $stats['tables_count'] = countDatabaseTables();

        // Copia i file dell'applicazione
        $base_dir = dirname(__DIR__); // Torna alla directory root
        $logger->debug('Directory base', ['dir' => $base_dir]);
        
        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($base_dir),
            RecursiveIteratorIterator::SELF_FIRST
        );

        foreach ($iterator as $file) {
            if ($file->isFile()) {
                $file_path = $file->getRealPath();
                $relative_path = substr($file_path, strlen($base_dir) + 1);
                
                // Controlla se il file deve essere escluso
                $exclude = false;
                foreach ($stats['excluded_patterns'] as $pattern) {
                    if (fnmatch($pattern, $relative_path)) {
                        $exclude = true;
                        break;
                    }
                }
                
                if (!$exclude) {
                    // Ottieni la directory principale del file
                    $main_dir = explode('/', str_replace('\\', '/', $relative_path))[0];
                    $stats['directories'][$main_dir] = ($stats['directories'][$main_dir] ?? 0) + 1;
                    
                    // Crea la directory di destinazione se non esiste
                    $dest_dir = $backup_dir . '/files/' . dirname($relative_path);
                    if (!is_dir($dest_dir)) {
                        if (!mkdir($dest_dir, 0777, true)) {
                            $logger->error('Impossibile creare directory', ['dir' => $dest_dir]);
                            continue;
                        }
                    }
                    
                    // Copia il file
                    $dest_file = $backup_dir . '/files/' . $relative_path;
                    if (!copy($file_path, $dest_file)) {
                        $logger->error('Impossibile copiare file', ['from' => $file_path, 'to' => $dest_file]);
                        continue;
                    }
                    
                    // Aggiorna le statistiche
                    $stats['files_count']++;
                    $stats['files_size'] += filesize($file_path);
                }
            }
        }

        $logger->info('Copia file completata', $stats);

        // Genera il report
        $stats['total_size'] = $stats['database_size'] + $stats['files_size'];
        $report = generateBackupReport($stats);
        file_put_contents($backup_dir . '/backup_report.txt', $report);

        // Crea il file ZIP
        $zip_file = sys_get_temp_dir() . '/asdp_backup_' . date('Y-m-d_H-i-s') . '.zip';
        $logger->debug('Creazione ZIP', ['file' => $zip_file]);
        
        $zip = new ZipArchive();
        if ($zip->open($zip_file, ZipArchive::CREATE) !== TRUE) {
            throw new Exception("Impossibile creare il file ZIP: " . $zip_file);
        }

        // Aggiungi tutti i file dalla directory temporanea
        $backup_files = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($backup_dir),
            RecursiveIteratorIterator::SELF_FIRST
        );

        foreach ($backup_files as $file) {
            if ($file->isFile()) {
                $file_path = $file->getRealPath();
                $relative_path = substr($file_path, strlen($backup_dir) + 1);
                if (!$zip->addFile($file_path, $relative_path)) {
                    $logger->error('Impossibile aggiungere file al ZIP', ['file' => $file_path]);
                }
            }
        }

        $zip->close();
        $logger->info('File ZIP creato', ['size' => filesize($zip_file)]);

        // Pulisci i file temporanei
        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($backup_dir, RecursiveDirectoryIterator::SKIP_DOTS),
            RecursiveIteratorIterator::CHILD_FIRST
        );
        
        foreach ($iterator as $file) {
            if ($file->isDir()) {
                rmdir($file->getRealPath());
            } else {
                unlink($file->getRealPath());
            }
        }
        rmdir($backup_dir);

        // Restituisci il risultato
        $result = [
            'success' => true,
            'message' => 'Backup completato con successo',
            'downloadUrl' => 'download_backup.php?file=' . basename($zip_file),
            'stats' => $stats
        ];
        
        $logger->info('Backup completato con successo', $result);
        return $result;

    } catch (Exception $e) {
        $logger->error('Errore durante il backup', ['error' => $e->getMessage()]);
        
        // Pulisci in caso di errore
        if (isset($backup_dir) && is_dir($backup_dir)) {
            $iterator = new RecursiveIteratorIterator(
                new RecursiveDirectoryIterator($backup_dir, RecursiveDirectoryIterator::SKIP_DOTS),
                RecursiveIteratorIterator::CHILD_FIRST
            );
            foreach ($iterator as $file) {
                if ($file->isDir()) {
                    rmdir($file->getRealPath());
                } else {
                    unlink($file->getRealPath());
                }
            }
            rmdir($backup_dir);
        }
        
        throw $e;
    }
}

// Esegui il backup e restituisci il risultato
try {
    header('Content-Type: application/json');
    $result = performBackup();
    echo json_encode($result);
} catch (Exception $e) {
    $logger->error('Errore fatale durante il backup', ['error' => $e->getMessage()]);
    header('Content-Type: application/json');
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
