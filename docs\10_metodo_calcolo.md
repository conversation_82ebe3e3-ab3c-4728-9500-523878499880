# Metodo di Calcolo Sismico
Ultimo aggiornamento: 20/01/2024

## 1. Introduzione
Il calcolo sismico viene effettuato secondo le NTC 2018 (Norme Tecniche per le Costruzioni) e relative circolari applicative. Il processo si articola in diverse fasi che vengono eseguite in sequenza.

## 2. Parametri di Input

### 2.1 Parametri Geografici
- Latitudine (LAT)
- Longitudine (LNG)
- Categoria di sottosuolo
- Categoria topografica

### 2.2 Parametri Strutturali
- Vita nominale di progetto (VN)
- Classe d'uso (Cu)
- Periodo di riferimento (VR = VN × Cu)
- Fattore di struttura (q)
- Coefficiente di smorzamento (ξ)

## 3. Fasi di Calcolo

### 3.1 Determinazione Parametri di Base
1. Recupero ag, F0, TC* dai dati di riferimento
2. Interpolazione valori per il punto specifico
3. Calcolo periodo di ritorno TR

### 3.2 Coefficienti di Amplificazione
1. Coefficiente stratigrafico SS
2. Coefficiente topografico ST
3. Coefficiente S = SS × ST
4. Coefficiente CC per categoria sottosuolo

### 3.3 Periodi Caratteristici
1. TC = CC × TC*
2. TB = TC / 3
3. TD = 4.0 × (ag/g) + 1.6

## 4. Spettri di Risposta

### 4.1 Spettro Elastico Orizzontale
Per 0 ≤ T < TB:
```
Se(T) = ag × S × η × F0 × [T/(TB) + 1/(η×F0) × (1 - T/TB)]
```

Per TB ≤ T < TC:
```
Se(T) = ag × S × η × F0
```

Per TC ≤ T < TD:
```
Se(T) = ag × S × η × F0 × (TC/T)
```

Per TD ≤ T:
```
Se(T) = ag × S × η × F0 × (TC×TD)/(T²)
```

### 4.2 Spettro di Progetto
Ottenuto riducendo le ordinate dello spettro elastico mediante il fattore q:
```
Sd(T) = Se(T) / q
```

## 5. Validazione Risultati

### 5.1 Controlli Automatici
1. Verifica range parametri di input
2. Controllo coerenza risultati
3. Validazione spettri generati
4. Verifica limiti normativi

### 5.2 Verifiche Manuali
1. Confronto con casi noti
2. Verifica andamento spettri
3. Controllo valori caratteristici
4. Validazione coefficienti

## 6. Output Generati

### 6.1 Parametri Calcolati
- ag: accelerazione orizzontale massima
- F0: fattore amplificazione spettrale
- TC*: periodo inizio tratto velocità costante
- SS: coefficiente amplificazione stratigrafica
- ST: coefficiente amplificazione topografica
- S: coefficiente che tiene conto categoria sottosuolo

### 6.2 Spettri
- Spettro elastico orizzontale
- Spettro elastico verticale
- Spettro di progetto SLV
- Spettro di progetto SLD
- Spettro di progetto SLO

## 7. Ottimizzazioni

### 7.1 Performance
1. Caching risultati frequenti
2. Ottimizzazione calcoli
3. Parallelizzazione processi
4. Gestione memoria

### 7.2 Precisione
1. Interpolazione dati precisa
2. Arrotondamenti controllati
3. Validazione step-by-step
4. Gestione casi limite

## Note Tecniche
1. Tutti i calcoli vengono eseguiti in doppia precisione
2. I risultati vengono arrotondati solo nella presentazione finale
3. Le interpolazioni utilizzano il metodo bilineare
4. I grafici vengono generati con precisione 0.01s

## Riferimenti Normativi
- NTC 2018 (D.M. 17/01/2018)
- Circolare applicativa n.7 del 21/01/2019
- Eurocodice 8 (EN 1998-1)

## Stati Limite

### SLO (Stato Limite di Operatività)
- **Probabilità**: 81% in VR
- **Periodo**: TR = 30 anni
- **Descrizione**: Dopo il terremoto, la costruzione nel suo complesso non deve subire danni ed interruzioni d'uso significative.

### SLD (Stato Limite di Danno)
- **Probabilità**: 63% in VR
- **Periodo**: TR = 50 anni
- **Descrizione**: Dopo il terremoto, la costruzione nel suo complesso subisce danni tali da non mettere a rischio gli utenti.

### SLV (Stato Limite di salvaguardia della Vita)
- **Probabilità**: 10% in VR
- **Periodo**: TR = 475 anni
- **Descrizione**: Dopo il terremoto, la costruzione subisce rotture e crolli dei componenti non strutturali ed impiantistici.

### SLC (Stato Limite di prevenzione del Collasso)
- **Probabilità**: 5% in VR
- **Periodo**: TR = 975 anni
- **Descrizione**: Dopo il terremoto, la costruzione subisce gravi rotture e crolli dei componenti non strutturali ed impiantistici.

## Parametri Input

### 1. Vita Nominale (VN)
```php
$vita_nominale = [
    'opere_provvisorie' => 10,  // Opere provvisorie, provvisionali, strutture in fase costruttiva
    'opere_ordinarie' => 50,    // Opere ordinarie, ponti, infrastrutture
    'opere_grandi' => 100       // Grandi opere, ponti, infrastrutture di grandi dimensioni
];
```

### 2. Classe d'Uso (CU)
```php
$classe_uso = [
    'I' => 0.7,    // Costruzioni con presenza solo occasionale di persone
    'II' => 1.0,   // Costruzioni con normali affollamenti
    'III' => 1.5,  // Costruzioni con affollamenti significativi
    'IV' => 2.0    // Costruzioni con funzioni pubbliche o strategiche importanti
];
```

### 3. Categoria Sottosuolo
```php
$categoria_sottosuolo = [
    'A' => ['descrizione' => 'Ammassi rocciosi affioranti o terreni molto rigidi',
            'vs30' => '> 800 m/s'],
    'B' => ['descrizione' => 'Rocce tenere e depositi di terreni a grana grossa molto addensati',
            'vs30' => '360-800 m/s'],
    'C' => ['descrizione' => 'Depositi di terreni a grana grossa mediamente addensati',
            'vs30' => '180-360 m/s'],
    'D' => ['descrizione' => 'Depositi di terreni a grana grossa scarsamente addensati',
            'vs30' => '< 180 m/s'],
    'E' => ['descrizione' => 'Terreni dei sottosuoli di tipo C o D per spessore non superiore a 20 m']
];
```

### 4. Categoria Topografica
```php
$categoria_topografica = [
    'T1' => ['descrizione' => 'Superficie pianeggiante, pendii e rilievi isolati con inclinazione media i ≤ 15°',
             'st' => 1.0],
    'T2' => ['descrizione' => 'Pendii con inclinazione media i > 15°',
             'st' => 1.2],
    'T3' => ['descrizione' => 'Rilievi con larghezza in cresta molto minore che alla base e inclinazione media 15° ≤ i ≤ 30°',
             'st' => 1.2],
    'T4' => ['descrizione' => 'Rilievi con larghezza in cresta molto minore che alla base e inclinazione media i > 30°',
             'st' => 1.4]
];
```

## Formule

### 1. Periodo di Riferimento (VR)
```php
function calcolaPeriodoRiferimento($vn, $cu) {
    return $vn * $cu;
}
```

### 2. Tempo di Ritorno (TR)
```php
function calcolaTempoRitorno($vr, $pvr) {
    return -$vr / log(1 - $pvr);
}
```

### 3. Accelerazione al Suolo (ag)
```php
function calcolaAccelerazione($lat, $lon, $tr) {
    // Interpolazione dai dati di pericolosità sismica
    $ag = interpolazioneGriglia($lat, $lon, $tr);
    return $ag;
}
```

### 4. Coefficienti di Amplificazione
```php
function calcolaCoefficienti($categoria_suolo, $categoria_topografica) {
    // Coefficiente stratigrafico
    $ss = calcolaSs($categoria_suolo);
    
    // Coefficiente topografico
    $st = $categoria_topografica['st'];
    
    // Coefficiente di amplificazione
    $s = $ss * $st;
    
    return $s;
}
```

### 5. Spettro di Risposta Elastico
```php
function calcolaSpettroRisposta($ag, $f0, $tc, $s) {
    $spettro = [];
    
    // Periodo T
    for ($t = 0; $t <= 4; $t += 0.01) {
        if ($t <= $tc) {
            $se = $ag * $s * $f0;
        } else {
            $se = $ag * $s * $f0 * ($tc / $t);
        }
        $spettro[] = ['T' => $t, 'Se' => $se];
    }
    
    return $spettro;
}
```

## Esempio di Calcolo

### Input
```php
$input = [
    'lat' => 41.902782,
    'lon' => 12.496366,
    'vn' => 50,
    'cu' => 1.0,
    'categoria_suolo' => 'B',
    'categoria_topografica' => 'T1'
];
```

### Calcolo
```php
// 1. Periodo di riferimento
$vr = calcolaPeriodoRiferimento($input['vn'], $input['cu']);

// 2. Stati limite
$stati_limite = [
    'SLO' => ['pvr' => 0.81, 'tr' => null],
    'SLD' => ['pvr' => 0.63, 'tr' => null],
    'SLV' => ['pvr' => 0.10, 'tr' => null],
    'SLC' => ['pvr' => 0.05, 'tr' => null]
];

foreach ($stati_limite as $stato => &$parametri) {
    // Calcolo TR
    $parametri['tr'] = calcolaTempoRitorno($vr, $parametri['pvr']);
    
    // Parametri spettrali
    $ag = calcolaAccelerazione($input['lat'], $input['lon'], $parametri['tr']);
    $f0 = calcolaF0($input['lat'], $input['lon'], $parametri['tr']);
    $tc = calcolaTC($input['lat'], $input['lon'], $parametri['tr']);
    
    // Coefficienti di amplificazione
    $s = calcolaCoefficienti($input['categoria_suolo'], $input['categoria_topografica']);
    
    // Spettro di risposta
    $parametri['spettro'] = calcolaSpettroRisposta($ag, $f0, $tc, $s);
}
```

### Output
```php
$output = [
    'SLO' => [
        'TR' => 30,
        'ag' => 0.042,
        'F0' => 2.547,
        'TC' => 0.279,
        'spettro' => [...]
    ],
    'SLD' => [
        'TR' => 50,
        'ag' => 0.054,
        'F0' => 2.562,
        'TC' => 0.298,
        'spettro' => [...]
    ],
    'SLV' => [
        'TR' => 475,
        'ag' => 0.123,
        'F0' => 2.641,
        'TC' => 0.334,
        'spettro' => [...]
    ],
    'SLC' => [
        'TR' => 975,
        'ag' => 0.156,
        'F0' => 2.678,
        'TC' => 0.345,
        'spettro' => [...]
    ]
];
```

## Nuove Funzionalità (06/01/2024)

### Ottimizzazioni Calcolo
1. Miglioramento precisione interpolazione dati
2. Nuovi controlli di validazione input
3. Gestione ottimizzata della cache
4. Logging dettagliato delle operazioni
5. Gestione errori avanzata

### Validazione Risultati
1. Controlli automatici di coerenza
2. Confronto con valori attesi
3. Verifica limiti normativi
4. Log dettagliato delle anomalie
5. Sistema di notifica errori

### Integrazione API
1. Nuovo endpoint `/api/calculate_seismic_params.php`
2. Gestione asincrona dei calcoli
3. Cache dei risultati frequenti
4. Validazione input/output
5. Rate limiting per ottimizzazione

### Debug e Testing
1. Nuovi strumenti di debug in `/tools/debug_seismic.php`
2. Log dettagliati in `/logs/seismic_calc.log`
3. Suite di test automatizzati
4. Strumenti di analisi performance
5. Sistema di reporting errori

## Note Importanti

### Validazione Input
1. Coordinate geografiche valide
2. Parametri vita nominale e classe d'uso corretti
3. Categorie suolo e topografica ammissibili
4. Dati di pericolosità sismica disponibili

### Precisione Calcoli
1. Interpolazione dati griglia
2. Arrotondamento risultati
3. Gestione errori numerici
4. Validazione output

### Riferimenti Normativi
1. NTC 2018
2. Circolare 2019
3. Eurocodice 8
4. Ordinanze PCM 

## Ricalcolo Parametri

### Interfaccia Utente
L'applicazione permette il ricalcolo dei parametri sismici attraverso:
1. Input vita nominale (default: 50 anni)
2. Selezione classe edificio (I, II, III, IV)
3. Selezione categoria terreno (A, B, C, D, E)
4. Selezione categoria topografica (T1, T2, T3, T4)

### Processo di Ricalcolo
```javascript
async function recalculateSeismicParams() {
    // Raccolta parametri
    const params = {
        lat: currentLocation.lat(),
        lng: currentLocation.lng(),
        nominalLife: parseInt(nominalLife),
        buildingClass: buildingClass,
        soilCategory: soilCategory,
        topographicCategory: topographicCategory
    };

    // Invio al server
    const response = await fetch('api/calculate_seismic_params.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(params)
    });

    // Elaborazione risultati
    const data = await response.json();
    if (data.success) {
        updateSeismicResults(data.data);
    }
}
```

### Visualizzazione Risultati
I risultati vengono mostrati in una tabella con:
- Stati Limite (SLO, SLD, SLV, SLC)
- Tempo di ritorno TR [anni]
- Accelerazione ag [g]
- Fattore amplificazione F0
- Periodo TC* [s] 