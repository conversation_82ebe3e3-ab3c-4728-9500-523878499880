<?php
/**
 * data_service.php - Recupero dati da ASDP
 * Path: /inertial_mass/api/data_service.php
 */

session_start();

// Verifica autenticazione
$isTestEnvironment = strpos($_SERVER['SCRIPT_NAME'], 'test_integration.php') !== false;

if (!isset($_SESSION['user_id']) && !$isTestEnvironment) {
    http_response_code(401);
    echo json_encode(['error' => 'Non autorizzato']);
    exit;
}

// Include database config
require_once '../../includes/db_config.php';

// Verifica metodo
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode(['error' => 'Metodo non consentito']);
    exit;
}

// Ottieni parametri
$lat = isset($_GET['lat']) ? floatval($_GET['lat']) : null;
$lon = isset($_GET['lon']) ? floatval($_GET['lon']) : null;

if (!$lat || !$lon) {
    http_response_code(400);
    echo json_encode(['error' => 'Coordinate mancanti']);
    exit;
}

try {
    // Recupera dati sismici per le coordinate
    $seismicData = getSeismicData($lat, $lon);
    
    // Restituisci i dati
    header('Content-Type: application/json');
    echo json_encode($seismicData);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Errore nel recupero dati: ' . $e->getMessage()]);
}

/**
 * Recupera i dati sismici per le coordinate specificate
 * @param float $lat Latitudine
 * @param float $lon Longitudine
 * @return array Dati sismici
 */
function getSeismicData($lat, $lon) {
    global $conn;
    
    // Query per trovare la zona sismica più vicina
    $sql = "SELECT 
                z.id,
                z.zona,
                z.ag,
                z.F0,
                z.TC_star as TC,
                z.categoria_suolo,
                c.nome as comune,
                c.provincia,
                c.regione,
                ST_Distance_Sphere(
                    POINT(?, ?),
                    POINT(z.longitudine, z.latitudine)
                ) as distance
            FROM zone_sismiche z
            LEFT JOIN comuni c ON z.comune_id = c.id
            WHERE z.latitudine IS NOT NULL 
                AND z.longitudine IS NOT NULL
            ORDER BY distance ASC
            LIMIT 1";
    
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("dd", $lon, $lat);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($row = $result->fetch_assoc()) {
        // Calcola parametri aggiuntivi se necessario
        $soilCategory = determineSoilCategory($row['categoria_suolo']);
        
        return [
            'lat' => $lat,
            'lon' => $lon,
            'zone' => $row['zona'],
            'ag' => floatval($row['ag']),
            'F0' => floatval($row['F0']),
            'TC' => floatval($row['TC']),
            'soil_category' => $soilCategory,
            'comune' => $row['comune'],
            'provincia' => $row['provincia'],
            'regione' => $row['regione'],
            'distance' => round($row['distance']) // distanza in metri
        ];
    } else {
        throw new Exception('Nessun dato sismico trovato per le coordinate specificate');
    }
}

/**
 * Determina la categoria di sottosuolo
 * @param mixed $dbCategory Categoria dal database
 * @return string Categoria standard (A-E)
 */
function determineSoilCategory($dbCategory) {
    // Mappa le categorie del database a quelle standard
    if (!$dbCategory) {
        return 'C'; // Default
    }
    
    // Se è già una lettera A-E, restituiscila
    if (preg_match('/^[A-E]$/', $dbCategory)) {
        return $dbCategory;
    }
    
    // Altrimenti usa il default
    return 'C';
}
