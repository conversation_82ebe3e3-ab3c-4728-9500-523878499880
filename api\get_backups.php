<?php
require_once '../includes/db_config.php';
session_start();

header('Content-Type: application/json');

// Verifica autenticazione
if (!isset($_SESSION['user_id']) || !isset($_SESSION['role']) || $_SESSION['role'] !== 'admin') {
    http_response_code(403);
    die(json_encode(['error' => 'Accesso non autorizzato']));
}

try {
    $conn = getConnection();
    
    // Verifica se la tabella backups esiste
    $tableExists = false;
    try {
        $result = $conn->query("SHOW TABLES LIKE 'backups'");
        $tableExists = ($result->rowCount() > 0);
    } catch (PDOException $e) {
        error_log("Errore verifica tabella backups: " . $e->getMessage());
    }
    
    // Se la tabella non esiste, creala
    if (!$tableExists) {
        $sql = "CREATE TABLE IF NOT EXISTS `backups` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `filename` varchar(255) NOT NULL,
            `created_at` datetime NOT NULL,
            `size` bigint(20) NOT NULL,
            PRIMARY KEY (`id`),
            UNIQUE KEY `filename` (`filename`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;";
        
        try {
            $conn->exec($sql);
            error_log("Tabella backups creata con successo");
        } catch (PDOException $e) {
            error_log("Errore creazione tabella backups: " . $e->getMessage());
            throw new Exception("Errore nella creazione della tabella backups");
        }
    }
    
    // Ottieni la lista dei backup dal database
    $stmt = $conn->query("SELECT filename, created_at, size FROM backups ORDER BY created_at DESC");
    if ($stmt === false) {
        throw new Exception("Errore nella query di selezione dei backup");
    }
    
    $backups = $stmt->fetchAll(PDO::FETCH_ASSOC);
    if ($backups === false) {
        $backups = []; // Se non ci sono backup, restituisci un array vuoto
    }
    
    echo json_encode($backups);
    
} catch (PDOException $e) {
    error_log("Errore database backup: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => 'Errore nel recupero della lista backup: ' . $e->getMessage()]);
} catch (Exception $e) {
    error_log("Errore generico backup: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => 'Errore nel recupero della lista backup: ' . $e->getMessage()]);
} 