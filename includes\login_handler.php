<?php
session_start();
require_once 'db_config.php';
require_once 'log_access.php';

header('Content-Type: application/json');

try {
    if (!isset($_POST['email']) || !isset($_POST['password'])) {
        throw new Exception('Email e password sono obbligatori');
    }

    $pdo = getConnection();
    
    $stmt = $pdo->prepare("SELECT * FROM users WHERE email = ?");
    $stmt->execute([trim($_POST['email'])]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$user || !password_verify($_POST['password'], $user['password'])) {
        throw new Exception('Credenziali non valide');
    }
    
    // Registra l'accesso
    logAccess($user['id'], 'login');
    
    // Imposta i dati di sessione
    $_SESSION['user_id'] = $user['id'];
    $_SESSION['email'] = $user['email'];
    $_SESSION['nome'] = $user['nome'];
    $_SESSION['cognome'] = $user['cognome'];
    $_SESSION['role'] = $user['role'];
    
    echo json_encode([
        'success' => true,
        'message' => 'Login effettuato con successo',
        'user' => [
            'id' => $user['id'],
            'email' => $user['email'],
            'nome' => $user['nome'],
            'cognome' => $user['cognome'],
            'role' => $user['role']
        ]
    ]);
    
} catch (Exception $e) {
    error_log("Errore nel login: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
} 