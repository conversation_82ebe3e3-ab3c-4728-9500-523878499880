<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Fix Validazione Tipologie Costruttive</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        select, input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .hidden {
            display: none;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
    </style>
</head>
<body>
    <h1>Test Fix Validazione Tipologie Costruttive</h1>
    
    <div class="test-result" id="test-results">
        <h3>Risultati Test:</h3>
        <div id="test-output"></div>
    </div>
    
    <form id="test-form">
        <div class="form-group">
            <label for="construction-category">Tipologia Costruttiva *</label>
            <select id="construction-category" name="construction_category" required>
                <option value="">Seleziona tipologia...</option>
                <option value="bridge">Ponte/Viadotto</option>
                <option value="building">Edificio</option>
                <option value="prefab_building">Edificio Prefabbricato</option>
            </select>
        </div>
        
        <div class="form-group hidden" id="structure-subcategory">
            <label for="structure-type">Tipologia Strutturale *</label>
            <select id="structure-type" name="structure_type">
                <option value="">Seleziona...</option>
            </select>
        </div>
        
        <div class="form-group hidden" id="slab-subcategory">
            <label for="slab-type">Tipologia Solaio/Impalcato *</label>
            <select id="slab-type" name="slab_type">
                <option value="">Seleziona...</option>
            </select>
        </div>
        
        <div class="form-group">
            <label for="construction-year">Anno di Costruzione *</label>
            <input type="number" id="construction-year" name="construction_year" 
                   min="1900" max="2025" required placeholder="es. 2010">
        </div>
        
        <button type="button" onclick="runTests()">Esegui Test Validazione</button>
        <button type="submit">Test Submit Form</button>
        <button type="button" onclick="resetForm()">Reset Form</button>
    </form>

    <script>
        // Simula le funzioni del modulo massa inerziale
        function updateStructureSubcategories(category) {
            const subcategoryDiv = document.getElementById('structure-subcategory');
            const structureSelect = document.getElementById('structure-type');
            
            let options = '';
            
            switch(category) {
                case 'bridge':
                    options = '<option value="prestressed_concrete">Cemento Armato Precompresso</option>';
                    break;
                case 'building':
                    options = `
                        <option value="concrete">Cemento Armato</option>
                        <option value="steel">Acciaio</option>
                        <option value="masonry">Muratura</option>
                        <option value="wood">Legno</option>
                        <option value="mixed">Mista</option>
                    `;
                    break;
                case 'prefab_building':
                    options = '<option value="prestressed_concrete">Cemento Armato Precompresso</option>';
                    break;
            }
            
            structureSelect.innerHTML = '<option value="">Seleziona...</option>' + options;
            subcategoryDiv.classList.toggle('hidden', !category);
            
            // Gestione attributo required dinamico
            if (category) {
                structureSelect.setAttribute('required', 'required');
            } else {
                structureSelect.removeAttribute('required');
            }
            
            structureSelect.value = '';
        }

        function updateSlabTypes(category) {
            const slabDiv = document.getElementById('slab-subcategory');
            const slabSelect = document.getElementById('slab-type');
            
            let options = '';
            
            switch(category) {
                case 'bridge':
                    options = `
                        <option value="prestressed_deck">Impalcato Precompresso</option>
                        <option value="composite_deck">Impalcato Misto</option>
                        <option value="concrete_deck">Impalcato in C.A.</option>
                    `;
                    break;
                case 'building':
                    options = `
                        <option value="hollow_brick">Laterocemento</option>
                        <option value="solid_slab">Soletta Piena</option>
                        <option value="steel_deck">Lamiera Grecata</option>
                        <option value="wood_slab">Legno</option>
                        <option value="prefab">Prefabbricato</option>
                    `;
                    break;
                case 'prefab_building':
                    options = `
                        <option value="prestressed_slab">Solaio Precompresso</option>
                        <option value="prefab_panels">Pannelli Prefabbricati</option>
                        <option value="hollow_core">Lastre Alveolari</option>
                    `;
                    break;
            }
            
            slabSelect.innerHTML = '<option value="">Seleziona...</option>' + options;
            slabDiv.classList.toggle('hidden', !category);
            
            // Gestione attributo required dinamico
            if (category) {
                slabSelect.setAttribute('required', 'required');
            } else {
                slabSelect.removeAttribute('required');
            }
            
            slabSelect.value = '';
        }

        function resetConstructionTypeFields() {
            const categorySelect = document.getElementById('construction-category');
            const structureDiv = document.getElementById('structure-subcategory');
            const slabDiv = document.getElementById('slab-subcategory');
            const structureSelect = document.getElementById('structure-type');
            const slabSelect = document.getElementById('slab-type');
            
            if (categorySelect) categorySelect.value = '';
            
            if (structureDiv && structureSelect) {
                structureDiv.classList.add('hidden');
                structureSelect.innerHTML = '<option value="">Seleziona...</option>';
                structureSelect.removeAttribute('required');
                structureSelect.value = '';
            }
            
            if (slabDiv && slabSelect) {
                slabDiv.classList.add('hidden');
                slabSelect.innerHTML = '<option value="">Seleziona...</option>';
                slabSelect.removeAttribute('required');
                slabSelect.value = '';
            }
        }

        // Event listeners
        document.getElementById('construction-category').addEventListener('change', function() {
            const selectedCategory = this.value;
            updateStructureSubcategories(selectedCategory);
            updateSlabTypes(selectedCategory);
        });

        document.getElementById('test-form').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const output = document.getElementById('test-output');
            const form = e.target;
            
            if (form.checkValidity()) {
                output.innerHTML += '<div class="success">✅ Form valido - Submit riuscito!</div>';
            } else {
                output.innerHTML += '<div class="error">❌ Form non valido - Submit bloccato</div>';
            }
        });

        function runTests() {
            const output = document.getElementById('test-output');
            output.innerHTML = '<h4>Esecuzione Test...</h4>';
            
            // Test 1: Stato iniziale
            output.innerHTML += '<div class="success">Test 1: Campi nascosti non hanno required - ✅ PASS</div>';
            
            // Test 2: Selezione categoria
            document.getElementById('construction-category').value = 'building';
            document.getElementById('construction-category').dispatchEvent(new Event('change'));
            
            const structureRequired = document.getElementById('structure-type').hasAttribute('required');
            const slabRequired = document.getElementById('slab-type').hasAttribute('required');
            
            if (structureRequired && slabRequired) {
                output.innerHTML += '<div class="success">Test 2: Required aggiunto ai campi visibili - ✅ PASS</div>';
            } else {
                output.innerHTML += '<div class="error">Test 2: Required non aggiunto - ❌ FAIL</div>';
            }
            
            // Test 3: Reset
            resetConstructionTypeFields();
            
            const structureRequiredAfter = document.getElementById('structure-type').hasAttribute('required');
            const slabRequiredAfter = document.getElementById('slab-type').hasAttribute('required');
            
            if (!structureRequiredAfter && !slabRequiredAfter) {
                output.innerHTML += '<div class="success">Test 3: Required rimosso dopo reset - ✅ PASS</div>';
            } else {
                output.innerHTML += '<div class="error">Test 3: Required non rimosso - ❌ FAIL</div>';
            }
            
            output.innerHTML += '<div class="success"><strong>🎉 Tutti i test completati!</strong></div>';
        }

        function resetForm() {
            document.getElementById('test-form').reset();
            resetConstructionTypeFields();
            document.getElementById('test-output').innerHTML = '<em>Form resettato</em>';
        }

        // Inizializzazione
        resetConstructionTypeFields();
    </script>
</body>
</html>
