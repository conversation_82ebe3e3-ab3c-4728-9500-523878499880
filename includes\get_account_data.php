<?php
session_start();
require_once 'db_config.php';

header('Content-Type: application/json');

// Verifica se l'utente è loggato
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode([
        'success' => false,
        'message' => 'Utente non autorizzato'
    ]);
    exit();
}

try {
    $pdo = getConnection();
    
    // Recupera i dati dell'utente
    $stmt = $pdo->prepare("
        SELECT id, username, nome, cognome, email, role, created_at 
        FROM users 
        WHERE id = ?
    ");
    
    $stmt->execute([$_SESSION['user_id']]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($user) {
        // Rimuovi dati sensibili
        unset($user['password']);
        
        echo json_encode([
            'success' => true,
            'user' => $user
        ]);
    } else {
        throw new Exception('Utente non trovato');
    }
    
} catch (Exception $e) {
    error_log("Errore nel recupero dei dati dell'account: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Errore nel recupero dei dati dell\'account'
    ]);
} 