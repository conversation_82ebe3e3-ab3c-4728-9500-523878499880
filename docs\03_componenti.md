# 3. Componenti Principali

## Backend (PHP)
- **Architettura MVC**
  - Model: Gestione dati e logica business
  - View: Template e presentazione
  - Controller: Gestione richieste e routing

- **Gestione Sessioni**
  - Autenticazione utenti
  - Mantenimento stato
  - Sicurezza accessi
  - Timeout configurabile

- **Calcolo Parametri Sismici**
  - Implementazione NTC 2018
  - Validazione input
  - Calcoli real-time
  - Gestione errori

- **Validazione Input/Output**
  - Sanitizzazione dati
  - Controlli tipo
  - Limiti e range
  - Messaggi errore

- **Integrazione Catasto**
  - Proxy WMS Agenzia Entrate
  - Gestione coordinate
  - Parsing dati catastali
  - Cache risultati

## Frontend
- **Interfaccia Responsive**
  - Layout adattivo
  - Mobile-first design
  - Breakpoint configurabili
  - Tema scuro/chiaro

- **Mappa Interattiva**
  - Leaflet.js per rendering
  - Layer catastale WMS
  - Selezione particelle
  - Visualizzazione dati catastali
  - Layer multipli
  - Controlli personalizzati

- **Calcoli Real-time**
  - AJAX per richieste
  - Validazione client-side
  - Feedback immediato
  - Cache risultati

- **PWA per Offline**
  - Service Worker
  - Cache API
  - Manifest
  - Push notifications

## Sistema di Backup
- **Backup Database Automatico**
  - Schedule configurabile
  - Compressione dati
  - Rotazione file
  - Verifica integrità

- **Backup File Sorgente**
  - Inclusione selettiva
  - Versionamento
  - Esclusione temp
  - Backup incrementale

- **Compressione ZIP**
  - Algoritmo ottimizzato
  - Password protection
  - Split archivi
  - Verifica CRC

- **Log Operazioni**
  - Tracciamento completo
  - Notifiche errori
  - Report periodici
  - Pulizia automatica 