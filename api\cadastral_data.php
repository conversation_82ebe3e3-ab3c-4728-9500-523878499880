<?php
header('Content-Type: application/json');

// Abilita il log degli errori
ini_set('display_errors', 0);
error_reporting(E_ALL);

// File di log
$logFile = '../logs/cadastral.log';

function writeLog($message) {
    global $logFile;
    $timestamp = date('[Y-m-d H:i:s]');
    file_put_contents($logFile, "$timestamp $message\n", FILE_APPEND);
}

// Verifica il metodo della richiesta
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['error' => 'Metodo non consentito']);
    exit;
}

// Ottieni i dati dalla richiesta POST
$data = json_decode(file_get_contents('php://input'), true);

if (!$data || !isset($data['lat']) || !isset($data['lng'])) {
    echo json_encode(['error' => 'Parametri mancanti']);
    exit;
}

$lat = floatval($data['lat']);
$lng = floatval($data['lng']);

writeLog("Richiesta dati per coordinate: $lat, $lng");

// URL del servizio WMS
$WMS_URL = 'https://wms.cartografia.agenziaentrate.gov.it/inspire/wms/ows01.php';

// Prepara i parametri per la richiesta GetFeatureInfo
$params = [
    'SERVICE' => 'WMS',
    'VERSION' => '1.3.0',
    'REQUEST' => 'GetFeatureInfo',
    'LAYERS' => 'CP.CadastralParcel',
    'QUERY_LAYERS' => 'CP.CadastralParcel',
    'INFO_FORMAT' => 'text/html',
    'FEATURE_COUNT' => 1,
    'I' => 50,
    'J' => 50,
    'WIDTH' => 101,
    'HEIGHT' => 101,
    'BBOX' => ($lat - 0.0001) . ',' . ($lng - 0.0001) . ',' . ($lat + 0.0001) . ',' . ($lng + 0.0001),
    'CRS' => 'EPSG:4258'
];

// Costruisci l'URL della richiesta
$requestUrl = $WMS_URL . '?' . http_build_query($params);
writeLog("URL richiesta WMS: $requestUrl");

// Inizializza cURL
$ch = curl_init();
curl_setopt_array($ch, [
    CURLOPT_URL => $requestUrl,
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_HEADER => false,
    CURLOPT_FOLLOWLOCATION => true,
    CURLOPT_ENCODING => "",
    CURLOPT_MAXREDIRS => 10,
    CURLOPT_TIMEOUT => 30,
    CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
    CURLOPT_SSL_VERIFYPEER => false,
    CURLOPT_USERAGENT => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
]);

// Esegui la richiesta
$response = curl_exec($ch);

if (curl_errno($ch)) {
    $error = curl_error($ch);
    writeLog("Errore cURL: $error");
    echo json_encode(['error' => 'Errore nella richiesta al servizio WMS']);
    curl_close($ch);
    exit;
}

$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$contentType = curl_getinfo($ch, CURLINFO_CONTENT_TYPE);
curl_close($ch);

writeLog("Risposta HTTP: $httpCode");
writeLog("Content-Type: $contentType");
writeLog("Risposta: " . substr($response, 0, 500) . "...");

// Se la risposta non è 200 OK
if ($httpCode !== 200) {
    echo json_encode(['error' => "Errore HTTP $httpCode"]);
    exit;
}

// Cerca il codice catastale nel HTML
if (preg_match('/NationalCadastralReference<\/th><td>(.*?)<\/td>/', $response, $matches)) {
    $codice = $matches[1];
    writeLog("Codice catastale trovato: $codice");
    $parts = explode('.', $codice);
    
    if (count($parts) === 2) {
        $response = [
            'comune' => substr($parts[0], 0, 4),
            'sezione' => substr($parts[0], 4, 1),
            'foglio' => ltrim(substr($parts[0], 5, 4), '0'),
            'allegato' => substr($parts[0], 9, 1),
            'sviluppo' => substr($parts[0], 10, 1),
            'particella' => $parts[1],
            'codice_completo' => $codice
        ];
        writeLog("Dati estratti: " . json_encode($response));
    } else {
        writeLog("Formato codice non valido: $codice");
        $response = ['error' => 'Formato codice catastale non valido'];
    }
} else {
    writeLog("Nessun codice catastale trovato nella risposta");
    $response = ['error' => 'Nessun dato catastale trovato per queste coordinate'];
}

echo json_encode($response);
?> 