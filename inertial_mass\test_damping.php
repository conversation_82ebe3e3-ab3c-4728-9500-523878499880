<?php
// inertial_mass/test_damping.php
// Test per verificare l'inclusione del fattore di smorzamento

// Simula una sessione per bypassare l'autenticazione
session_start();
$_SESSION['user_id'] = 'test_user';

// Include solo le funzioni necessarie
function calculateResponseSpectrum($T, $ag, $F0, $TC, $damping = 5.0) {
    // Parametri spettro semplificato
    $S = 1.2; // Fattore amplificazione medio

    // Calcola il fattore di smorzamento eta secondo NTC 2018
    // eta = sqrt(10/(5+xi)) >= 0.55, dove xi è lo smorzamento in %
    $eta = max(sqrt(10 / (5 + $damping)), 0.55);

    $TB = $TC / 3;
    $TD = 4.0 * $ag + 1.6;

    if ($T <= $TB) {
        return $ag * $S * $eta * $F0 * ($T/$TB + (1/($eta*$F0)) * (1 - $T/$TB));
    } elseif ($T <= $TC) {
        return $ag * $S * $eta * $F0;
    } elseif ($T <= $TD) {
        return $ag * $S * $eta * $F0 * ($TC/$T);
    } else {
        return $ag * $S * $eta * $F0 * ($TC*$TD/($T*$T));
    }
}

echo "=== TEST FATTORE DI SMORZAMENTO NEL MODULO MASSA INERZIALE ===\n\n";

// Test 1: Verifica calcolo eta con diversi valori di smorzamento
echo "1. Test calcolo fattore eta:\n";
$dampingValues = [3.0, 5.0, 7.0, 10.0];

foreach ($dampingValues as $damping) {
    $eta = max(sqrt(10 / (5 + $damping)), 0.55);
    echo "   Smorzamento: {$damping}% → eta = " . round($eta, 3) . "\n";
}

echo "\n2. Test spettro di risposta con smorzamento variabile:\n";
$T = 0.5; // Periodo di test
$ag = 0.15;
$F0 = 2.5;
$TC = 0.3;

foreach ($dampingValues as $damping) {
    $Se = calculateResponseSpectrum($T, $ag, $F0, $TC, $damping);
    echo "   Smorzamento: {$damping}% → Se(T=0.5s) = " . round($Se, 3) . " g\n";
}

// Test 3: Simulazione dati completi
echo "\n3. Test con dati completi edificio:\n";
$testData = [
    'location' => [
        'lat' => 41.9028,
        'lon' => 12.4964
    ],
    'seismic_params' => [
        'zone' => '3',
        'ag' => 0.15,
        'F0' => 2.5,
        'TC' => 0.3,
        'soil_category' => 'C',
        'damping' => 7.0  // Test con smorzamento del 7%
    ],
    'building' => [
        'structure_type' => 'concrete',
        'slab_type' => 'hollow_brick',
        'construction_year' => 1990,
        'floors' => [
            [
                'level' => 1,
                'area' => 150,
                'height' => 3.5,
                'use' => 'residential'
            ],
            [
                'level' => 2,
                'area' => 150,
                'height' => 3.0,
                'use' => 'residential'
            ]
        ]
    ]
];

// Test semplificato senza calcolo completo
echo "   Test del fattore di smorzamento nel calcolo spettrale:\n";
$damping = $testData['seismic_params']['damping'];
$ag = $testData['seismic_params']['ag'];
$F0 = $testData['seismic_params']['F0'];
$TC = $testData['seismic_params']['TC'];
$T = 0.5; // Periodo di test

$Se = calculateResponseSpectrum($T, $ag, $F0, $TC, $damping);
$expectedEta = max(sqrt(10 / (5 + $damping)), 0.55);

echo "   Smorzamento utilizzato: {$damping}%\n";
echo "   Fattore eta calcolato: " . round($expectedEta, 3) . "\n";
echo "   Accelerazione spettrale Se(T=0.5s): " . round($Se, 3) . " g\n";
echo "   Test completato con successo!\n";

echo "\n=== TEST COMPLETATO ===\n";
?>
