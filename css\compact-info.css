/* Compact Info Panels Styling */

/* <PERSON><PERSON><PERSON> il layout a tre colonne ma ottimizza lo spazio */
.content-grid {
    grid-template-columns: 2fr 0.8fr 0.8fr;
    gap: 0.75rem;
}

/* Riduce il padding dei pannelli info */
.info-panel {
    background-color: #1E1E1E;
    border-radius: 8px;
    padding: 1rem;
    height: 100%;
    overflow-y: auto;
    font-size: 0.9rem;
    scrollbar-width: thin;
    scrollbar-color: #FF7043 #1E1E1E;
}

/* Ottimizza i gruppi di parametri */
.param-group {
    margin-bottom: 0.75rem;
    padding: 0.75rem;
    background-color: #2A2A2A;
    border-radius: 6px;
    border: 1px solid #333333;
}

/* Riduce lo spazio tra le righe di info */
.info-row {
    margin-bottom: 0.25rem;
    padding-bottom: 0.25rem;
}

/* Compatta i titoli */
.info-panel h3 {
    color: #FF7043;
    font-size: 1rem;
    margin-bottom: 0.75rem;
    padding-bottom: 0.25rem;
    border-bottom: 2px solid #FF7043;
    font-weight: normal;
}

.param-group h4 {
    font-size: 0.75rem;
    margin-bottom: 0.25rem;
}

/* Ottimizza la griglia dei parametri sismici */
.param-row {
    gap: 0.25rem;
}

/* Compatta i singoli parametri */
.param label {
    font-size: 0.65rem;
    margin-bottom: 0.1rem;
}

.param span {
    font-size: 0.8rem;
    padding: 0.15rem 0.35rem;
}

/* Ottimizza input fields */
.info-panel input[type="text"],
.info-panel input[type="number"] {
    padding: 0.25rem 0.5rem;
    font-size: 0.85rem;
    height: 1.8rem;
}

/* Compatta le label degli input */
.info-panel label {
    font-size: 0.75rem;
    margin-bottom: 0.15rem;
}

/* Gestione responsive */
@media (max-width: 992px) {
    .content-grid {
        grid-template-columns: 1fr;
    }
    
    .info-panel {
        height: 400px;
    }
}

/* Mantiene la scrollbar sottile */
.info-panel::-webkit-scrollbar {
    width: 6px;
}

.info-panel::-webkit-scrollbar-track {
    background: #1E1E1E;
    border-radius: 3px;
}

.info-panel::-webkit-scrollbar-thumb {
    background-color: #FF7043;
    border-radius: 3px;
}

/* Ottimizza lo spazio della descrizione */
.description div {
    font-size: 0.85rem;
    line-height: 1.4;
    color: #CCCCCC;
    font-weight: normal;
    background-color: transparent;
    padding: 0;
}

/* Compattazione dati catastali */
.cadastral-data {
    margin-top: 0.5rem;
}

.cadastral-data h3 {
    font-size: 0.95rem;
    margin-bottom: 0.5rem;
    color: #FFFFFF;
}

.cadastral-data .form-group {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0.25rem;
    margin-bottom: 0.25rem;
}

.cadastral-data label {
    font-size: 0.75rem;
    color: #A0A0A0;
    margin-bottom: 0.1rem;
}

.cadastral-data input {
    font-size: 0.85rem;
    padding: 0.25rem 0.5rem;
    height: 1.8rem;
    background-color: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: #FFFFFF;
    border-radius: 4px;
}

/* Compattazione informazioni località */
.location-data {
    margin-top: 0.5rem;
}

.location-data h3 {
    font-size: 0.95rem;
    margin-bottom: 0.5rem;
    color: #FFFFFF;
}

.location-data .info-row {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0.25rem;
    margin-bottom: 0.25rem;
    align-items: center;
}

.location-data label {
    font-size: 0.75rem;
    color: #A0A0A0;
}

.location-data div:not(.info-row) {
    font-size: 0.85rem;
    padding: 0.25rem 0.5rem;
    background-color: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: #FFFFFF;
    border-radius: 4px;
    min-height: 1.8rem;
    display: flex;
    align-items: center;
}

/* Gestione messaggi info */
#message-box.message {
    font-size: 0.75rem;
    padding: 0.5rem;
    margin: 0.25rem 0;
    border-radius: 4px;
    background-color: rgba(0, 123, 255, 0.1);
    border: 1px solid rgba(0, 123, 255, 0.2);
    color: #A0A0A0;
}

/* Ottimizza scrolling */
.info-panel {
    max-height: calc(100vh - 180px);
    overflow-y: auto;
    scrollbar-width: thin;
}

/* Compattazione Pannello Parametri Sismici */
.seismic-zone {
    margin-top: 0.5rem;
}

/* Ottimizza header parametri sismici */
.seismic-params h3 {
    margin-top: 0;
    margin-bottom: 0.5rem;
    padding: 0.5rem;
    color: #FFFFFF;
    background-color: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    font-weight: normal;
}

/* Compatta i gruppi di stati limite */
.seismic-params .param-group {
    background-color: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    margin-bottom: 0.5rem;
    padding: 0.5rem;
}

.seismic-params .param-group:first-child {
    margin-top: 0.5rem;
}

/* Rimuovo margini duplicati */
.info-panel > div:first-child {
    margin-top: 0;
}

.info-panel > .seismic-zone {
    margin-top: 0;
}

/* Ottimizza titoli stati limite */
.seismic-params h4 {
    font-size: 0.75rem;
    color: #A0A0A0;
    margin-bottom: 0.25rem;
}

/* Griglia parametri sismici */
.seismic-params .param-row {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 0.25rem;
}

/* Stile singoli parametri */
.seismic-params .param {
    text-align: center;
}

.seismic-params .param label {
    font-size: 0.7rem;
    color: #A0A0A0;
    display: block;
    margin-bottom: 0.1rem;
}

.seismic-params .param span {
    font-size: 0.8rem;
    background-color: rgba(255, 255, 255, 0.1);
    padding: 0.15rem 0.25rem;
    border-radius: 3px;
    display: block;
    color: #FFFFFF;
}

/* Compattazione Parametri di Calcolo */
.param-group {
    background-color: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    margin-bottom: 0.5rem;
}

/* Ottimizza input e select */
.param-group select,
.param-group input[type="number"] {
    width: 100%;
    font-size: 0.85rem;
    padding: 0.25rem 0.5rem;
    height: 1.8rem;
    background-color: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: #FFFFFF;
    border-radius: 4px;
}

/* Compattazione Tabella Risultati */
.seismic-results {
    margin-top: 0.5rem;
}

.seismic-results h3 {
    font-size: 0.95rem;
    margin-bottom: 0.5rem;
    color: #FFFFFF;
}

.seismic-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.75rem;
    background-color: rgba(255, 255, 255, 0.05);
    border-radius: 4px;
    overflow: hidden;
}

.seismic-table th,
.seismic-table td {
    padding: 0.35rem 0.25rem;
    text-align: center;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.seismic-table th {
    background-color: rgba(255, 255, 255, 0.1);
    color: #A0A0A0;
    font-weight: normal;
}

.seismic-table td {
    color: #FFFFFF;
}

/* Bottone Ricalcola */
.button-group {
    margin: 0.5rem 0;
}

.action-button {
    width: 100%;
    padding: 0.5rem;
    font-size: 0.85rem;
    background-color: rgba(0, 123, 255, 0.2);
    border: 1px solid rgba(0, 123, 255, 0.3);
    color: #FFFFFF;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.action-button:hover {
    background-color: rgba(0, 123, 255, 0.3);
}

/* Ottimizza spaziature generali */
.info-panel > div:not(:first-child) {
    margin-top: 0.75rem;
}

/* Gestione responsive per tabella */
@media (max-width: 768px) {
    .seismic-table {
        font-size: 0.7rem;
    }
    
    .seismic-table th,
    .seismic-table td {
        padding: 0.25rem 0.15rem;
    }
}

/* Stile ottimizzato per select con descrizioni */
.param-group select,
.info-row select {
    width: 100%;
    background-color: #242424;
    border: 1px solid #444444;
    color: #FFFFFF;
    padding: 0.5rem;
    border-radius: 4px;
    font-size: 0.8rem;
    line-height: 1.4;
    cursor: pointer;
    height: auto;
    min-height: 2.2rem;
}

.param-group select option,
.info-row select option {
    padding: 0.6rem;
    background-color: #333333;
    color: #FFFFFF;
    font-size: 0.8rem;
    line-height: 1.4;
    border-bottom: 1px solid #444444;
    white-space: normal;
    word-wrap: break-word;
    min-height: 2rem;
    display: block;
}

.param-group select option:hover,
.info-row select option:hover {
    background-color: #444444;
}

.param-group select:focus,
.info-row select:focus {
    outline: none;
    border-color: #FF7043;
    box-shadow: 0 0 0 2px rgba(255, 112, 67, 0.2);
}

/* Rimuovo gli stili del tooltip che non servono più */

/* Stile unificato per tutti i titoli dei pannelli */
.info-panel h3,
.seismic-params h3,
.cadastral-data h3,
.location-data h3,
.seismic-results h3,
.param-group h3 {
    color: #FF7043;
    font-size: 1rem;
    margin-bottom: 0.75rem;
    padding-bottom: 0.25rem;
    border-bottom: 2px solid #FF7043;
    font-weight: normal;
}

/* Sottotitoli per gli stati limite */
.seismic-params h4 {
    font-size: 0.75rem;
    color: #A0A0A0;
    margin: 0.5rem 0 0.25rem 0;
    padding: 0.25rem;
    background-color: rgba(255, 255, 255, 0.03);
    border-radius: 3px;
}

/* Rimuovo stili duplicati */
.seismic-params h3,
.cadastral-data h3,
.location-data h3,
.seismic-results h3 {
    margin-top: 0.5rem;
}

/* Aggiustamento spaziature per i gruppi */
.param-group {
    margin-top: 0.5rem;
}

.param-group:first-child {
    margin-top: 0;
}

/* Stile unificato per i gruppi di parametri */
.param-group,
.info-row,
.form-group {
    margin-bottom: 0.75rem;
    padding: 0.75rem;
    background-color: #2A2A2A;
    border-radius: 6px;
    border: 1px solid #333333;
}

/* Stile unificato per le label */
.param-group label,
.info-row label,
.form-group label {
    display: block;
    color: #888888;
    font-size: 0.8rem;
    margin-bottom: 0.25rem;
}

/* Stile unificato per input e select */
.param-group input,
.info-row input,
.form-group input,
.param-group select,
.info-row select {
    width: 100%;
    background-color: #242424;
    border: 1px solid #333333;
    color: #FFFFFF;
    padding: 0.5rem;
    border-radius: 4px;
    font-size: 0.9rem;
}

/* Stile unificato per input readonly */
input[readonly],
input[disabled] {
    background-color: #242424;
    color: #CCCCCC;
    cursor: not-allowed;
}

/* Stile unificato per i valori */
.param span,
.info-row div:not(.info-row):not(.form-group):not(.param-group) {
    color: #FF7043;
    font-size: 0.9rem;
    font-weight: 500;
    background-color: #242424;
    padding: 0.4rem 0.75rem;
    border-radius: 3px;
    display: inline-block;
    width: 100%;
}

/* Stile per il container dell'altitudine */
.altitude-container {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background-color: #242424;
    border: 1px solid #333333;
    border-radius: 4px;
    padding: 0.25rem 0.5rem;
}

.altitude-container input {
    background: none;
    border: none;
    padding: 0;
    color: #FFFFFF;
    width: calc(100% - 2rem);
}

.altitude-container input:focus {
    outline: none;
}

.altitude-container .unit {
    color: #888888;
    font-size: 0.8rem;
    user-select: none;
}
