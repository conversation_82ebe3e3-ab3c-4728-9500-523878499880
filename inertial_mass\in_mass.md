# Piano di Integrazione Modulo Massa Inerziale con LLM Esterno

> **Stato attuale: IMPLEMENTATO**
> *Ultimo aggiornamento: 14/06/2025*

## Obiettivo
Integrare il modulo di calcolo della massa inerziale con un LLM esterno per il calcolo delle forze sismiche, mantenendo l'interfaccia utente coerente con ASDP e richiedendo all'utente solo le informazioni mancanti sulla geometria dell'edificio.

## Architettura Proposta

```mermaid
graph TD
    A[ASDP Software] -->|Apre popup| B(Modulo Massa Inerziale)
    B -->|<PERSON><PERSON> dati mancanti| C[Utente]
    B -->|Invia dati| D{Servizio di Calcolo}
    D -- Priorità 1 --> E[API LLM Esterno (Gemini/Deepseek)]
    D -- Fallback --> F[Calcolatore Locale PHP]
    E -->|Restituisce risultati| B
    F -->|Restituisce risultati| B
    B -->|Mostra risultati| C
    B -->|Salva risultati| A
```

**Nota sull'Architettura:** Il sistema è progettato per utilizzare primariamente servizi LLM esterni (Gemini come prima scelta, Deepseek come seconda) per il calcolo dei parametri sismici. Qualora i servizi LLM non fossero disponibili (ad esempio, per problemi di connettività o configurazione delle API key), il modulo è in grado di effettuare i calcoli utilizzando un motore di calcolo locale basato su PHP, garantendo così la continuità del servizio.

## Fasi di Sviluppo

### 1. Analisi e Progettazione (2 giorni)
- [x] Analisi dei dati disponibili in ASDP
- [x] Definizione API di comunicazione con LLM esterno
- [x] Progettazione interfaccia utente modale
- [x] Identificazione dati mancanti da richiedere all'utente

### 2. Sviluppo Backend (3 giorni)
- [x] Creazione endpoint API per la comunicazione con LLM
- [x] Implementazione logica di recupero dati da ASDP
- [x] Sviluppo sistema di validazione input
- [x] Gestione autenticazione e sicurezza

### 3. Integrazione Frontend (2 giorni)
- [x] Creazione finestra modale responsive
- [x] Implementazione form per dati mancanti
- [x] Visualizzazione risultati in formato tabellare e grafico
- [x] Integrazione con lo stile ASDP esistente

### 4. Collegamento con LLM Esterno (2 giorni)
- [x] Configurazione connessione API LLM (Deepseek)
- [x] Formattazione prompt per il calcolo sismico
- [x] Parsing e validazione risposte LLM
- [x] Gestione errori e timeout

### 5. Testing e Ottimizzazione (3 giorni)
- [x] Test unitari e di integrazione
- [x] Test di carico e prestazioni
- [x] Ottimizzazione chiamate API
- [x] Documentazione utente e sviluppatori

## Struttura File Implementata

```
inertial_mass/
├── api/
│   ├── llm_service.php      # Gestione chiamate LLM
│   ├── data_service.php     # Recupero dati da ASDP
│   └── save_results.php     # Salvataggio risultati
├── assets/
│   ├── css/
│   │   └── modal.css       # Stili per la finestra modale
│   └── js/
│       └── modal.js         # Logica della finestra modale
├── includes/
│   ├── config.php          # Configurazione API LLM
│   └── utils.php           # Funzioni di utilità
├── cache/                  # Cache per ottimizzare chiamate API
├── in_mass.md              # Documentazione piano originale
├── modal.php               # Interfaccia modale principale
└── test_integration.php    # Pagina di test per l'integrazione
```

**Integrazione in ASDP:**
```
/js/
└── inertial_mass_integration.js  # Script per l'integrazione del modulo
```

## Dati da Recuperare da ASDP e da Richiedere all'Utente

### Dati Automatici (da ASDP)
- Coordinate geografiche (lat, lon)
- Zona sismica
- Categoria suolo
- Comune, provincia, regione
- Accelerazione al suolo (ag)
- Fattore di amplificazione (F0)
- Periodo di riferimento (TC*)
- Tempo di ritorno (TR)

### Dati da Richiedere all'Utente (form)
| Nome dato               | Dove viene chiesto        | Formato   | Note                           |
|-------------------------|--------------------------|-----------|---------------------------------|
| Tipologia strutturale   | Form (select)            | string    | Obbligatorio                   |
| Tipologia solaio        | Form (select)            | string    | Obbligatorio                   |
| Anno di costruzione     | Form (input number)      | int       | Obbligatorio, normativa sismica|
| Numero di piani         | Form (dinamico)          | int       | Almeno 1 piano                 |
| Area di ciascun piano   | Form (input number)      | float     | m², obbligatorio per ogni piano|
| Altezza interpiano      | Form (input number)      | float     | m, obbligatorio per ogni piano |
| Destinazione d’uso      | Form (select)            | string    | Obbligatorio per ogni piano    |

**Descrizione campi form:**
- Tipologia strutturale: cemento armato, acciaio, muratura, legno, mista
- Tipologia solaio: laterocemento, soletta piena, lamiera grecata, legno, prefabbricato
- Anno di costruzione: anno (es. 1995, 2012) per determinare la normativa sismica applicata
- Area: superficie del piano in metri quadrati
- Altezza: altezza interpiano in metri
- Destinazione d’uso: residenziale, uffici, commerciale, industriale, magazzino

Questi dati sono richiesti all’utente perché non recuperabili automaticamente dal software principale.
## Esempio di Payload per LLM

```json
{
  "location": {
    "lat": 41.9028,
    "lon": 12.4964
  },
  "seismic_params": {
    "category": 2,
    "ag": 0.25,
    "F0": 2.4,
    "TC": 0.3,
    "soil_category": "C"
  },
  "building": {
    "floors": [
      {"level": 1, "height": 3.5, "area": 120, "weight_per_sqm": 8.5},
      {"level": 2, "height": 3.0, "area": 120, "weight_per_sqm": 8.5},
      {"level": 3, "height": 3.0, "area": 100, "weight_per_sqm": 8.5}
    ],
    "structure_type": "concrete",
    "slab_type": "hollow_brick"
  }
}
```

## Configurazione Deepseek

Deepseek è stato configurato come LLM esterno:
1. Il file `.env` nella root del progetto contiene:
   ```
   # Deepseek API
   DEEPSEEK_API_KEY=***********************************
   DEEPSEEK_MODEL=deepseek-chat
   ```
2. La configurazione viene letta automaticamente dal backend tramite il file `includes/config.php`:
   ```php
   return [
       'llm_provider'     => 'deepseek',
       'deepseek_api_key' => getenv('DEEPSEEK_API_KEY') ?: '',
       'deepseek_model'   => getenv('DEEPSEEK_MODEL') ?: 'deepseek-chat'
   ];
   ```

## Considerazioni sulla Sicurezza

1. **Autenticazione**:
   - Validazione token di sessione ASDP
   - Rate limiting per le chiamate API

2. **Dati Sensibili**:
   - Crittografia dei dati in transito (HTTPS)
   - Validazione input lato server

3. **Performance**:
   - Caching delle risposte LLM
   - Timeout per le chiamate API

## Monitoraggio e Logging

- Registrazione delle richieste/risposte (senza dati sensibili)
- Tracciamento errori e performance
- Log per audit e debug

## Analisi Funzionamento Modale e Preparazione Dati LLM (Aggiornamento del 2025-06-13)

Questa sezione descrive il flusso di dati all'interno del modale di calcolo della massa inerziale e come i dati vengono preparati per una potenziale chiamata a un Large Language Model (LLM) come Deepseek, basandosi sull'analisi del codice JavaScript (`js/inertial_mass_integration.js` e `inertial_mass/assets/js/modal.js`).

**1. Apertura del Modale e Recupero Dati Iniziali (da ASDP):**

*   L'utente clicca il pulsante "Calcolo Massa Inerziale" su `home.php`.
*   `js/inertial_mass_integration.js` (`openInertialMassModal()` -> `getCurrentSeismicData()`):
    *   Raccoglie dati da `home.php`: coordinate, categoria suolo, parametri sismici (ag, F0, TC*), zona sismica, smorzamento (`#damping`), e fattore di struttura (`#q-factor`).
    *   Questi dati formano l'oggetto `seismicData`.
*   Il modale (`modal.php`) e il suo script (`modal.js`) vengono caricati (se non già presenti).
*   Viene chiamata `window.initInertialMassModal(seismicData)` in `modal.js`.

**2. Inizializzazione del Modale (`inertial_mass/assets/js/modal.js`):**

*   `initInertialMassModal(seismicData)`:
    *   Salva `seismicData` nello stato interno del modale (`inertialMassState.seismicData`).
    *   Popola la sezione "Dati Sismici (da ASDP)" del modale con i valori ricevuti, inclusi smorzamento e fattore di struttura.

**3. Raccolta Dati Utente nel Modale:**

*   L'utente inserisce nel form del modale: tipologia strutturale, solaio, anno costruzione, e dettagli per ogni piano (area, altezza, destinazione d'uso).
*   Al click sul pulsante di calcolo nel modale, `handleFormSubmit()` viene chiamata.

**4. Aggregazione Dati (`collectFormData()` in `modal.js`):**

*   Questa funzione costruisce un oggetto `formData` che include:
    *   `location`: { `lat`, `lon` } (da `inertialMassState.seismicData`).
    *   `seismic_params`: { `zone`, `ag`, `F0`, `TC`, `soil_category` } (da `inertialMassState.seismicData`).
        *   I valori di `damping` (smorzamento) e `q_factor` (fattore di struttura), presi da `home.php` e disponibili in `inertialMassState.seismicData`, sono ora inclusi nell'oggetto `seismic_params`.
    *   `building`: { `structure_type`, `slab_type`, `construction_year`, `floors`: [...] }.
        *   `floors` è un array di oggetti, ciascuno con `level`, `area`, `height`, `use`.

**5. Validazione Dati:**

*   `handleFormSubmit()` chiama `validateFormData(formData)` per verificare i dati.

**6. Preparazione e Invio Richiesta LLM (`calculateWithLLM()` in `modal.js`):**

*   Se la validazione ha successo:
    *   L'oggetto `formData` viene convertito in una stringa JSON.
    *   Viene effettuata una richiesta `fetch` (`POST`) all'endpoint `/progetti/asdp/inertial_mass/api/llm_service.php` con il JSON come corpo della richiesta.
    *   Il file `llm_service.php` (lato server) è responsabile di interagire con l'LLM (es. Deepseek), inviandogli i dati opportunamente formattati come prompt, e di elaborare la sua risposta.

**Struttura Dati JSON Inviata al Backend (Esempio):**

```json
{
  "location": {
    "lat": 41.8081,
    "lon": 12.6794
  },
  "seismic_params": {
    "zone": "3",
    "ag": 0.17,
    "F0": 2.586,
    "TC": 0.279,
    "soil_category": "C",
    "damping": 5.0, // Esempio, valore da home.php
    "q_factor": 1.2  // Esempio, valore da home.php
  },
  "building": {
    "structure_type": "telaio_ca",
    "slab_type": "laterocemento",
    "construction_year": 2010,
    "floors": [
      {
        "level": 1,
        "area": 120.5,
        "height": 3.2,
        "use": "abitazione"
      }
      // ... altri piani
    ]
  }
}
```

**Conclusioni e Passi Successivi per Integrazione LLM Completa:**

*   La funzione `collectFormData()` in `modal.js` è stata aggiornata per includere `damping` e `q_factor` nell'oggetto `seismic_params` inviato al backend. Questo assicura che tali valori siano disponibili per il calcolo LLM.
*   La logica in `llm_service.php` deve essere implementata (o verificata se già esistente) per:
    *   Utilizzare correttamente i valori di `damping` e `q_factor` ricevuti.
    *   Costruire un prompt efficace per Deepseek che includa tutti i dati rilevanti.
    *   Interpretare la risposta dell'LLM.
*   È consigliabile testare l'intero flusso per assicurarsi che i dati (inclusi `damping` e `q_factor`) siano correttamente processati dal backend e, se applicabile, dall'LLM.

## Stato Attuale

- [x] Piano approvato e implementato
- [x] Ambiente di sviluppo configurato
- [x] Funzionalità implementate completamente
- [x] Database creato e configurato (4 tabelle)
- [x] Test e validazione completati
- [x] Integrazione nell'interfaccia principale di ASDP
- [x] Sistema di cache e logging implementato
- [x] Rate limiting e sicurezza configurati
- [x] Documentazione completa aggiornata
- [x] Guida utente integrata nell'help di ASDP

## Miglioramenti Interfaccia Utente (Giugno 2025)

Durante le sessioni di sviluppo di Giugno 2025, sono stati apportati i seguenti miglioramenti all'interfaccia utente del modale di calcolo della massa inerziale:

- **Risoluzione Doppia Icona Titolo Risultati**:
  - **Problema**: Il titolo "Distribuzione Forze per Piano" nella sezione dei risultati mostrava un'icona 📊 duplicata.
  - **Soluzione**: È stata rimossa l'istanza dell'icona hard-coded direttamente nel file JavaScript `inertial_mass/assets/js/modal.js` (all'interno della funzione `generateResultsHTML`). La visualizzazione dell'icona è ora gestita unicamente tramite CSS, garantendo una singola rappresentazione.

- **Uniformazione Stile Pulsanti del Modale**:
  - **Obiettivo**: Garantire coerenza visiva e un aspetto professionale a tutti i pulsanti di interazione all'interno del modale.
  - **Modifiche**: I seguenti pulsanti nel file `inertial_mass/modal.php` sono stati aggiornati per utilizzare la classe `btn btn-primary` (stile primario arancione):
    - Pulsante "Nuovo Calcolo" (precedentemente `btn-secondary`).
    - Pulsante "+ Aggiungi Piano" (precedentemente con una classe custom `btn-add-floor`).
    - Pulsante "Annulla" (precedentemente `btn-secondary`).
  - **Risultato**: Tutti i pulsanti principali del modale ora presentano uno stile uniforme, migliorando l'esperienza utente.

## Prossimi Passi

1. Monitoraggio delle prestazioni in produzione
2. Raccolta feedback utenti
3. Possibili miglioramenti futuri:
   - Integrazione con altri LLM (OpenAI, Claude)
   - Export risultati in PDF
   - Visualizzazione 3D edificio
