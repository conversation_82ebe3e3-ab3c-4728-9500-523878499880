<?php
require_once '../../includes/db_config.php';
session_start();

// Verifica accesso admin
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Accesso non autorizzato']);
    exit;
}

if ($_SERVER['REQUEST_METHOD'] === 'GET' && isset($_GET['id'])) {
    try {
        $conn = getConnection();
        
        // Recupera i dati dell'utente
        $stmt = $conn->prepare("SELECT id, username, nome, cognome, email, role FROM users WHERE id = ?");
        $stmt->execute([$_GET['id']]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$user) {
            throw new Exception('Utente non trovato');
        }

        echo json_encode(['success' => true, 'user' => $user]);
    } catch (Exception $e) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
} else {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Richiesta non valida']);
} 