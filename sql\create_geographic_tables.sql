-- <PERSON><PERSON> per i punti della griglia sismica
CREATE TABLE IF NOT EXISTS `seismic_grid_points` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `lat` decimal(10,6) NOT NULL,
  `lng` decimal(10,6) NOT NULL,
  `ag_value` decimal(10,6) DEFAULT NULL,
  `f0_value` decimal(10,6) DEFAULT NULL,
  `tc_star_value` decimal(10,6) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `lat_lng` (`lat`, `lng`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- <PERSON><PERSON> per le zone sismiche
CREATE TABLE IF NOT EXISTS `zone_sismiche` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `zona` varchar(10) NOT NULL,
  `descrizione` text DEFAULT NULL,
  `ag_min` decimal(10,6) DEFAULT NULL,
  `ag_max` decimal(10,6) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Tabella per la classificazione delle zone sismiche
CREATE TABLE IF NOT EXISTS `classificazione_zone_sismiche` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `comune_id` int(11) NOT NULL,
  `zona_id` int(11) NOT NULL,
  `data_aggiornamento` date DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `comune_id` (`comune_id`),
  KEY `zona_id` (`zona_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Tabella per i comuni
CREATE TABLE IF NOT EXISTS `comuni` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `nome` varchar(100) NOT NULL,
  `provincia` varchar(2) NOT NULL,
  `regione` varchar(50) NOT NULL,
  `lat` decimal(10,6) DEFAULT NULL,
  `lng` decimal(10,6) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `nome_provincia` (`nome`, `provincia`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Tabella per le informazioni catastali
CREATE TABLE IF NOT EXISTS `catasto_info` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `comune_id` int(11) NOT NULL,
  `foglio` varchar(10) NOT NULL,
  `particella` varchar(10) NOT NULL,
  `sub` varchar(10) DEFAULT NULL,
  `lat` decimal(10,6) DEFAULT NULL,
  `lng` decimal(10,6) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `comune_id` (`comune_id`),
  CONSTRAINT `catasto_info_ibfk_1` FOREIGN KEY (`comune_id`) REFERENCES `comuni` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci; 