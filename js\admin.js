// Gestione menu utente
function toggleUserMenu() {
    const menu = document.querySelector('.admin-user-menu');
    menu.classList.toggle('active');
}

// Chiudi menu quando si clicca fuori
document.addEventListener('click', function(e) {
    const menu = document.querySelector('.admin-user-menu');
    const userButton = document.querySelector('.admin-user');
    
    if (!userButton.contains(e.target) && menu.classList.contains('active')) {
        menu.classList.remove('active');
    }
});

// Gestione logout
async function handleLogout(e) {
    e.preventDefault();
    
    if (confirm('Sei sicuro di voler effettuare il logout?')) {
        try {
            const response = await fetch('../auth/logout.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });
            
            if (response.ok) {
                window.location.href = '../index.php';
            } else {
                const data = await response.text();
                throw new Error(data || 'Errore durante il logout');
            }
        } catch (error) {
            console.error('Errore:', error);
            alert('Si è verificato un errore durante il logout. Riprova.');
        }
    }
}

// Gestione popup documenti
function openDocPopup(popupId) {
    document.getElementById(popupId).style.display = 'block';
    // Chiudi il menu utente quando si apre un popup
    document.querySelector('.admin-user-menu').classList.remove('active');
}

function closeDocPopup(popupId) {
    document.getElementById(popupId).style.display = 'none';
}

// Chiudi i popup quando si preme ESC
document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape') {
        document.querySelectorAll('.doc-popup').forEach(popup => {
            popup.style.display = 'none';
        });
        // Chiudi anche il menu utente
        document.querySelector('.admin-user-menu').classList.remove('active');
    }
}); 