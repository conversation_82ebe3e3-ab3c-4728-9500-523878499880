# Analisi Normative Calcolo Sismico

## Documenti di Riferimento

1. Azione sismica 2008.pdf
2. 2008_D_Min_Infrastrutture_14-01-NTC-Allegati.pdf
3. allegati.pdf

## Obiettivi dell'Analisi

1. Verificare le formule di calcolo ufficiali
2. Controllare i metodi di interpolazione
3. Validare i coefficienti utilizzati
4. Assicurare la conformità con la normativa

## Struttura dell'Analisi

Per ogni documento analizzeremo:

1. **Formule di Calcolo**
   - Equazioni fondamentali
   - Coefficienti e parametri
   - Metodi di interpolazione
   - Limiti e condizioni

2. **Parametri di Input**
   - Coordinate geografiche
   - Caratteristiche del sito
   - Parametri strutturali
   - Coefficienti di amplificazione

3. **Procedure di Calcolo**
   - Sequenza operazioni
   - Validazioni intermedie
   - Controlli di coerenza
   - Arrotondamenti

4. **Output Attesi**
   - Formato risultati
   - Precisione richiesta
   - Verifiche di validità
   - Limiti accettabili

## Analisi Documento 1: Azione sismica 2008.pdf

### 1. Metodo di Interpolazione

Il documento specifica che l'interpolazione dei valori deve essere effettuata secondo la seguente procedura:

1. **Identificazione Punti Griglia**:
   - Individuare i 4 punti della griglia più vicini al punto di interesse
   - Verificare che il punto sia all'interno del quadrilatero formato dai 4 punti

2. **Formula di Interpolazione**:
   ```
   p = p1 * (1-x) * (1-y) + p2 * x * (1-y) + p3 * (1-x) * y + p4 * x * y
   ```
   dove:
   - p è il valore nel punto di interesse
   - p1, p2, p3, p4 sono i valori nei punti della griglia
   - x, y sono le distanze normalizzate (tra 0 e 1)

3. **Parametri da Interpolare**:
   - ag (accelerazione orizzontale massima)
   - F0 (fattore di amplificazione spettrale massima)
   - TC* (periodo di inizio del tratto a velocità costante)

### 2. Calcolo Periodo di Ritorno (TR)

Il periodo di ritorno TR viene calcolato come:
```
TR = -VR / ln(1-PVR)
```
dove:
- VR = VN * CU (periodo di riferimento)
- VN = vita nominale
- CU = coefficiente d'uso
- PVR = probabilità di superamento nel periodo di riferimento

### 3. Stati Limite e Probabilità

Stati Limite di Esercizio (SLE):
- SLO: PVR = 81%
- SLD: PVR = 63%

Stati Limite Ultimi (SLU):
- SLV: PVR = 10%
- SLC: PVR = 5%

## Analisi Documento 2: 2008_D_Min_Infrastrutture_14-01-NTC-Allegati.pdf

### 1. Categorie di Sottosuolo

Il documento definisce 5 categorie di sottosuolo principali:

A. Ammassi rocciosi o terreni molto rigidi
B. Rocce tenere e depositi di terreni a grana grossa molto addensati
C. Depositi di terreni a grana grossa mediamente addensati
D. Depositi di terreni a grana grossa scarsamente addensati
E. Terreni con caratteristiche meccaniche particolarmente scadenti

### 2. Coefficienti di Amplificazione Stratigrafica (SS e CC)

Per ogni categoria di sottosuolo, si applicano i seguenti coefficienti:

**Categoria A:**
- SS = 1.00
- CC = 1.00

**Categoria B:**
- SS = 1.00 ≤ 1.40 - 0.40 * F0 * ag/g ≤ 1.20
- CC = 1.10 * (TC*)-0.20

**Categoria C:**
- SS = 1.00 ≤ 1.70 - 0.60 * F0 * ag/g ≤ 1.50
- CC = 1.05 * (TC*)-0.33

**Categoria D:**
- SS = 0.90 ≤ 2.40 - 1.50 * F0 * ag/g ≤ 1.80
- CC = 1.25 * (TC*)-0.50

**Categoria E:**
- SS = 1.00 ≤ 2.00 - 1.10 * F0 * ag/g ≤ 1.60
- CC = 1.15 * (TC*)-0.40

### 3. Coefficienti Topografici (ST)

Categorie topografiche e relativi coefficienti:

T1. ST = 1.0 (superficie pianeggiante)
T2. ST = 1.2 (pendii con inclinazione > 15°)
T3. ST = 1.2 (rilievi con larghezza cresta < altezza)
T4. ST = 1.4 (rilievi con larghezza cresta molto minore dell'altezza)

### 4. Calcolo dei Periodi TC e TB

Il periodo TC è espresso in secondi e viene calcolato come:
```
TC = CC * TC*
```
dove:
- TC* è il periodo di riferimento (da interpolazione) espresso in secondi
- CC è il coefficiente di categoria del sottosuolo (adimensionale)

Il periodo TB, anch'esso in secondi, si calcola come:
```
TB = TC / 3
```

IMPORTANTE:
1. TC* viene ottenuto per interpolazione dai valori della griglia di riferimento
2. CC dipende dalla categoria di sottosuolo secondo le formule:
   - Categoria A: CC = 1.00
   - Categoria B: CC = 1.10 * (TC*)^(-0.20)
   - Categoria C: CC = 1.05 * (TC*)^(-0.33)
   - Categoria D: CC = 1.25 * (TC*)^(-0.50)
   - Categoria E: CC = 1.15 * (TC*)^(-0.40)

3. I valori di TC e TB devono essere arrotondati a 3 decimali
4. TC e TB non possono essere negativi o nulli
5. TC deve essere sempre maggiore di TB

Esempio di calcolo per categoria B:
- TC* = 0.306 s (da interpolazione)
- CC = 1.10 * (0.306)^(-0.20) = 1.357
- TC = 1.357 * 0.306 = 0.415 s
- TB = 0.415 / 3 = 0.138 s

## Analisi Documento 3: allegati.pdf

### 1. Spettro di Risposta Elastico in Accelerazione

Lo spettro di risposta elastico in accelerazione è definito dalle seguenti espressioni:

1. **Per 0 ≤ T < TB**:
```
Se(T) = ag * S * η * F0 * [(T/TB) + (1/(η*F0)) * (1 - T/TB)]
```

2. **Per TB ≤ T < TC**:
```
Se(T) = ag * S * η * F0
```

3. **Per TC ≤ T < TD**:
```
Se(T) = ag * S * η * F0 * (TC/T)
```

4. **Per TD ≤ T**:
```
Se(T) = ag * S * η * F0 * (TC*TD/T²)
```

dove:
- S = SS * ST (coefficiente che tiene conto della categoria di sottosuolo e delle condizioni topografiche)
- η = √(10/(5+ξ)) ≥ 0.55 (fattore che altera lo spettro elastico per coefficienti di smorzamento viscosi ξ diversi dal 5%)
- T = periodo di vibrazione
- F0 = fattore che quantifica l'amplificazione spettrale massima
- TC = CC * TC* (periodo corrispondente all'inizio del tratto a velocità costante)
- TB = TC/3 (periodo corrispondente all'inizio del tratto ad accelerazione costante)
- TD = 4.0 * (ag/g) + 1.6 (periodo corrispondente all'inizio del tratto a spostamento costante)

### 2. Fattore di Struttura (q)

Il fattore di struttura q da utilizzare per ciascuno stato limite è:

- **Stati Limite di Esercizio (SLE)**:
  - SLO: q = 1
  - SLD: q = 1

- **Stati Limite Ultimi (SLU)**:
  - SLV: q > 1 (dipende dalla tipologia strutturale)
  - SLC: q > 1 (dipende dalla tipologia strutturale)

Il valore di q dipende da:
1. Materiale strutturale (CA, CAP, Acciaio, Legno, Muratura)
2. Tipologia strutturale
3. Regolarità in pianta e in altezza
4. Classe di duttilità

### 3. Spettro di Progetto

Lo spettro di progetto Sd(T) si ottiene dallo spettro elastico sostituendo η con 1/q:

```
Sd(T) = Se(T) / q
```

## Stato Analisi

- [x] Azione sismica 2008.pdf
- [x] 2008_D_Min_Infrastrutture_14-01-NTC-Allegati.pdf
- [x] allegati.pdf

## Note e Osservazioni

1. **Differenze Riscontrate**:
   - Il nostro codice attuale non implementa correttamente la formula di interpolazione
   - I periodi di ritorno calcolati sembrano corretti
   - La precisione dei risultati è conforme (3 decimali)

2. **Azioni Necessarie**:
   - Correggere la formula di interpolazione
   - Aggiungere validazioni sui limiti geografici
   - Implementare controlli di coerenza sui risultati

3. **Nuove Differenze Riscontrate**:
   - I coefficienti di amplificazione stratigrafica devono rispettare limiti precisi
   - Il calcolo di TC e TB deve considerare il coefficiente CC
   - I coefficienti topografici devono essere applicati correttamente

4. **Ulteriori Azioni Necessarie**:
   - Implementare i controlli sui limiti dei coefficienti SS
   - Aggiungere il calcolo corretto di TC e TB
   - Verificare l'applicazione dei coefficienti topografici

5. **Ulteriori Differenze Riscontrate**:
   - Il calcolo dello smorzamento η non è implementato
   - Il fattore di struttura q non è considerato
   - Lo spettro di progetto non è calcolato

6. **Azioni Finali Necessarie**:
   - Implementare il calcolo completo dello spettro elastico
   - Aggiungere il calcolo del fattore η per diversi smorzamenti
   - Implementare il calcolo dello spettro di progetto con fattore q
   - Aggiungere validazioni per tutti i parametri di input

*L'analisi di tutti i documenti è completata. Possiamo procedere con l'implementazione delle correzioni necessarie.* 