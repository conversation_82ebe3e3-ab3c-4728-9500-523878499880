<?php
require_once '../includes/db_config.php';
require_once '../includes/Logger.php';

session_start();

// Verifica se l'utente è loggato e ha il ruolo di admin
if (!isset($_SESSION['user_id']) || !isset($_SESSION['role']) || $_SESSION['role'] !== 'admin') {
    die('Accesso non autorizzato');
}

$logger = Logger::getInstance();
$logger->info('Inizio installazione tabella settings');

try {
    $conn = getConnection();
    
    // Leggi il file SQL
    $sql = file_get_contents('../sql/create_settings_table.sql');
    
    if ($sql === false) {
        throw new Exception("Impossibile leggere il file SQL");
    }
    
    $logger->debug('File SQL letto correttamente');
    
    // Esegui le query
    $result = $conn->exec($sql);
    
    $logger->info('Tabella settings creata con successo');
    echo "Tabella settings creata con successo!<br>";
    echo "<a href='settings.php'>Torna alle impostazioni</a>";

} catch (Exception $e) {
    $logger->error('Errore durante l\'installazione della tabella settings', ['error' => $e->getMessage()]);
    echo "Errore durante l'installazione: " . $e->getMessage();
}
?>
