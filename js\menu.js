// Protezione base contro l'accesso diretto al file
if (typeof window === 'undefined') {
    throw new Error('Accesso non consentito');
}

const EdisisMenu = {
    init: function() {
        const menuToggle = document.getElementById('menuToggle');
        const sidebar = document.getElementById('sidebar');
        const overlay = document.getElementById('overlay');

        // Toggle menu
        function toggleMenu() {
            sidebar.classList.toggle('active');
            overlay.classList.toggle('active');
            document.body.classList.toggle('no-scroll');
        }

        // Event listeners
        menuToggle.addEventListener('click', toggleMenu);
        overlay.addEventListener('click', toggleMenu);

        // Chiusura menu mobile su click link
        document.querySelectorAll('.nav-item').forEach(item => {
            item.addEventListener('click', () => {
                if (window.innerWidth <= 768) toggleMenu();
            });
        });

        // Gestione resize
        window.addEventListener('resize', () => {
            if (window.innerWidth > 768) {
                sidebar.classList.remove('active');
                overlay.classList.remove('active');
                document.body.classList.remove('no-scroll');
            }
        });

        // Logout
        document.getElementById('logoutBtn').addEventListener('click', () => {
            window.location.href = 'logout.php';
        });
    }
};

document.addEventListener('DOMContentLoaded', () => {
    EdisisMenu.init();
});
