document.addEventListener('DOMContentLoaded',function(){function getInitials(fullName){const parts=fullName.trim().split(' ');let initials='';if(parts[0]){initials+=parts[0].charAt(0);}
if(parts[1]){initials+=parts[1].charAt(0);}
return initials.toUpperCase();}
const accountLink=document.querySelector('[data-section="account"]');const accountPopup=document.getElementById('accountPopup');if(!accountPopup){console.error('Elemento popup account non trovato');return;}
if(!accountLink){console.error('Link account non trovato');return;}
accountLink.addEventListener('click',function(e){e.preventDefault();e.stopPropagation();openAccountPopup();});function openAccountPopup(){console.log('Apertura popup account...');accountPopup.style.display='flex';loadAccountData();document.addEventListener('keydown',handleEscKey);accountPopup.addEventListener('click',handleOverlayClick);}
window.closeAccountPopup=function(){console.log('Chiusura popup account...');accountPopup.style.display='none';document.removeEventListener('keydown',handleEscKey);accountPopup.removeEventListener('click',handleOverlayClick);}
function handleEscKey(e){if(e.key==='Escape'){closeAccountPopup();}}
function handleOverlayClick(e){if(e.target===accountPopup){closeAccountPopup();}}
function loadAccountData(){fetch('includes/get_account_data.php').then(response=>response.json()).then(data=>{if(data.success){document.getElementById('nome').value=data.user.nome||'';document.getElementById('cognome').value=data.user.cognome||'';document.getElementById('email').value=data.user.email||'';}else{console.error('Errore nel caricamento dei dati:',data.message);}}).catch(error=>console.error('Errore:',error));}
const accountForm=document.getElementById('accountForm');if(accountForm){accountForm.addEventListener('submit',function(e){e.preventDefault();const formData=new FormData();formData.append('nome',document.getElementById('nome').value);formData.append('cognome',document.getElementById('cognome').value);fetch('includes/update_account.php',{method:'POST',body:formData}).then(response=>response.json()).then(data=>{if(data.success){const fullName=`${document.getElementById('nome').value} ${document.getElementById('cognome').value}`;document.querySelector('.username').textContent=fullName;document.querySelector('.user-avatar').textContent=getInitials(fullName);alert('Dati aggiornati con successo');}else{alert('Errore nell\'aggiornamento dei dati: '+data.message);}}).catch(error=>{console.error('Errore:',error);alert('Errore nella comunicazione con il server');});});}
const passwordForm=document.getElementById('passwordForm');if(passwordForm){passwordForm.addEventListener('submit',function(e){e.preventDefault();const formData=new FormData(this);fetch('includes/update_password.php',{method:'POST',body:formData}).then(response=>response.json()).then(data=>{if(data.success){alert('Password aggiornata con successo');this.reset();}else{alert('Errore nell\'aggiornamento della password: '+data.message);}}).catch(error=>{console.error('Errore:',error);alert('Errore nella comunicazione con il server');});});}});