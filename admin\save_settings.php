<?php
require_once '../includes/db_config.php';
require_once '../includes/Logger.php';

session_start();

// Verifica se l'utente è loggato e ha il ruolo di admin
if (!isset($_SESSION['user_id']) || !isset($_SESSION['role']) || $_SESSION['role'] !== 'admin') {
    http_response_code(403);
    die(json_encode(['success' => false, 'message' => 'Accesso non autorizzato']));
}

$logger = Logger::getInstance();
$logger->info('Tentativo di salvataggio impostazioni');

try {
    // Leggi il JSON inviato
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (json_last_error() !== JSON_ERROR_NONE) {
        throw new Exception('Dati JSON non validi');
    }
    
    $logger->debug('Dati ricevuti', $input);
    
    $conn = getConnection();
    
    // Prepara la query
    $stmt = $conn->prepare("INSERT INTO settings (`key`, `value`) VALUES (?, ?) ON DUPLICATE KEY UPDATE `value` = VALUES(`value`)");
    
    // Aggiorna ogni impostazione
    foreach ($input as $key => $value) {
        $stmt->execute([$key, $value]);
        $logger->debug("Impostazione aggiornata", ['key' => $key, 'value' => $value]);
    }
    
    $logger->info('Impostazioni salvate con successo');
    echo json_encode(['success' => true, 'message' => 'Impostazioni salvate con successo']);

} catch (Exception $e) {
    $logger->error('Errore durante il salvataggio delle impostazioni', ['error' => $e->getMessage()]);
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => $e->getMessage()]);
}
?>
