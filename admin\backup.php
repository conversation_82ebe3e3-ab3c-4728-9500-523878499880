<?php
require_once '../includes/admin_header.php';
?>

<div class="admin-wrapper">
    <div class="content">
        <h1>Backup Sistema</h1>
        <p class="subtitle">Gestisci i backup del database e dei file del sistema</p>

        <div class="backup-section">
            <!-- Card Crea Backup -->
            <div class="backup-card create-backup">
                <h2><i class="fas fa-database"></i> Crea Backup</h2>
                <div class="backup-content">
                    <p>Crea un backup completo del database e dei file del sistema</p>
                    <div class="progress-container" id="backupProgress">
                        <div class="progress-bar">
                            <div class="progress-fill"></div>
                        </div>
                        <div class="progress-text">0%</div>
                    </div>
                    <div class="status-message" id="statusMessage">In attesa...</div>
                    <button id="startBackup" class="backup-btn">
                        <i class="fas fa-download"></i> Avvia Backup
                    </button>
                </div>
            </div>

            <!-- Card Log del Backup -->
            <div class="backup-card backup-log">
                <h2><i class="fas fa-terminal"></i> Log del Backup</h2>
                <div class="log-container" id="backupLog">
                    <div class="log-content"></div>
                </div>
            </div>

            <!-- Card Lista Backup -->
            <div class="backup-card backup-list">
                <h2><i class="fas fa-history"></i> Backup Recenti</h2>
                <div class="backup-list-container">
                    <table class="backup-table">
                        <thead>
                            <tr>
                                <th>Data</th>
                                <th>Dimensione</th>
                                <th>Stato</th>
                                <th>Azioni</th>
                            </tr>
                        </thead>
                        <tbody id="backupTableBody">
                            <!-- I backup verranno caricati dinamicamente qui -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <?php include 'admin_footer.php'; ?>
</div>

<style>
.admin-wrapper {
    min-height: calc(100vh - 50px);
    display: flex;
    flex-direction: column;
    background-color: #121212;
}

.content {
    max-width: 1200px;
    margin: 0.5rem auto;
    padding: 0 1rem;
    height: calc(100vh - 120px);
    overflow: hidden;
    width: 100%;
    position: relative;
    background-color: #121212;
}

h1 {
    color: #FF7043;
    font-size: 1.5rem;
    margin: 0 0 0.25rem 0;
    font-weight: 500;
}

.subtitle {
    color: #BDBDBD;
    margin-bottom: 1rem;
    font-size: 0.9rem;
}

.backup-section {
    display: grid;
    grid-template-columns: repeat(12, 1fr);
    gap: 1rem;
    height: calc(100vh - 180px);
}

.backup-card {
    background: #1E1E1E;
    border-radius: 8px;
    padding: 1rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.2);
}

/* Layout delle card */
.create-backup {
    grid-column: span 4;
}

.backup-log {
    grid-column: span 8;
}

.backup-list {
    grid-column: span 12;
}

.backup-card h2 {
    color: #FF7043;
    font-size: 1.1rem;
    margin: 0 0 0.75rem 0;
    padding-bottom: 0.4rem;
    border-bottom: 2px solid #FF7043;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.backup-content {
    color: #FFFFFF;
}

.backup-content p {
    margin-bottom: 1rem;
    color: #BDBDBD;
    font-size: 0.9rem;
}

/* Progress Bar */
.progress-container {
    margin-bottom: 0.75rem;
}

.progress-bar {
    background: #333333;
    border-radius: 4px;
    height: 16px;
    overflow: hidden;
    position: relative;
}

.progress-fill {
    background: #FF7043;
    height: 100%;
    width: 0%;
    transition: width 0.3s ease;
}

.progress-text {
    color: #FFFFFF;
    text-align: center;
    margin-top: 0.25rem;
    font-size: 0.8rem;
}

.status-message {
    color: #BDBDBD;
    margin: 0.75rem 0;
    font-size: 0.8rem;
}

/* Pulsante Backup */
.backup-btn {
    background: #FF7043;
    color: #FFFFFF;
    border: none;
    border-radius: 4px;
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    width: 100%;
    justify-content: center;
}

.backup-btn:hover {
    background: #F4511E;
    transform: translateY(-2px);
}

.backup-btn:disabled {
    background: #666666;
    cursor: not-allowed;
    transform: none;
}

/* Log Container */
.log-container {
    background: #242424;
    border-radius: 4px;
    padding: 0.75rem;
    height: calc(100% - 3rem);
    overflow-y: auto;
}

.log-content {
    font-family: monospace;
    font-size: 0.8rem;
    color: #BDBDBD;
}

.log-entry {
    padding: 0.4rem;
    border-bottom: 1px solid #333333;
}

.log-entry:last-child {
    border-bottom: none;
}

/* Tabella Backup */
.backup-list-container {
    overflow-x: auto;
}

.backup-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.9rem;
}

.backup-table th,
.backup-table td {
    padding: 0.5rem;
    text-align: left;
    border-bottom: 1px solid #333;
}

.backup-table th {
    color: #FF7043;
    font-weight: 500;
}

.backup-table td {
    color: #BDBDBD;
}

/* Responsive */
@media (max-width: 1200px) {
    .create-backup,
    .backup-log {
        grid-column: span 12;
    }
}

@media (max-width: 768px) {
    .backup-section {
        height: auto;
        overflow-y: auto;
    }

    .log-container {
        height: 200px;
    }

    .backup-table {
        font-size: 0.8rem;
    }
}
</style>

<script>
// Funzione per aggiornare la progress bar
function updateProgress(percent) {
    const progressFill = document.querySelector('.progress-fill');
    const progressText = document.querySelector('.progress-text');
    progressFill.style.width = `${percent}%`;
    progressText.textContent = `${percent}%`;
}

// Funzione per aggiungere un log
function addLog(message, type = 'info') {
    const logContent = document.querySelector('.log-content');
    const time = new Date().toLocaleTimeString();
    const logEntry = document.createElement('div');
    logEntry.className = `log-entry log-${type}`;
    logEntry.innerHTML = `<span class="log-time">[${time}]</span> <span class="log-info">${message}</span>`;
    logContent.appendChild(logEntry);
    logContent.scrollTop = logContent.scrollHeight;
}

// Funzione per formattare la dimensione del file
function formatSize(bytes) {
    if (bytes === 0) return '0 MB';
    const mb = bytes / (1024 * 1024);
    return mb.toFixed(2) + ' MB';
}

// Funzione per caricare la lista dei backup
function loadBackupList() {
    fetch('../api/get_backups.php')
        .then(response => {
            if (!response.ok) {
                return response.json().then(data => {
                    throw new Error(data.error || 'Errore nel caricamento dei backup');
                });
            }
            return response.json();
        })
        .then(data => {
            const tbody = document.getElementById('backupTableBody');
            tbody.innerHTML = data.map(backup => `
                <tr>
                    <td>${new Date(backup.created_at).toLocaleString('it-IT')}</td>
                    <td>${formatSize(backup.size)}</td>
                    <td><span class="status-badge">Completato</span></td>
                    <td>
                        <button class="action-btn" onclick="downloadBackup('${backup.filename}')">
                            <i class="fas fa-download"></i>
                        </button>
                        <button class="action-btn delete" onclick="deleteBackup('${backup.filename}')">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                </tr>
            `).join('');
        })
        .catch(error => {
            console.error('Errore caricamento backup:', error);
            addLog(error.message || 'Errore nel caricamento della lista backup', 'error');
        });
}

// Funzione per scaricare un backup
function downloadBackup(filename) {
    const downloadLink = document.createElement('a');
    downloadLink.href = `../api/download_backup.php?file=${filename}`;
    downloadLink.download = filename;
    downloadLink.style.display = 'none';
    document.body.appendChild(downloadLink);
    downloadLink.click();
    document.body.removeChild(downloadLink);
}

// Funzione per eliminare un backup
function deleteBackup(filename) {
    if (confirm('Sei sicuro di voler eliminare questo backup? Questa azione non può essere annullata.')) {
        fetch('../api/delete_backup.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ filename })
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                addLog('Backup eliminato con successo', 'success');
                loadBackupList();
            } else {
                throw new Error(data.error || 'Errore durante l\'eliminazione');
            }
        })
        .catch(error => {
            console.error('Errore eliminazione backup:', error);
            addLog('Errore durante l\'eliminazione del backup', 'error');
        });
    }
}

// Gestione del click sul pulsante di backup
document.getElementById('startBackup').addEventListener('click', async function() {
    try {
        // Disabilita il pulsante
        this.disabled = true;
        
        // Mostra progresso
        updateProgress(0);
        document.getElementById('statusMessage').textContent = 'Avvio backup...';
        
        // Esegue il backup
        const response = await fetch('../api/backup_process_zip.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        });
        
        if (!response.ok) {
            throw new Error('Errore durante il backup');
        }
        
        const result = await response.json();
        
        if (result.success) {
            // Aggiorna progresso
            updateProgress(100);
            document.getElementById('statusMessage').textContent = 'Backup completato!';
            
            // Scarica il file
            downloadBackup(result.filename);
            
            // Aggiorna la lista dei backup
            updateBackupList();
        } else {
            throw new Error(result.error || 'Errore sconosciuto');
        }
    } catch (error) {
        console.error('Errore:', error);
        document.getElementById('statusMessage').textContent = 'Errore: ' + error.message;
        updateProgress(0);
    } finally {
        // Riabilita il pulsante
        this.disabled = false;
    }
});

// Funzione per aggiornare la lista dei backup
async function updateBackupList() {
    try {
        const response = await fetch('../api/get_backups.php');
        const data = await response.json();
        
        const tableBody = document.getElementById('backupTableBody');
        tableBody.innerHTML = '';
        
        data.forEach(backup => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${new Date(backup.created_at).toLocaleString()}</td>
                <td>${formatBytes(backup.size)}</td>
                <td><span class="status-success">Completato</span></td>
                <td>
                    <button onclick="downloadBackup('${backup.filename}')" class="action-btn download">
                        <i class="fas fa-download"></i>
                    </button>
                    <button onclick="deleteBackup('${backup.filename}')" class="action-btn delete">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            `;
            tableBody.appendChild(row);
        });
    } catch (error) {
        console.error('Errore aggiornamento lista:', error);
    }
}

// Funzione per formattare i bytes
function formatBytes(bytes, decimals = 2) {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const dm = decimals < 0 ? 0 : decimals;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
}

// Carica la lista dei backup all'avvio
document.addEventListener('DOMContentLoaded', updateBackupList);
</script>

</body>
</html>
