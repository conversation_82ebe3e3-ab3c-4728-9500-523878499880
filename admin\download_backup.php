<?php
session_start();
require_once '../includes/Logger.php';

// Verifica se l'utente è autenticato
if (!isset($_SESSION['user_id'])) {
    die('Accesso non autorizzato');
}

$logger = Logger::getInstance();

// Verifica se è stato specificato un file
if (!isset($_GET['file'])) {
    $logger->error('Tentativo di download senza specificare il file');
    die('File non specificato');
}

// Pulisci il nome del file
$filename = basename($_GET['file']);
$filepath = sys_get_temp_dir() . '/' . $filename;

// Verifica che il file esista e sia un backup valido
if (!file_exists($filepath) || !preg_match('/^asdp_backup_\d{4}-\d{2}-\d{2}_\d{2}-\d{2}-\d{2}\.zip$/', $filename)) {
    $logger->error('Tentativo di download di un file non valido', ['file' => $filename]);
    die('File non valido');
}

// Log del download
$logger->info('Download backup', ['file' => $filename, 'user' => $_SESSION['user_id']]);

// Invia il file
header('Content-Type: application/zip');
header('Content-Disposition: attachment; filename="' . $filename . '"');
header('Content-Length: ' . filesize($filepath));
header('Cache-Control: no-cache');

readfile($filepath);

// Elimina il file temporaneo dopo il download
unlink($filepath);
?>
