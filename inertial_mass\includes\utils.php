<?php
/**
 * utils.php - Funzioni di utilità per il modulo massa inerziale
 * Path: /inertial_mass/includes/utils.php
 */

/**
 * Verifica se l'utente è autenticato
 * @return bool True se autenticato
 */
function isAuthenticated() {
    return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
}

/**
 * Sanitizza l'input
 * @param mixed $data Dato da sanitizzare
 * @return mixed Dato sanitizzato
 */
function sanitizeInput($data) {
    if (is_array($data)) {
        return array_map('sanitizeInput', $data);
    }
    
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);
    return $data;
}

/**
 * Valida coordinate geografiche
 * @param float $lat Latitudine
 * @param float $lon Longitudine
 * @return bool True se valide
 */
function validateCoordinates($lat, $lon) {
    return is_numeric($lat) && is_numeric($lon) &&
           $lat >= -90 && $lat <= 90 &&
           $lon >= -180 && $lon <= 180;
}

/**
 * Formatta numero per visualizzazione
 * @param float $number Numero da formattare
 * @param int $decimals Decimali
 * @return string Numero formattato
 */
function formatNumber($number, $decimals = 2) {
    return number_format($number, $decimals, ',', '.');
}

/**
 * Calcola il hash per la cache
 * @param array $data Dati da hashare
 * @return string Hash
 */
function getCacheKey($data) {
    return md5(json_encode($data));
}

/**
 * Verifica se esiste cache valida
 * @param string $key Chiave cache
 * @param string $cacheDir Directory cache
 * @return mixed Dati cache o false
 */
function getFromCache($key, $cacheDir = '../cache') {
    $cacheFile = "$cacheDir/$key.json";
    
    if (!file_exists($cacheFile)) {
        return false;
    }
    
    $cacheData = json_decode(file_get_contents($cacheFile), true);
    
    if (!$cacheData || !isset($cacheData['expires'])) {
        return false;
    }
    
    // Verifica scadenza
    if (time() > $cacheData['expires']) {
        unlink($cacheFile); // Rimuovi cache scaduta
        return false;
    }
    
    return $cacheData['data'];
}

/**
 * Pulisce la cache scaduta
 * @param string $cacheDir Directory cache
 * @param int $maxAge Età massima in secondi (default 24 ore)
 */
function cleanExpiredCache($cacheDir = '../cache', $maxAge = 86400) {
    if (!is_dir($cacheDir)) {
        return;
    }
    
    $files = glob("$cacheDir/*.json");
    $now = time();
    
    foreach ($files as $file) {
        if (filemtime($file) < ($now - $maxAge)) {
            unlink($file);
        }
    }
}

/**
 * Genera un ID univoco per il calcolo
 * @return string ID calcolo
 */
function generateCalculationId() {
    return 'calc_' . date('Ymd_His') . '_' . substr(uniqid(), -6);
}

/**
 * Mappa il tipo di struttura dal codice al nome
 * @param string $code Codice struttura
 * @return string Nome struttura
 */
function getStructureTypeName($code) {
    $types = [
        'concrete' => 'Cemento Armato',
        'steel' => 'Acciaio',
        'masonry' => 'Muratura',
        'wood' => 'Legno',
        'mixed' => 'Mista'
    ];
    
    return $types[$code] ?? $code;
}

/**
 * Mappa il tipo di solaio dal codice al nome
 * @param string $code Codice solaio
 * @return string Nome solaio
 */
function getSlabTypeName($code) {
    $types = [
        'hollow_brick' => 'Laterocemento',
        'solid_slab' => 'Soletta Piena',
        'steel_deck' => 'Lamiera Grecata',
        'wood_slab' => 'Legno',
        'prefab' => 'Prefabbricato'
    ];
    
    return $types[$code] ?? $code;
}

/**
 * Mappa la destinazione d'uso dal codice al nome
 * @param string $code Codice uso
 * @return string Nome uso
 */
function getUseName($code) {
    $uses = [
        'residential' => 'Residenziale',
        'office' => 'Uffici',
        'commercial' => 'Commerciale',
        'industrial' => 'Industriale',
        'storage' => 'Magazzino'
    ];
    
    return $uses[$code] ?? $code;
}

/**
 * Implementa rate limiting semplice
 * @param string $identifier Identificatore (es. user_id)
 * @param int $maxRequests Richieste massime
 * @param int $timeWindow Finestra temporale in secondi
 * @return bool True se può procedere, false se limite raggiunto
 */
function checkRateLimit($identifier, $maxRequests = 10, $timeWindow = 60) {
    $cacheDir = '../cache/rate_limit';
    if (!is_dir($cacheDir)) {
        mkdir($cacheDir, 0755, true);
    }
    
    $file = "$cacheDir/" . md5($identifier) . ".json";
    $now = time();
    
    // Leggi dati esistenti
    if (file_exists($file)) {
        $data = json_decode(file_get_contents($file), true);
        $requests = array_filter($data['requests'] ?? [], function($timestamp) use ($now, $timeWindow) {
            return ($now - $timestamp) < $timeWindow;
        });
    } else {
        $requests = [];
    }
    
    // Verifica limite
    if (count($requests) >= $maxRequests) {
        return false;
    }
    
    // Aggiungi nuova richiesta
    $requests[] = $now;
    
    // Salva
    file_put_contents($file, json_encode(['requests' => array_values($requests)]));
    
    return true;
}
