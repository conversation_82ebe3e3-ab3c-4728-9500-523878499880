class UI{static init(){this.setupMenuToggle();this.setupLogout();this.setupSearchClear();this.setupNavigation();}
static setupMenuToggle(){const menuToggle=document.getElementById('menuToggle');const sidebar=document.getElementById('sidebar');const overlay=document.getElementById('overlay');if(menuToggle&&sidebar&&overlay){const toggleMenu=()=>{sidebar.classList.toggle('active');overlay.classList.toggle('active');document.body.classList.toggle('no-scroll');};menuToggle.addEventListener('click',toggleMenu);overlay.addEventListener('click',toggleMenu);}}
static setupLogout(){const logoutBtn=document.getElementById('logoutBtn');if(logoutBtn){logoutBtn.addEventListener('click',async()=>{try{const basePath=window.location.pathname.substring(0,window.location.pathname.lastIndexOf('/')+1);const response=await fetch(basePath+'auth/logout.php',{method:'POST',headers:{'Content-Type':'application/json'},credentials:'same-origin'});if(!response.ok){throw new Error('Errore nella risposta del server');}
const data=await response.json();if(data.success){window.location.href=basePath+'logout.php';}else{this.showError(data.message||'Errore durante il logout');}}catch(error){console.error('Errore:',error);this.showError('Errore durante il logout. Riprova.');}});}}
static setupSearchClear(){const clearBtn=document.getElementById('clearSearch');const searchInput=document.getElementById('addressSearch');if(clearBtn&&searchInput){clearBtn.addEventListener('click',()=>{searchInput.value='';searchInput.focus();});}}
static setupNavigation(){const navItems=document.querySelectorAll('.nav-item');navItems.forEach(item=>{item.addEventListener('mouseenter',(e)=>{const tooltip=item.querySelector('.nav-tooltip');if(tooltip){const rect=item.getBoundingClientRect();tooltip.style.top=`${rect.top}px`;}});item.addEventListener('click',(e)=>{if(item.classList.contains('disabled')){e.preventDefault();return;}
e.preventDefault();navItems.forEach(i=>i.classList.remove('active'));item.classList.add('active');const section=item.getAttribute('data-section');this.changeSection(section);});});}
static changeSection(section){console.log('Cambio sezione:',section);}
static showError(message){const errorDiv=document.createElement('div');errorDiv.className='error-message';errorDiv.textContent=message;errorDiv.style.position='fixed';errorDiv.style.top='20px';errorDiv.style.left='50%';errorDiv.style.transform='translateX(-50%)';errorDiv.style.backgroundColor='#ff3333';errorDiv.style.color='white';errorDiv.style.padding='10px 20px';errorDiv.style.borderRadius='5px';errorDiv.style.zIndex='9999';document.body.appendChild(errorDiv);setTimeout(()=>{errorDiv.remove();},3000);}}
document.addEventListener('DOMContentLoaded',()=>{UI.init();});