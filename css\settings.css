.settings-popup {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: #1E1E1E;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    width: 90%;
    max-width: 800px;
    max-height: 90vh;
    overflow-y: auto;
    z-index: 1000;
    scrollbar-width: thin;
    scrollbar-color: #FF7043 #1E1E1E;
}

.settings-popup::-webkit-scrollbar {
    width: 6px;
}

.settings-popup::-webkit-scrollbar-track {
    background: #1E1E1E;
    border-radius: 3px;
}

.settings-popup::-webkit-scrollbar-thumb {
    background-color: #FF7043;
    border-radius: 3px;
}

.settings-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid #333333;
    position: sticky;
    top: 0;
    background: #1E1E1E;
    z-index: 2;
}

.settings-header h2 {
    margin: 0;
    color: #FF7043;
    font-size: 1.5rem;
}

.close-settings {
    background: none;
    border: none;
    padding: 8px;
    cursor: pointer;
    color: #888888;
    transition: color 0.2s;
}

.close-settings:hover {
    color: #FF7043;
}

.close-settings svg {
    width: 20px;
    height: 20px;
}

.settings-content {
    padding: 1.5rem;
}

.settings-section {
    margin-bottom: 2rem;
}

.settings-section h3 {
    color: #FF7043;
    font-size: 1.2rem;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #FF7043;
}

.settings-group {
    margin-bottom: 1.5rem;
}

.setting-label {
    display: block;
    margin-bottom: 0.5rem;
    color: #CCCCCC;
    font-weight: 500;
}

.setting-select {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid #333333;
    border-radius: 4px;
    font-size: 1rem;
    color: #FFFFFF;
    background-color: #2A2A2A;
    transition: border-color 0.2s;
}

.setting-select:focus {
    border-color: #FF7043;
    outline: none;
}

.setting-description {
    margin-top: 0.5rem;
    font-size: 0.9rem;
    color: #888888;
}

/* Switch style */
.switch-label {
    display: flex;
    align-items: center;
    cursor: pointer;
    user-select: none;
}

.switch {
    position: relative;
    display: inline-block;
    width: 44px;
    height: 24px;
    background-color: #ccc;
    border-radius: 12px;
    margin-right: 10px;
    transition: background-color 0.2s;
}

.switch-label input:checked + .switch {
    background-color: #FF7043;
}

.switch:before {
    content: '';
    position: absolute;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background-color: white;
    top: 2px;
    left: 2px;
    transition: transform 0.2s;
}

.switch-label input:checked + .switch:before {
    transform: translateX(20px);
}

.switch-label input {
    display: none;
}

/* Buttons */
.settings-actions {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    margin-top: 2rem;
    padding-top: 1rem;
    border-top: 1px solid #e0e0e0;
}

.btn-reset, .btn-save {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 4px;
    font-size: 1rem;
    cursor: pointer;
    transition: background-color 0.2s;
}

.btn-reset {
    background-color: #2A2A2A;
    color: #FFFFFF;
    border: 1px solid #333333;
}

.btn-reset:hover {
    background-color: #333333;
}

.btn-save {
    background-color: #FF7043;
    color: #FFFFFF;
}

.btn-save:hover {
    background-color: #F4511E;
}

/* Overlay */
.settings-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: none;
    z-index: 999;
}

.settings-overlay.active {
    display: block;
}

/* Responsive */
@media (max-width: 768px) {
    .settings-popup {
        width: 95%;
        max-height: 95vh;
    }

    .settings-header {
        padding: 1rem;
    }

    .settings-content {
        padding: 1rem;
    }

    .settings-actions {
        flex-direction: column;
    }

    .btn-reset, .btn-save {
        width: 100%;
    }
} 