<?php
require_once '../includes/db_config.php';
session_start();

// Verifica autenticazione
if (!isset($_SESSION['user_id']) || !isset($_SESSION['role']) || $_SESSION['role'] !== 'admin') {
    header('HTTP/1.1 403 Forbidden');
    echo json_encode(['error' => 'Accesso non autorizzato']);
    exit;
}

header('Content-Type: application/json');

try {
    $conn = getConnection();
    
    // Numero totale utenti (solo ruolo 'user')
    $stmt = $conn->query("SELECT COUNT(*) as total_users FROM users WHERE role = 'user'");
    $totalUsers = $stmt->fetch(PDO::FETCH_ASSOC)['total_users'];
    
    // Numero di accessi di oggi (solo utenti non admin)
    $stmt = $conn->query("SELECT COUNT(DISTINCT al.user_id) as active_today 
                        FROM access_logs al 
                        JOIN users u ON al.user_id = u.id 
                        WHERE DATE(al.timestamp) = CURDATE() 
                        AND u.role = 'user'
                        AND al.action = 'Login effettuato'
                        AND al.user_id NOT IN (SELECT id FROM users WHERE role = 'admin')");
    $activeToday = $stmt->fetch(PDO::FETCH_ASSOC)['active_today'];
    
    // Numero di accessi negli ultimi 7 giorni (solo utenti non admin)
    $stmt = $conn->query("SELECT COUNT(DISTINCT al.user_id) as active_week 
                        FROM access_logs al 
                        JOIN users u ON al.user_id = u.id 
                        WHERE al.timestamp >= DATE_SUB(NOW(), INTERVAL 7 DAY) 
                        AND u.role = 'user'
                        AND al.action = 'Login effettuato'
                        AND al.user_id NOT IN (SELECT id FROM users WHERE role = 'admin')");
    $activeWeek = $stmt->fetch(PDO::FETCH_ASSOC)['active_week'];
    
    // Spazio utilizzato dai backup
    $backupDir = __DIR__ . '/../backups';
    $backupSize = 0;
    if (is_dir($backupDir)) {
        foreach (new RecursiveIteratorIterator(new RecursiveDirectoryIterator($backupDir)) as $file) {
            if ($file->isFile()) {
                $backupSize += $file->getSize();
            }
        }
    }
    $backupSize = round($backupSize / (1024 * 1024 * 1024), 2); // Converti in GB
    
    // Recupera data ultimo backup
    $lastBackupDate = null;
    if (is_dir($backupDir)) {
        $latest_ctime = 0;
        $latest_filename = '';    
        $d = dir($backupDir);
        while (false !== ($entry = $d->read())) {
            $filepath = "{$backupDir}/{$entry}";
            if (is_file($filepath) && filectime($filepath) > $latest_ctime) {
                $latest_ctime = filectime($filepath);
                $latest_filename = $filepath;
            }
        }
        $d->close();
        if ($latest_ctime > 0) {
            $lastBackupDate = date('Y-m-d H:i:s', $latest_ctime);
        }
    }
    
    echo json_encode([
        'total_users' => $totalUsers ?? 0,
        'active_today' => $activeToday ?? 0,
        'active_week' => $activeWeek ?? 0,
        'last_backup' => $lastBackupDate ?? date('Y-m-d H:i:s'),
        'disk_used' => $backupSize,
        'disk_total' => round(disk_total_space("/") / (1024 * 1024 * 1024), 2) // Converti in GB
    ]);
    
} catch (Exception $e) {
    error_log("Errore API statistiche: " . $e->getMessage());
    header('HTTP/1.1 500 Internal Server Error');
    echo json_encode(['error' => 'Errore nel recupero delle statistiche']);
} 