<?php
session_start();
require_once 'db_config.php';

header('Content-Type: application/json');

// Verifica se l'utente è loggato
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode([
        'success' => false,
        'message' => 'Utente non autorizzato'
    ]);
    exit();
}

try {
    // Verifica i dati ricevuti
    if (!isset($_POST['currentPassword']) || !isset($_POST['newPassword']) || !isset($_POST['confirmPassword'])) {
        throw new Exception('<PERSON><PERSON> mancanti');
    }
    
    if ($_POST['newPassword'] !== $_POST['confirmPassword']) {
        throw new Exception('Le password non coincidono');
    }
    
    if (strlen($_POST['newPassword']) < 8) {
        throw new Exception('La password deve essere di almeno 8 caratteri');
    }
    
    $pdo = getConnection();
    
    // Verifica la password corrente
    $stmt = $pdo->prepare("SELECT password FROM users WHERE id = ?");
    $stmt->execute([$_SESSION['user_id']]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$user || !password_verify($_POST['currentPassword'], $user['password'])) {
        throw new Exception('Password corrente non valida');
    }
    
    // Aggiorna la password
    $stmt = $pdo->prepare("UPDATE users SET password = ? WHERE id = ?");
    $hashedPassword = password_hash($_POST['newPassword'], PASSWORD_DEFAULT);
    
    $stmt->execute([$hashedPassword, $_SESSION['user_id']]);
    
    if ($stmt->rowCount() > 0) {
        echo json_encode([
            'success' => true,
            'message' => 'Password aggiornata con successo'
        ]);
    } else {
        throw new Exception('Errore nell\'aggiornamento della password');
    }
    
} catch (Exception $e) {
    error_log("Errore nell'aggiornamento della password: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
} 