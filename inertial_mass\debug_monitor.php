<?php
/**
 * debug_monitor.php - Monitor per debug del modulo massa inerziale
 * Path: /inertial_mass/debug_monitor.php
 */

session_start();

// Headers per evitare cache
header('Cache-Control: no-cache, must-revalidate');
header('Expires: Mon, 26 Jul 1997 05:00:00 GMT');
header('Content-Type: application/json');

// Verifica autenticazione
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['error' => 'Non autorizzato']);
    exit;
}

try {
    $status = [
        'timestamp' => date('Y-m-d H:i:s'),
        'session_id' => session_id(),
        'user_id' => $_SESSION['user_id'],
        'memory_usage' => memory_get_usage(true),
        'memory_peak' => memory_get_peak_usage(true),
        'execution_time' => microtime(true) - $_SERVER['REQUEST_TIME_FLOAT'],
        'php_version' => PHP_VERSION,
        'max_execution_time' => ini_get('max_execution_time'),
        'memory_limit' => ini_get('memory_limit'),
        'server_load' => sys_getloadavg(),
        'disk_free_space' => disk_free_space('.'),
        'config_status' => [],
        'api_status' => 'unknown'
    ];
    
    // Verifica configurazione
    $config = require_once 'includes/config.php';
    $status['config_status'] = [
        'deepseek_api_key' => !empty($config['deepseek_api_key']) ? 'configured' : 'missing',
        'api_timeout' => $config['api_timeout'],
        'max_retries' => $config['max_retries'],
        'cache_enabled' => $config['enable_cache']
    ];
    
    // Test rapido API (solo ping, non chiamata completa)
    if (!empty($config['deepseek_api_key'])) {
        $ch = curl_init($config['deepseek_api_url']);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_NOBODY, true); // Solo HEAD request
        curl_setopt($ch, CURLOPT_TIMEOUT, 5);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Authorization: Bearer ' . $config['deepseek_api_key']
        ]);
        
        $result = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        $status['api_status'] = ($httpCode === 200 || $httpCode === 405) ? 'reachable' : 'unreachable';
        $status['api_response_code'] = $httpCode;
    }
    
    // Verifica log files
    $logDir = dirname(__DIR__) . '/logs';
    $status['logs'] = [
        'ai_log_exists' => file_exists($logDir . '/ai.log'),
        'ai_log_size' => file_exists($logDir . '/ai.log') ? filesize($logDir . '/ai.log') : 0,
        'error_log_exists' => file_exists($logDir . '/error.log'),
        'error_log_size' => file_exists($logDir . '/error.log') ? filesize($logDir . '/error.log') : 0,
        'inertial_log_exists' => file_exists($logDir . '/inertial_mass.log'),
        'inertial_log_size' => file_exists($logDir . '/inertial_mass.log') ? filesize($logDir . '/inertial_mass.log') : 0
    ];
    
    echo json_encode($status, JSON_PRETTY_PRINT);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'error' => 'Errore nel monitor: ' . $e->getMessage(),
        'timestamp' => date('Y-m-d H:i:s')
    ]);
}
?>