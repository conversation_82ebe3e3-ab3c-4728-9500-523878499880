<?php
// api/calculate_seismic_params.php - API per il calcolo dei parametri sismici reali
header('Content-Type: application/json');
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Include le dipendenze necessarie
require_once dirname(__DIR__) . '/includes/db_config.php';
require_once dirname(__DIR__) . '/includes/SeismicCalculator.php';

try {
    // Leggi i dati dalla richiesta
    $input = file_get_contents('php://input');
    $data = json_decode($input, true);
    
    // Se non ci sono dati POST JSON, prova con GET
    if (empty($data)) {
        $data = $_GET;
    }
    
    // Verifica i dati richiesti
    if (!isset($data['lat']) || !isset($data['lng'])) {
        throw new Exception('Coordinate mancanti');
    }

    $lat = floatval($data['lat']);
    $lng = floatval($data['lng']);
    
    // Parametri di default se non specificati
    $nominalLife = isset($data['nominalLife']) ? intval($data['nominalLife']) :
                   (isset($data['nominal_life']) ? intval($data['nominal_life']) : 50);
    $useClass = isset($data['buildingClass']) ? $data['buildingClass'] :
                (isset($data['use_class']) ? $data['use_class'] : 'II');
    $soilCategory = isset($data['soilCategory']) ? $data['soilCategory'] :
                    (isset($data['soil_category']) ? $data['soil_category'] : 'B');
    $topographicCategory = isset($data['topographicCategory']) ? $data['topographicCategory'] :
                           (isset($data['topographic_category']) ? $data['topographic_category'] : 'T1');
    $damping = isset($data['damping']) ? floatval($data['damping']) : 5.0;
    $qFactor = isset($data['qFactor']) ? floatval($data['qFactor']) : 1.0;
    
    // Crea l'istanza del calcolatore sismico
    $calculator = new SeismicCalculator();
    
    // Log della richiesta con parametri
    $calculator->writeLog("Richiesta calcolo parametri sismici per lat=$lat, lng=$lng", "INFO");
    $calculator->writeLog("Parametri: VN=$nominalLife, Classe=$useClass, Suolo=$soilCategory, Topo=$topographicCategory, Smorzamento=$damping, q=$qFactor", "INFO");
    
    // Stati limite con le loro probabilità di superamento
    $statesPVR = [
        'SLO' => 0.81,
        'SLD' => 0.63,
        'SLV' => 0.10,
        'SLC' => 0.05
    ];
    
    // Calcola i TR personalizzati per ogni stato limite
    $states = [];
    foreach ($statesPVR as $state => $PVR) {
        $TR = $calculator->calculateTR($nominalLife, $useClass, $PVR);
        $states[$state] = ['TR' => round($TR), 'PVR' => $PVR];
        $calculator->writeLog("Stato $state: TR calcolato = $TR anni", "INFO");
    }
    
    $results = [];
    
    foreach ($states as $state => $stateData) {
        try {
            // Interpola i parametri base per il periodo di ritorno specifico
            $baseParams = $calculator->interpolateForTR($lat, $lng, $stateData['TR']);
            
            // Calcola i coefficienti di amplificazione con i parametri personalizzati
            $amplificationCoeffs = $calculator->calculateAmplificationCoefficients(
                $soilCategory,
                $topographicCategory,
                $baseParams['ag'],
                $baseParams['F0'],
                $baseParams['TC*']
            );
            
            // Calcola i periodi caratteristici
            $periods = $calculator->calculatePeriods(
                $baseParams['TC*'],
                $amplificationCoeffs['CC'],
                $baseParams['ag']
            );
            
            // Calcola il fattore di smorzamento
            $eta = $calculator->calculateDampingFactor($damping);
            
            // Prepara i risultati con tutti i parametri calcolati
            $results[$state] = [
                'TR' => $stateData['TR'],
                'ag' => round($baseParams['ag'], 3),
                'F0' => round($baseParams['F0'], 3),
                'TC' => round($baseParams['TC*'], 3),
                // Coefficienti di amplificazione
                'SS' => $amplificationCoeffs['SS'],
                'CC' => $amplificationCoeffs['CC'],
                'ST' => $amplificationCoeffs['ST'],
                'S' => $amplificationCoeffs['S'],
                // Periodi caratteristici
                'TB' => $periods['TB'],
                'TC_calc' => $periods['TC'], // TC calcolato = CC * TC*
                'TD' => $periods['TD'],
                // Fattori per lo spettro
                'eta' => round($eta, 3),
                'q' => $qFactor
            ];
            
            $calculator->writeLog("Parametri completi calcolati per $state: " . json_encode($results[$state]), "INFO");
            
        } catch (Exception $e) {
            $calculator->writeLog("Errore nel calcolo per $state: " . $e->getMessage(), "ERROR");
            throw $e;
        }
    }
    
    $response = [
        'success' => true,
        'data' => $results,
        'coordinates' => [
            'lat' => $lat,
            'lng' => $lng
        ],
        'parameters' => [
            'nominal_life' => $nominalLife,
            'use_class' => $useClass,
            'soil_category' => $soilCategory,
            'topographic_category' => $topographicCategory,
            'damping' => $damping,
            'q_factor' => $qFactor
        ],
        'calculation_info' => [
            'method' => 'NTC 2018 con parametri personalizzati',
            'tr_calculated' => true,
            'amplification_applied' => true,
            'timestamp' => date('Y-m-d H:i:s')
        ]
    ];
    
    $calculator->writeLog("Calcolo completato con successo", "INFO");
    echo json_encode($response);

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
