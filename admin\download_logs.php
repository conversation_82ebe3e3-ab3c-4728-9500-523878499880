<?php
require_once '../includes/db_config.php';
require_once '../includes/Logger.php';
session_start();

// Verifica autenticazione
if (!isset($_SESSION['user_id']) || !isset($_SESSION['role']) || $_SESSION['role'] !== 'admin') {
    header('HTTP/1.1 403 Forbidden');
    exit;
}

try {
    // Crea una directory temporanea
    $tempDir = sys_get_temp_dir() . '/logs_' . uniqid();
    mkdir($tempDir);
    
    // Copia i file di log
    $logFiles = [
        'app.log',
        'seismic_calc.log',
        'error.log',
        'cadastral.log',
        'catasto_proxy.log'
    ];
    
    foreach ($logFiles as $logFile) {
        $sourcePath = __DIR__ . '/../logs/' . $logFile;
        if (file_exists($sourcePath)) {
            copy($sourcePath, $tempDir . '/' . $logFile);
        }
    }
    
    // Esporta i log dal database
    $conn = getConnection();
    $stmt = $conn->query("SELECT al.*, u.username 
                         FROM access_logs al 
                         LEFT JOIN users u ON al.user_id = u.id 
                         ORDER BY al.timestamp DESC");
    $dbLogs = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Crea il file CSV per i log del database
    $csvFile = $tempDir . '/database_logs.csv';
    $fp = fopen($csvFile, 'w');
    
    // Intestazioni CSV
    fputcsv($fp, ['Data/Ora', 'Utente', 'Azione', 'IP', 'User Agent']);
    
    // Dati
    foreach ($dbLogs as $log) {
        fputcsv($fp, [
            $log['timestamp'],
            $log['username'] ?? 'N/A',
            $log['action'],
            $log['ip_address'],
            $log['user_agent']
        ]);
    }
    
    fclose($fp);
    
    // Crea il file ZIP
    $zipFile = sys_get_temp_dir() . '/system_logs.zip';
    $zip = new ZipArchive();
    
    if ($zip->open($zipFile, ZipArchive::CREATE | ZipArchive::OVERWRITE) === TRUE) {
        $files = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($tempDir),
            RecursiveIteratorIterator::LEAVES_ONLY
        );
        
        foreach ($files as $file) {
            if (!$file->isDir()) {
                $filePath = $file->getRealPath();
                $relativePath = basename($filePath);
                $zip->addFile($filePath, $relativePath);
            }
        }
        
        $zip->close();
        
        // Invia il file ZIP
        header('Content-Type: application/zip');
        header('Content-Disposition: attachment; filename="system_logs.zip"');
        header('Content-Length: ' . filesize($zipFile));
        header('Pragma: no-cache');
        
        readfile($zipFile);
        
        // Pulisci i file temporanei
        array_map('unlink', glob("$tempDir/*.*"));
        rmdir($tempDir);
        unlink($zipFile);
    } else {
        throw new Exception('Impossibile creare il file ZIP');
    }
    
} catch (Exception $e) {
    error_log("Errore nel download dei log: " . $e->getMessage());
    header('HTTP/1.1 500 Internal Server Error');
    echo "Errore durante il download dei log";
} 