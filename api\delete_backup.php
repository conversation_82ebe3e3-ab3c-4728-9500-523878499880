<?php
require_once '../includes/db_config.php';

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Metodo non consentito']);
    exit;
}

// Funzione per eliminare ricorsivamente una directory
function removeDirectory($dir) {
    if (!is_dir($dir)) {
        return false;
    }
    
    $files = array_diff(scandir($dir), array('.', '..'));
    
    foreach ($files as $file) {
        $path = $dir . DIRECTORY_SEPARATOR . $file;
        if (is_dir($path)) {
            removeDirectory($path);
        } else {
            unlink($path);
        }
    }
    
    return rmdir($dir);
}

session_start();

// Verifica autenticazione
if (!isset($_SESSION['user_id']) || !isset($_SESSION['role']) || $_SESSION['role'] !== 'admin') {
    http_response_code(403);
    die(json_encode(['error' => 'Accesso non autorizzato']));
}

// Ottieni i dati dalla richiesta
$data = json_decode(file_get_contents('php://input'), true);
if (!isset($data['filename'])) {
    http_response_code(400);
    die(json_encode(['error' => 'Parametro filename mancante']));
}

// Sanitizza il nome del file
$filename = basename($data['filename']);
$backup_dir = __DIR__ . '/../backups/';
$file_path = $backup_dir . $filename;

try {
    $conn = getConnection();
    error_log("DEBUG: Tentativo eliminazione file: $filename");
    error_log("DEBUG: Percorso file: $file_path");
    
    // Verifica che il file esista nel database
    $stmt = $conn->prepare("SELECT * FROM backups WHERE filename = ?");
    $stmt->execute([$filename]);
    if (!$stmt->fetch()) {
        error_log("DEBUG: Backup non trovato nel database");
        throw new Exception('Backup non trovato nel database');
    }
    error_log("DEBUG: Backup trovato nel database");
    
    // Verifica esistenza e tipo del backup (file o directory)
    if (file_exists($file_path)) {
        error_log("DEBUG: Backup esiste: $file_path");
        
        if (is_dir($file_path)) {
            error_log("DEBUG: Il backup è una directory");
            
            // Verifica permessi directory
            if (!is_writable($file_path)) {
                error_log("DEBUG: Directory non scrivibile");
                throw new Exception('Directory non scrivibile - permessi insufficienti');
            }
            
            // Elimina ricorsivamente la directory
            if (!removeDirectory($file_path)) {
                error_log("DEBUG: Eliminazione directory fallita");
                throw new Exception('Impossibile eliminare la directory di backup');
            }
            
            error_log("DEBUG: Directory eliminata con successo");
            
        } else {
            error_log("DEBUG: Il backup è un file");
            
            // Verifica permessi file
            if (!is_writable($file_path)) {
                error_log("DEBUG: File non scrivibile");
                throw new Exception('File non scrivibile - permessi insufficienti');
            }
            
            // Elimina il file
            if (!unlink($file_path)) {
                $error = error_get_last();
                $errorMsg = $error ? $error['message'] : 'Errore sconosciuto';
                error_log("DEBUG: unlink() fallito - $errorMsg");
                throw new Exception("Impossibile eliminare il file: $errorMsg");
            }
            
            error_log("DEBUG: File eliminato con successo");
        }
    } else {
        error_log("DEBUG: Backup non esiste fisicamente, procedo con eliminazione dal database");
    }
    
    // Elimina il record dal database
    $stmt = $conn->prepare("DELETE FROM backups WHERE filename = ?");
    $stmt->execute([$filename]);
    error_log("DEBUG: Record eliminato dal database");
    
    echo json_encode([
        'success' => true,
        'message' => 'Backup eliminato con successo'
    ]);
} catch (Exception $e) {
    error_log("Errore eliminazione backup: " . $e->getMessage());
    error_log("DEBUG: Stack trace: " . $e->getTraceAsString());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}