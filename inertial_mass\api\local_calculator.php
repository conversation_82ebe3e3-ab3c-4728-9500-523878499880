<?php
/**
 * local_calculator.php - Calcolatore locale per massa inerziale
 * Alternativa al servizio LLM esterno per calcoli offline
 */

// Disabilita la visualizzazione degli errori per evitare output HTML
ini_set('display_errors', 0);
error_reporting(E_ALL);

// Assicura che l'output sia sempre JSON
header('Content-Type: application/json');

session_start();

// Verifica autenticazione
$isTestEnvironment = strpos($_SERVER['SCRIPT_NAME'], 'test_integration.php') !== false;

if (!isset($_SESSION['user_id']) && !$isTestEnvironment) {
    http_response_code(401);
    echo json_encode(['error' => 'Non autorizzato']);
    exit;
}

// Verifica metodo
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Metodo non consentito']);
    exit;
}

// Ottieni dati dalla richiesta
$input = json_decode(file_get_contents('php://input'), true);

if (!$input) {
    http_response_code(400);
    echo json_encode(['error' => 'Dati non validi o JSON malformato']);
    exit;
}

// Verifica che i dati essenziali siano presenti
if (!isset($input['seismic_params']) || !isset($input['building'])) {
    http_response_code(400);
    echo json_encode(['error' => 'Dati sismici o strutturali mancanti']);
    exit;
}

try {
    // Log inizio elaborazione
    error_log("=== CALCOLO LOCALE MASSA INERZIALE ===");
    error_log("Timestamp: " . date('Y-m-d H:i:s'));
    
    // Calcola la massa inerziale usando formule standard
    $result = calculateInertialMass($input);
    
    // Log successo
    error_log("Calcolo locale completato con successo");
    
    // Restituisci il risultato
    header('Content-Type: application/json');
    echo json_encode($result);
    
} catch (Exception $e) {
    error_log("Errore calcolo locale: " . $e->getMessage());
    
    http_response_code(500);
    echo json_encode([
        'error' => 'Errore durante il calcolo: ' . $e->getMessage(),
        'timestamp' => date('Y-m-d H:i:s')
    ]);
}

/**
 * Calcola la massa inerziale usando formule standard NTC 2018
 */
function calculateInertialMass($data) {
    // Validazione dati di input
    if (!isset($data['seismic_params']['ag']) || !isset($data['seismic_params']['F0']) || !isset($data['seismic_params']['TC'])) {
        throw new Exception('Parametri sismici mancanti (ag, F0, TC)');
    }
    
    if (!isset($data['building']['structure_type']) || !isset($data['building']['slab_type']) || !isset($data['building']['construction_year'])) {
        throw new Exception('Parametri strutturali mancanti');
    }
    
    if (!isset($data['building']['floors']) || !is_array($data['building']['floors']) || empty($data['building']['floors'])) {
        throw new Exception('Dati dei piani mancanti o non validi');
    }
    
    // Estrai parametri sismici
    $ag = floatval($data['seismic_params']['ag']);
    $F0 = floatval($data['seismic_params']['F0']);
    $TC = floatval($data['seismic_params']['TC']);
    
    // Validazione valori sismici
    if ($ag <= 0 || $F0 <= 0 || $TC <= 0) {
        throw new Exception('Parametri sismici non validi (devono essere > 0)');
    }
    
    // Parametri strutturali
    $constructionCategory = $data['building']['construction_category'] ?? 'building';
    $structureType = $data['building']['structure_type'];
    $slabType = $data['building']['slab_type'];
    $constructionYear = intval($data['building']['construction_year']);

    // Validazione parametri strutturali
    if (empty($constructionCategory) || empty($structureType) || empty($slabType) || $constructionYear < 1800 || $constructionYear > date('Y')) {
        throw new Exception('Parametri strutturali non validi');
    }

    // Calcola pesi per tipologia strutturale
    $weights = calculateStructuralWeights($structureType, $slabType, $constructionYear, $constructionCategory);
    
    // Calcola massa e forze per ogni piano
    $floorForces = [];
    $totalMass = 0;
    $totalHeight = 0;
    $weightedHeight = 0;
    
    foreach ($data['building']['floors'] as $index => $floor) {
        // Validazione dati piano
        if (!isset($floor['area']) || !isset($floor['height']) || !isset($floor['use'])) {
            throw new Exception("Piano " . ($index + 1) . ": dati mancanti (area, altezza, uso)");
        }
        
        $area = floatval($floor['area']);
        $height = floatval($floor['height']);
        $use = $floor['use'];
        
        // Validazione valori
        if ($area <= 0) {
            throw new Exception("Piano " . ($index + 1) . ": area deve essere > 0");
        }
        if ($height <= 0) {
            throw new Exception("Piano " . ($index + 1) . ": altezza deve essere > 0");
        }
        if (empty($use)) {
            throw new Exception("Piano " . ($index + 1) . ": destinazione d'uso mancante");
        }
        
        // Peso per unità di superficie
        $unitWeight = $weights['structural'] + $weights['permanent'] + getVariableLoad($use, $constructionCategory);
        
        // Massa del piano (kN -> tonnellate)
        $floorMass = ($area * $unitWeight) / 9.81;
        
        // Altezza cumulativa
        $totalHeight += $height;
        
        $floorForces[] = [
            'level' => intval($floor['level'] ?? ($index + 1)),
            'area' => $area,
            'height' => $totalHeight,
            'unit_weight' => round($unitWeight, 2),
            'mass' => round($floorMass, 2),
            'force' => 0 // Calcolato dopo
        ];
        
        $totalMass += $floorMass;
        $weightedHeight += $floorMass * $totalHeight;
    }
    
    // Calcola periodo fondamentale T1
    $period = calculateFundamentalPeriod($structureType, $totalHeight, count($data['building']['floors']), $constructionCategory);
    
    // Recupera il fattore di smorzamento
    $damping = isset($data['seismic_params']['damping']) ? $data['seismic_params']['damping'] : 5.0;

    // Calcola spettro di risposta
    $Se = calculateResponseSpectrum($period, $ag, $F0, $TC, $damping);
    
    // Forza sismica totale
    $totalForce = $totalMass * $Se * 9.81; // kN
    
    // Distribuzione forze per piano (metodo semplificato)
    $sumMassHeight = 0;
    foreach ($floorForces as &$floor) {
        $sumMassHeight += $floor['mass'] * $floor['height'];
    }
    
    foreach ($floorForces as &$floor) {
        $floor['force'] = round(($floor['mass'] * $floor['height'] / $sumMassHeight) * $totalForce, 2);
    }
    
    // Analisi vulnerabilità
    $analysis = generateVulnerabilityAnalysis($structureType, $constructionYear, $ag, $period);
    
    return [
        'calculation_id' => uniqid('local_calc_'),
        'timestamp' => date('Y-m-d H:i:s'),
        'method' => 'Calcolo locale NTC 2018',
        'input_data' => $data,
        'total_mass' => round($totalMass, 2),
        'period' => round($period, 3),
        'total_force' => round($totalForce, 2),
        'response_spectrum' => round($Se, 3),
        'floor_forces' => $floorForces,
        'structural_weights' => $weights,
        'analysis' => $analysis
    ];
}

/**
 * Calcola i pesi strutturali in base alla tipologia
 */
function calculateStructuralWeights($structureType, $slabType, $year, $constructionCategory = 'building') {
    $weights = [
        'structural' => 0,
        'permanent' => 0,
        'variable' => 0
    ];

    // Peso strutturale base
    switch (strtolower($structureType)) {
        case 'prestressed_concrete':
            if ($constructionCategory === 'bridge') {
                $weights['structural'] = 6.0; // kN/m² per ponti precompressi
            } else { // prefab_building
                $weights['structural'] = 5.0; // kN/m² per prefabbricati precompressi
            }
            break;
        case 'concrete':
        case 'cemento armato':
        case 'c.a.':
            $weights['structural'] = 4.0; // kN/m²
            break;
        case 'steel':
        case 'acciaio':
            $weights['structural'] = 2.5;
            break;
        case 'wood':
        case 'legno':
            $weights['structural'] = 1.5;
            break;
        case 'masonry':
        case 'muratura':
            $weights['structural'] = 6.0;
            break;
        case 'mixed':
        case 'mista':
            $weights['structural'] = 4.5;
            break;
        default:
            $weights['structural'] = 3.5;
    }
    
    // Peso solaio/impalcato
    if ($constructionCategory === 'bridge') {
        // Gestione impalcati per ponti
        switch (strtolower($slabType)) {
            case 'prestressed_deck':
                $weights['structural'] += 8.0; // Impalcato precompresso
                break;
            case 'composite_deck':
                $weights['structural'] += 6.5; // Impalcato misto
                break;
            case 'concrete_deck':
                $weights['structural'] += 7.0; // Impalcato in C.A.
                break;
            default:
                $weights['structural'] += 7.5; // Default per ponti
        }
    } else {
        // Gestione solai per edifici
        switch (strtolower($slabType)) {
            case 'prestressed_slab':
                $weights['structural'] += 4.5; // Solaio precompresso
                break;
            case 'prefab_panels':
                $weights['structural'] += 3.8; // Pannelli prefabbricati
                break;
            case 'hollow_core':
                $weights['structural'] += 3.2; // Lastre alveolari
                break;
            case 'hollow_brick':
            case 'laterocemento':
                $weights['structural'] += 3.5;
                break;
            case 'predalles':
                $weights['structural'] += 4.0;
                break;
            case 'solid_slab':
            case 'soletta piena':
                $weights['structural'] += 5.0;
                break;
            case 'steel_deck':
            case 'lamiera grecata':
                $weights['structural'] += 2.5;
                break;
            case 'wood_slab':
            case 'legno':
                $weights['structural'] += 1.0;
                break;
            case 'prefab':
            case 'prefabbricato':
                $weights['structural'] += 3.5;
                break;
            default:
                $weights['structural'] += 3.0;
        }
    }
    
    // Carichi permanenti
    if ($constructionCategory === 'bridge') {
        // Per ponti: barriere, guard-rail, pavimentazione, servizi
        $weights['permanent'] = 3.0; // kN/m²
    } else {
        // Per edifici: tramezzi, impianti, finiture
        $weights['permanent'] = 2.0; // kN/m²
    }
    
    // Fattore età edificio
    if ($year < 1980) {
        $weights['structural'] *= 1.2; // Edifici più pesanti
    }
    
    return $weights;
}

/**
 * Restituisce il carico variabile per destinazione d'uso
 */
function getVariableLoad($use, $constructionCategory = 'building') {
    if ($constructionCategory === 'bridge') {
        // Carichi da traffico per ponti (secondo NTC 2018)
        $bridgeLoads = [
            'highway' => 9.0,        // kN/m² (carico da traffico autostradale)
            'urban_road' => 5.0,     // kN/m² (carico da traffico urbano)
            'railway' => 80.0,       // kN/m² (carico da treno)
            'pedestrian' => 5.0,     // kN/m² (carico pedonale)
            'mixed_traffic' => 7.0   // kN/m² (traffico misto)
        ];

        $useKey = strtolower(trim($use));
        return $bridgeLoads[$useKey] ?? 5.0; // Default pedonale
    }

    // Carichi per edifici
    $loads = [
        // Italiano
        'residenziale' => 2.0,
        'uffici' => 3.0,
        'commerciale' => 4.0,
        'industriale' => 5.0,
        'scuole' => 3.0,
        'ospedali' => 4.0,
        'autorimesse' => 2.5,
        'magazzino' => 5.0,
        // Inglese (dal JavaScript)
        'residential' => 2.0,
        'office' => 3.0,
        'commercial' => 4.0,
        'industrial' => 5.0,
        'storage' => 5.0,
        'school' => 3.0,
        'hospital' => 4.0,
        'garage' => 2.5
    ];

    $useKey = strtolower(trim($use));
    return $loads[$useKey] ?? 2.0; // Default residenziale
}

/**
 * Calcola il periodo fondamentale T1
 */
function calculateFundamentalPeriod($structureType, $height, $floors, $constructionCategory = 'building') {
    if ($constructionCategory === 'bridge') {
        // Formula specifica per ponti (dipende dalla luce e tipologia)
        // T = 2π√(L³/(48EI/m)) semplificata per ponti
        switch (strtolower($structureType)) {
            case 'prestressed_concrete':
                return 0.02 * pow($height, 1.2); // Formula approssimata per ponti precompressi
            default:
                return 0.025 * pow($height, 1.1); // Formula generica per ponti
        }
    }

    // Formula semplificata NTC 2018 per edifici
    switch (strtolower($structureType)) {
        case 'prestressed_concrete':
            return 0.070 * pow($height, 0.75); // Leggermente più rigido del c.a. tradizionale
        case 'concrete':
        case 'cemento armato':
        case 'c.a.':
            return 0.075 * pow($height, 0.75);
        case 'steel':
        case 'acciaio':
            return 0.085 * pow($height, 0.75);
        case 'masonry':
        case 'muratura':
            return 0.05 * pow($height, 0.75);
        case 'wood':
        case 'legno':
            return 0.09 * pow($height, 0.75);
        case 'mixed':
        case 'mista':
            return 0.08 * pow($height, 0.75);
        default:
            return 0.1 * $floors; // Formula approssimata
    }
}

/**
 * Calcola l'accelerazione spettrale
 */
function calculateResponseSpectrum($T, $ag, $F0, $TC, $damping = 5.0) {
    // Parametri spettro semplificato
    $S = 1.2; // Fattore amplificazione medio

    // Calcola il fattore di smorzamento eta secondo NTC 2018
    // eta = sqrt(10/(5+xi)) >= 0.55, dove xi è lo smorzamento in %
    $eta = max(sqrt(10 / (5 + $damping)), 0.55);

    $TB = $TC / 3;
    $TD = 4.0 * $ag + 1.6;

    if ($T <= $TB) {
        return $ag * $S * $eta * $F0 * ($T/$TB + (1/($eta*$F0)) * (1 - $T/$TB));
    } elseif ($T <= $TC) {
        return $ag * $S * $eta * $F0;
    } elseif ($T <= $TD) {
        return $ag * $S * $eta * $F0 * ($TC/$T);
    } else {
        return $ag * $S * $eta * $F0 * ($TC*$TD/($T*$T));
    }
}

/**
 * Genera analisi di vulnerabilità
 */
function generateVulnerabilityAnalysis($structureType, $year, $ag, $period) {
    $analysis = "ANALISI VULNERABILITÀ SISMICA:\n\n";
    
    // Valutazione per tipologia strutturale
    switch (strtolower($structureType)) {
        case 'cemento armato':
        case 'c.a.':
            $analysis .= "• Struttura in cemento armato: buona resistenza sismica se progettata secondo normative moderne.\n";
            break;
        case 'muratura':
            $analysis .= "• Struttura in muratura: vulnerabilità elevata, necessari interventi di miglioramento.\n";
            break;
        case 'acciaio':
            $analysis .= "• Struttura in acciaio: ottima duttilità e resistenza sismica.\n";
            break;
    }
    
    // Valutazione per epoca di costruzione
    if ($year < 1974) {
        $analysis .= "• Edificio pre-normativa sismica: alta vulnerabilità, necessaria verifica strutturale.\n";
    } elseif ($year < 2008) {
        $analysis .= "• Edificio con normativa sismica precedente: vulnerabilità media, possibili adeguamenti.\n";
    } else {
        $analysis .= "• Edificio con normativa moderna: buona resistenza sismica.\n";
    }
    
    // Valutazione accelerazione
    if ($ag > 0.25) {
        $analysis .= "• Zona ad alta sismicità: necessari controlli approfonditi.\n";
    } elseif ($ag > 0.15) {
        $analysis .= "• Zona a media sismicità: verifiche periodiche consigliate.\n";
    } else {
        $analysis .= "• Zona a bassa sismicità: rischio contenuto.\n";
    }
    
    // Valutazione periodo
    if ($period > 1.0) {
        $analysis .= "• Periodo elevato: possibili problemi di instabilità, verificare rigidezza.\n";
    }
    
    return $analysis;
}
?>