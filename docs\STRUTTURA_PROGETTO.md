# Struttura del Progetto ASDP

## Panoramica
Advanced Seismic Dissipator Project - Sistema per il calcolo di parametri sismici e gestione di dissipatori.

## Struttura Directory

### 📁 Root Files
- `index.php` - Pagina principale dell'applicazione
- `login.php` - Sistema di autenticazione
- `home.php` - Dashboard utente
- `account.php` - Gestione profilo utente
- `settings.php` - Impostazioni utente
- `help.php` - Documentazione e aiuto
- `privacy.php` - Informativa privacy
- `router.php` - Router per gestione URL
- `composer.json` - Dipendenze PHP

### 📁 admin/
Pannello di amministrazione del sistema
- `dashboard.php` - Dashboard amministratore
- `backup.php` - Gestione backup sistema
- `users.php` - Gestione utenti
- `settings.php` - Impostazioni sistema
- `logs.php` - Visualizzazione log
- `create_admin.php` - Creazione utente amministratore

### 📁 api/
Endpoint API REST per funzionalità del sistema
- `backup_process_zip.php` - Processo di backup
- `delete_backup.php` - Eliminazione backup
- `get_backups.php` - Lista backup
- `calculate_seismic_params.php` - Calcoli sismici
- `seismic_data.php` - Dati sismici
- `cadastral_data.php` - Dati catastali

### 📁 auth/
Sistema di autenticazione
- `login_process.php` - Processo di login
- `logout.php` - Processo di logout
- `handlers/` - Handler per autenticazione

### 📁 includes/
File di supporto e configurazione
- `db_config.php` - Configurazione database
- `config.php` - Configurazioni generali
- `functions.php` - Funzioni utility
- `logger.php` - Sistema di logging
- `SeismicCalculator.php` - Calcolatore sismico
- `services/` - Servizi (Cache, Logger, WMS)
- `components/` - Componenti riutilizzabili

### 📁 inertial_mass/
Modulo per calcolo massa inerziale
- `modal.php` - Interfaccia modale
- `api/` - API specifiche per massa inerziale
- `assets/` - CSS e JS del modulo
- `includes/` - Configurazioni del modulo

### 📁 css/
Fogli di stile
- `style.css` - Stile principale
- `admin.css` - Stili pannello admin
- `backup.css` - Stili sistema backup
- `variables.css` - Variabili CSS

### 📁 js/
JavaScript dell'applicazione
- `ui.js` - Interfaccia utente
- `admin.js` - Funzionalità admin
- `SeismicCalculator.js` - Calcolatore lato client
- `map.js` - Gestione mappe
- `min/` - File minificati

### 📁 docs/
Documentazione del progetto
- `00_indice.md` - Indice documentazione
- `01_panoramica.md` - Panoramica sistema
- `04_database.md` - Schema database
- `05_api.md` - Documentazione API
- `10_metodo_calcolo.md` - Metodi di calcolo

### 📁 sql/
Script SQL per database
- `create_tables.sql` - Creazione tabelle
- `create_user_tables.sql` - Tabelle utenti
- `create_calculation_tables.sql` - Tabelle calcoli

### 📁 tools/
Strumenti di utilità
- `minify_js.php` - Minificazione JavaScript
- `check_permissions.php` - Verifica permessi
- `refresh_version.php` - Aggiornamento versione

### 📁 backups/
Directory per backup del sistema (vuota di default)

### 📁 logs/
File di log del sistema (vuoti di default)

### 📁 cache/
Cache del sistema
- `version.txt` - Cache versione

## Tecnologie Utilizzate
- **Backend**: PHP 8.0+
- **Database**: MySQL/MariaDB
- **Frontend**: HTML5, CSS3, JavaScript ES6+
- **Mappe**: Leaflet.js
- **Dipendenze**: Composer

## Funzionalità Principali
1. **Calcolo Parametri Sismici**: Calcolo di parametri per dissipatori sismici
2. **Gestione Utenti**: Sistema completo di autenticazione e autorizzazione
3. **Pannello Admin**: Gestione sistema, backup, utenti
4. **Sistema Backup**: Backup automatico database e file
5. **Massa Inerziale**: Modulo specializzato per calcoli massa inerziale
6. **Integrazione Catastale**: Connessione con servizi WMS catastali

## Note di Sicurezza
- Autenticazione basata su sessioni PHP
- Validazione input lato server
- Protezione CSRF
- Logging completo delle attività
- Backup automatici per disaster recovery

## Manutenzione
- Log automatici in `logs/`
- Backup programmabili via pannello admin
- Monitoraggio versioni tramite `VersionManager`
- Cache automatica per performance