<?php
require_once '../includes/db_config.php';

try {
    // Verifica se la colonna role esiste
    $stmt = $conn->query("SHOW COLUMNS FROM users LIKE 'role'");
    $roleExists = $stmt->fetch();

    if (!$roleExists) {
        // Aggiungi la colonna role se non esiste
        $conn->exec("ALTER TABLE users ADD COLUMN role ENUM('user', 'admin') NOT NULL DEFAULT 'user'");
        echo "Colonna 'role' aggiunta con successo.<br>";
    }

    // Verifica se esiste l'utente admin
    $stmt = $conn->prepare("SELECT id, role FROM users WHERE username = ?");
    $stmt->execute(['admin']);
    $admin = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$admin) {
        // Crea l'utente admin se non esiste
        $hashedPassword = password_hash('admin123', PASSWORD_DEFAULT);
        $stmt = $conn->prepare("INSERT INTO users (username, password, email, nome, cognome, role) VALUES (?, ?, ?, ?, ?, ?)");
        $stmt->execute(['admin', $hashedPassword, '<EMAIL>', 'Admin', 'ASDP', 'admin']);
        echo "Utente admin creato con successo.<br>";
        echo "Username: admin<br>";
        echo "Password: admin123<br>";
    } else if ($admin['role'] !== 'admin') {
        // Aggiorna il ruolo se l'utente esiste ma non è admin
        $stmt = $conn->prepare("UPDATE users SET role = 'admin' WHERE username = ?");
        $stmt->execute(['admin']);
        echo "Ruolo admin aggiornato con successo.<br>";
    } else {
        echo "L'utente admin esiste già ed ha il ruolo corretto.<br>";
    }

    // Mostra tutti gli utenti e i loro ruoli
    echo "<h2>Lista Utenti:</h2>";
    $stmt = $conn->query("SELECT username, role FROM users");
    while ($user = $stmt->fetch(PDO::FETCH_ASSOC)) {
        echo "Username: " . htmlspecialchars($user['username']) . " - Ruolo: " . htmlspecialchars($user['role']) . "<br>";
    }

} catch (PDOException $e) {
    echo "Errore: " . $e->getMessage();
}
?>
