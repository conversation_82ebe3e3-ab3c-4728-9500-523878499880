<?php
session_start();
require_once 'db_config.php';

header('Content-Type: application/json');

// Verifica se l'utente è loggato
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode([
        'success' => false,
        'message' => 'Utente non autorizzato'
    ]);
    exit();
}

try {
    // Verifica i dati ricevuti
    $data = json_decode(file_get_contents('php://input'), true);
    if (!$data) {
        throw new Exception('Dati non validi');
    }
    
    $pdo = getConnection();
    
    // Aggiorna le impostazioni dell'utente
    $stmt = $pdo->prepare("
        UPDATE user_settings 
        SET 
            map_type = ?,
            language = ?,
            notifications_enabled = ?,
            auto_save = ?,
            theme = ?
        WHERE user_id = ?
    ");
    
    $stmt->execute([
        $data['map_type'] ?? 'satellite',
        $data['language'] ?? 'it',
        $data['notifications_enabled'] ?? true,
        $data['auto_save'] ?? true,
        $data['theme'] ?? 'dark',
        $_SESSION['user_id']
    ]);
    
    if ($stmt->rowCount() === 0) {
        // Se non esiste un record, lo creiamo
        $stmt = $pdo->prepare("
            INSERT INTO user_settings 
            (user_id, map_type, language, notifications_enabled, auto_save, theme)
            VALUES (?, ?, ?, ?, ?, ?)
        ");
        
        $stmt->execute([
            $_SESSION['user_id'],
            $data['map_type'] ?? 'satellite',
            $data['language'] ?? 'it',
            $data['notifications_enabled'] ?? true,
            $data['auto_save'] ?? true,
            $data['theme'] ?? 'dark'
        ]);
    }
    
    echo json_encode([
        'success' => true,
        'message' => 'Impostazioni aggiornate con successo'
    ]);
    
} catch (Exception $e) {
    error_log("Errore nell'aggiornamento delle impostazioni: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Errore nell\'aggiornamento delle impostazioni'
    ]);
} 