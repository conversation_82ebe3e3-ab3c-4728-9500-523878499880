<?php
// Abilita il log degli errori
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// File di log per il debug
$logFile = __DIR__ . '/../logs/catasto_proxy.log';

function writeLog($message) {
    global $logFile;
    $timestamp = date('[Y-m-d H:i:s]');
    try {
        if (!file_put_contents($logFile, "$timestamp $message\n", FILE_APPEND)) {
            error_log("Errore nella scrittura del log: $logFile");
        }
    } catch (Exception $e) {
        error_log("Eccezione nella scrittura del log: " . $e->getMessage());
    }
}

// Verifica che sia presente l'URL del WMS
if (!isset($_GET['wmsurl'])) {
    $error = [
        'status' => 'service_unavailable',
        'error' => 'URL WMS mancante'
    ];
    header('Content-Type: application/json');
    echo json_encode($error);
    writeLog("Errore: " . json_encode($error));
    exit;
}

// Estrai l'URL del WMS e rimuovilo dai parametri
$wmsUrl = $_GET['wmsurl'];
unset($_GET['wmsurl']);

// Raccogli i parametri dalla richiesta
$params = $_GET;

// Log dei parametri originali
writeLog("Parametri richiesta: " . json_encode($params));

// Se è una richiesta GetCapabilities, verifica solo la disponibilità del servizio
if (isset($params['request']) && $params['request'] === 'GetCapabilities') {
    // Inizializza cURL
    $ch = curl_init();
    curl_setopt_array($ch, [
        CURLOPT_URL => $wmsUrl,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_HEADER => true,
        CURLOPT_TIMEOUT => 10,
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_SSL_VERIFYHOST => 0,
        CURLOPT_USERAGENT => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    ]);

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    // Se il servizio risponde con successo
    if ($httpCode === 200) {
        $response = [
            'status' => 'ok',
            'message' => 'Servizio disponibile'
        ];
    } else {
        $response = [
            'status' => 'service_unavailable',
            'error' => 'Servizio Catastale Agenzia delle Entrate momentaneamente non disponibile'
        ];
    }

    header('Content-Type: application/json');
    echo json_encode($response);
    exit;
}

// Se è una richiesta GetFeatureInfo o GetMap, gestiamo le coordinate
if (isset($params['request']) && isset($params['bbox']) &&
    ($params['request'] === 'GetFeatureInfo' || $params['request'] === 'GetMap')) {
    
    $bbox = explode(',', $params['bbox']);
    if (count($bbox) === 4) {
        // Converti le coordinate in numeri
        $minLat = floatval($bbox[0]);
        $minLon = floatval($bbox[1]);
        $maxLat = floatval($bbox[2]);
        $maxLon = floatval($bbox[3]);

        // Per WMS 1.3.0 con CRS:EPSG:4326, l'ordine è lat,lon
        $params['bbox'] = sprintf("%.6f,%.6f,%.6f,%.6f", $minLat, $minLon, $maxLat, $maxLon);
        writeLog("BBOX modificato: " . $params['bbox']);
    }
}

// Imposta i parametri obbligatori per WMS 1.3.0
$params['service'] = 'WMS';
$params['version'] = '1.3.0';
if (!isset($params['styles'])) {
    $params['styles'] = '';
}

// Gestione specifica per GetMap
if (isset($params['request']) && $params['request'] === 'GetMap') {
    if (!isset($params['format'])) {
        $params['format'] = 'image/png';
    }
    if (!isset($params['transparent'])) {
        $params['transparent'] = 'true';
    }
}

// Imposta sempre il CRS a EPSG:4326 per il WMS
$params['crs'] = 'EPSG:4326';

// Costruisci l'URL della richiesta
$queryString = http_build_query($params);
$requestUrl = $wmsUrl . '?' . $queryString;

writeLog("URL richiesta: $requestUrl");

// Inizializza cURL con opzioni migliorate
$ch = curl_init();
curl_setopt_array($ch, [
    CURLOPT_URL => $requestUrl,
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_HEADER => true,
    CURLOPT_FOLLOWLOCATION => true,
    CURLOPT_ENCODING => "",
    CURLOPT_MAXREDIRS => 10,
    CURLOPT_TIMEOUT => 30,
    CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
    CURLOPT_SSL_VERIFYPEER => false,
    CURLOPT_SSL_VERIFYHOST => 0,
    CURLOPT_USERAGENT => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
]);

// Esegui la richiesta
$response = curl_exec($ch);

if (curl_errno($ch)) {
    $error = [
        'status' => 'service_unavailable',
        'error' => 'Servizio Catastale Agenzia delle Entrate momentaneamente non disponibile'
    ];
    writeLog("Errore CURL: " . curl_error($ch));
    header('Content-Type: application/json');
    echo json_encode($error);
    curl_close($ch);
    exit;
}

// Separa header e body
$headerSize = curl_getinfo($ch, CURLINFO_HEADER_SIZE);
$header = substr($response, 0, $headerSize);
$body = substr($response, $headerSize);

$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$contentType = curl_getinfo($ch, CURLINFO_CONTENT_TYPE);

curl_close($ch);

writeLog("Codice HTTP: $httpCode");
writeLog("Content-Type: $contentType");

// Se la risposta non è 200 OK o contiene un errore XML
if ($httpCode !== 200 || strpos($body, '<ServiceExceptionReport') !== false || strpos($body, '<ExceptionReport') !== false) {
    $error = [
        'status' => 'service_unavailable',
        'error' => 'Servizio Catastale Agenzia delle Entrate momentaneamente non disponibile'
    ];
    writeLog("Errore servizio catastale: " . json_encode($error));
    header('Content-Type: application/json');
    echo json_encode($error);
    exit;
}

// Per le altre richieste, invia la risposta così com'è
header("Content-Type: $contentType");
echo $body;
?> 