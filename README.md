# Documentazione ASDP - Advanced Seismic Dissipator Project

## 📋 Panoramica

Questa cartella contiene tutta la documentazione tecnica e operativa del progetto ASDP (Advanced Seismic Dissipator Project), un sistema avanzato per il calcolo e l'analisi di parametri sismici secondo le normative NTC 2018.

## 🗂️ Struttura Documentazione

### 📚 Documentazione Base
- **[00_indice.md](docs/00_indice.md)** - Indice generale di tutta la documentazione
- **[STRUTTURA_PROGETTO.md](docs/STRUTTURA_PROGETTO.md)** - **NUOVO** - Struttura completa del progetto
- **[01_panoramica.md](docs/01_panoramica.md)** - Introduzione e panoramica del progetto
- **[02_struttura.md](docs/02_struttura.md)** - Struttura dell'applicazione
- **[03_componenti.md](docs/03_componenti.md)** - Componenti principali del sistema

### 🔧 Documentazione Tecnica
- **[04_database.md](docs/04_database.md)** - Struttura e gestione del database
- **[05_api.md](docs/05_api.md)** - Documentazione delle API REST
- **[06_procedure.md](docs/06_procedure.md)** - Procedure operative standard
- **[07_troubleshooting.md](docs/07_troubleshooting.md)** - Risoluzione problemi comuni
- **[08_sicurezza.md](docs/08_sicurezza.md)** - Misure di sicurezza implementate
- **[09_performance.md](docs/09_performance.md)** - Ottimizzazione delle prestazioni
- **[10_metodo_calcolo.md](docs/10_metodo_calcolo.md)** - Metodologie di calcolo sismico
### 🏗️ Modulo Massa Inerziale
- **[14_massa_inerziale.md](docs/14_massa_inerziale.md)** - Documentazione completa del modulo

### 📊 Gestione Progetto
- **[11_miglioramenti.md](docs/11_miglioramenti.md)** - Proposte di miglioramento
- **[12_aggiornamenti.md](docs/12_aggiornamenti.md)** - Registro degli aggiornamenti
- **[13_flussi_lavoro.md](docs/13_flussi_lavoro.md)** - Flussi di lavoro

### 📋 File di Supporto
- **[relazione_asdp.md](docs/relazione_asdp.md)** - Relazione generale del progetto
- **[app_map.md](docs/app_map.md)** - Mappa dell'applicazione

### 📁 Directory Speciali
- **[analisi_normative/](docs/analisi_normative/)** - Analisi delle normative tecniche NTC 2018
- **[templates/](docs/templates/)** - Template per documentazione

## 🚀 Funzionalità Principali

### Sistema a Tre Livelli LLM
Il progetto implementa un sistema innovativo di calcolo massa inerziale con fallback automatico:

1. **🥇 Google Gemma3** (Primario) - Modello compatto e veloce
2. **🥈 Deepseek AI** (Fallback) - Analisi ingegneristica avanzata
3. **🥉 Calcolo Locale** (Garantito) - Formule NTC 2018 standard

**Affidabilità**: 99.9% garantita con fallback automatico trasparente.

### Calcolo Parametri Sismici Dinamici
- Calcolo TR (periodo di ritorno) basato su vita nominale e classe d'uso
- Interpolazione automatica da griglia sismica nazionale
- Coefficienti di amplificazione SS, CC, ST dinamici
- Integrazione completa con database sismico

### Interfaccia Utente Avanzata
- Modal responsive con animazioni fluide
- Caricamento professionale con progress bar
- Gestione errori robusta con retry automatico
- Risultati eleganti con visualizzazione ottimizzata

## 📈 Versioni Recenti

### v2.3.2 (06/06/2025) - Fix Sistema Log e Diagrammi Mermaid
- ✅ **FIX PULIZIA LOG**: Risolto errore ZipArchive con controllo compatibilità automatico
- ✅ **FIX JSON AJAX**: Risolto output HTML mescolato con JSON nelle risposte server
- ✅ **FIX DIAGRAMMI MERMAID**: Risolto errore JavaScript con configurazione migliorata
- ✅ **BACKUP AUTOMATICI**: Creazione backup log prima della pulizia
- ✅ **COMPATIBILITÀ ESTESA**: Sistema funziona con qualsiasi configurazione PHP
- ✅ **DOCUMENTAZIONE**: Aggiornato troubleshooting con nuovi fix

### v2.3.0 (05/06/2025) - Pulizia Completa Workspace
- ✅ **PULIZIA WORKSPACE**: Eliminati file obsoleti, duplicati e di test
- ✅ **STRUTTURA OTTIMIZZATA**: Riorganizzata architettura progetto
- ✅ **DOCUMENTAZIONE AGGIORNATA**: Creato STRUTTURA_PROGETTO.md completo
- ✅ **PERFORMANCE**: Rimossi file non necessari per migliori prestazioni
- ✅ **MANUTENIBILITÀ**: Struttura più pulita e navigabile

### v2.2.0 (05/06/2025) - Sistema a Tre Livelli LLM
- ✅ Implementato sistema fallback Deepseek → Gemma3 → Locale
- ✅ Corretti bug parametri sismici dinamici
- ✅ Organizzata documentazione in cartella docs
- ✅ Affidabilità 99.9% garantita

### v2.1.0 (04/06/2025) - Correzioni Critiche
- ✅ Risolto problema raddoppio icone
- ✅ Ottimizzate performance rendering
- ✅ Implementata pulizia HTML dinamica

## 🔍 Come Navigare la Documentazione

1. **Inizia da**: [00_indice.md](docs/00_indice.md) per una panoramica completa
2. **Per sviluppatori**: Consulta [02_struttura.md](docs/02_struttura.md) e [03_componenti.md](docs/03_componenti.md)
3. **Per amministratori**: Vedi [04_database.md](docs/04_database.md) e [08_sicurezza.md](docs/08_sicurezza.md)
4. **Per troubleshooting**: Consulta [07_troubleshooting.md](docs/07_troubleshooting.md)
5. **Per aggiornamenti**: Monitora [12_aggiornamenti.md](docs/12_aggiornamenti.md)

## 📞 Supporto

Per domande tecniche o problemi:
1. Consulta prima [07_troubleshooting.md](docs/07_troubleshooting.md)
2. Verifica [12_aggiornamenti.md](docs/12_aggiornamenti.md) per problemi noti
3. Controlla [STRUTTURA_PROGETTO.md](docs/STRUTTURA_PROGETTO.md) per architettura

## 📝 Contribuire alla Documentazione

- Registra modifiche in [12_aggiornamenti.md](docs/12_aggiornamenti.md)
- Aggiorna [00_indice.md](docs/00_indice.md) per nuovi documenti
- Usa template in [templates/](docs/templates/) per consistenza
- Mantieni aggiornato [STRUTTURA_PROGETTO.md](docs/STRUTTURA_PROGETTO.md) per modifiche architetturali

---

**Ultimo aggiornamento**: 14/06/2025
**Versione documentazione**: 2.4.2
**Stato progetto**: ✅ Documentazione consolidata - Tutti gli aggiornamenti unificati in 11_miglioramenti.md
