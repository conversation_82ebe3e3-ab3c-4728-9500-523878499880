// js/auth.js

class Auth {
    static showError(message) {
        console.error('Error:', message);
        const errorDiv = document.getElementById('error-message');
        if (errorDiv) {
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
            
            // Nascondi il messaggio dopo 5 secondi
            setTimeout(() => {
                errorDiv.style.display = 'none';
            }, 5000);
        }
    }

    static showSuccess(message) {
        console.log('Success:', message);
        const successDiv = document.getElementById('success-message');
        if (successDiv) {
            successDiv.textContent = message;
            successDiv.style.display = 'block';
            
            // Nascondi il messaggio dopo 5 secondi
            setTimeout(() => {
                successDiv.style.display = 'none';
            }, 5000);
        }
    }

    static toggleLoadingState(form, isLoading) {
        if (form) {
            const submitBtn = form.querySelector('button[type="submit"]');
            const spinner = form.querySelector('.spinner');
            
            if (submitBtn) {
                submitBtn.disabled = isLoading;
            }
            
            if (spinner) {
                spinner.style.display = isLoading ? 'block' : 'none';
            }
        }
    }

    static async login(username, password) {
        const loginForm = document.getElementById('login-form');
        console.log('Inizializzazione processo di login...');
        
        try {
            this.toggleLoadingState(loginForm, true);
            console.log('Tentativo di login per:', username);
            
            console.log('Invio richiesta al server...');
            const response = await fetch('auth/handlers/login_handler.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    username: username.trim(),
                    password: password
                })
            });

            console.log('Risposta ricevuta dal server:', {
                status: response.status,
                statusText: response.statusText,
                headers: Object.fromEntries(response.headers.entries())
            });
            
            const responseText = await response.text();
            console.log('Risposta testo:', responseText);

            let data;
            try {
                data = JSON.parse(responseText);
                console.log('Dati parsati:', data);
            } catch (e) {
                console.error('Errore parsing JSON:', e);
                throw new Error('Errore del server. Riprova più tardi.');
            }

            if (data.success) {
                console.log('Login riuscito, dati sessione:', data.session);
                console.log('Reindirizzamento a:', data.redirect);
                
                this.showSuccess('Login effettuato con successo. Reindirizzamento...');
                
                // Aggiungi un breve ritardo per permettere all'utente di vedere il messaggio di successo
                setTimeout(() => {
                    console.log('Esecuzione reindirizzamento...');
                    window.location.href = data.redirect;
                }, 1000);
            } else {
                console.error('Login fallito:', data.message);
                throw new Error(data.message || 'Errore durante il login');
            }
        } catch (error) {
            console.error('Errore completo:', error);
            this.showError(error.message);
        } finally {
            this.toggleLoadingState(loginForm, false);
        }
    }

    static async register(formData) {
        const registerForm = document.getElementById('register-form');
        console.log('Inizializzazione processo di registrazione...');
        
        try {
            this.toggleLoadingState(registerForm, true);
            console.log('Tentativo di registrazione:', formData);
            
            console.log('Invio richiesta al server...');
            const response = await fetch('register_handler.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(formData)
            });

            console.log('Risposta ricevuta dal server:', {
                status: response.status,
                statusText: response.statusText,
                headers: Object.fromEntries(response.headers.entries())
            });
            
            const responseText = await response.text();
            console.log('Risposta testo:', responseText);

            let data;
            try {
                data = JSON.parse(responseText);
                console.log('Dati parsati:', data);
            } catch (e) {
                console.error('Errore parsing JSON:', e);
                throw new Error('Risposta non valida dal server');
            }

            if (data.success) {
                console.log('Registrazione riuscita');
                this.showSuccess(data.message || 'Registrazione completata con successo!');
                // Reset form
                if (registerForm) {
                    registerForm.reset();
                }
                // Reindirizza dopo 2 secondi
                setTimeout(() => {
                    console.log('Reindirizzamento dopo registrazione...');
                    window.location.href = 'index.php';
                }, 2000);
            } else {
                console.error('Registrazione fallita:', data.message);
                throw new Error(data.message || 'Errore durante la registrazione');
            }
        } catch (error) {
            console.error('Errore completo:', error);
            this.showError(error.message);
        } finally {
            this.toggleLoadingState(registerForm, false);
        }
    }

    static async logout() {
        console.log('Inizializzazione processo di logout...');
        try {
            const response = await fetch('auth/do_logout.php');
            const data = await response.json();
            
            if (data.success) {
                console.log('Logout riuscito, reindirizzamento...');
                window.location.href = 'index.php';
            } else {
                console.error('Logout fallito:', data.message);
                throw new Error(data.message || 'Errore durante il logout');
            }
        } catch (error) {
            console.error('Errore durante il logout:', error);
            this.showError(error.message);
        }
    }

    static validatePassword(password) {
        // Almeno 8 caratteri
        if (password.length < 8) {
            return 'La password deve contenere almeno 8 caratteri';
        }
        
        // Almeno una lettera maiuscola
        if (!/[A-Z]/.test(password)) {
            return 'La password deve contenere almeno una lettera maiuscola';
        }
        
        // Almeno una lettera minuscola
        if (!/[a-z]/.test(password)) {
            return 'La password deve contenere almeno una lettera minuscola';
        }
        
        // Almeno un numero
        if (!/\d/.test(password)) {
            return 'La password deve contenere almeno un numero';
        }
        
        // Almeno un carattere speciale
        if (!/[!@#$%^&*]/.test(password)) {
            return 'La password deve contenere almeno un carattere speciale (!@#$%^&*)';
        }
        
        return null; // Password valida
    }

    static validateEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }
}

// Event Listeners
document.addEventListener('DOMContentLoaded', () => {
    console.log('DOM caricato, inizializzazione event listeners...');
    
    // Gestione form di login
    const loginForm = document.getElementById('login-form');
    if (loginForm) {
        console.log('Form di login trovato, configurazione event listener...');
        loginForm.addEventListener('submit', async (e) => {
            e.preventDefault();
            console.log('Form di login inviato');
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            if (!username || !password) {
                console.error('Campi mancanti nel form di login');
                Auth.showError('Inserisci username e password');
                return;
            }
            
            await Auth.login(username, password);
        });

        // Toggle password visibility
        const togglePassword = document.querySelector('.toggle-password');
        const passwordInput = document.getElementById('password');
        
        if (togglePassword && passwordInput) {
            console.log('Configurazione toggle password...');
            togglePassword.addEventListener('click', () => {
                const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
                passwordInput.setAttribute('type', type);
                
                // Aggiorna l'icona
                const icon = togglePassword.querySelector('i');
                if (icon) {
                    icon.classList.toggle('fa-eye');
                    icon.classList.toggle('fa-eye-slash');
                }
            });
        }
    }

    // Gestione form di registrazione
    const registerForm = document.getElementById('register-form');
    if (registerForm) {
        console.log('Form di registrazione trovato, configurazione event listener...');
        registerForm.addEventListener('submit', async (e) => {
            e.preventDefault();
            console.log('Form di registrazione inviato');
            
            const formData = {
                username: document.getElementById('username').value,
                password: document.getElementById('password').value,
                confirm_password: document.getElementById('confirm_password').value,
                email: document.getElementById('email').value,
                nome: document.getElementById('nome').value,
                cognome: document.getElementById('cognome').value
            };
            
            // Validazione
            if (!formData.username || !formData.password || !formData.email || !formData.nome || !formData.cognome) {
                console.error('Campi mancanti nel form di registrazione');
                Auth.showError('Tutti i campi sono obbligatori');
                return;
            }
            
            if (formData.password !== formData.confirm_password) {
                console.error('Le password non coincidono');
                Auth.showError('Le password non coincidono');
                return;
            }
            
            const passwordError = Auth.validatePassword(formData.password);
            if (passwordError) {
                console.error('Errore validazione password:', passwordError);
                Auth.showError(passwordError);
                return;
            }
            
            if (!Auth.validateEmail(formData.email)) {
                console.error('Email non valida:', formData.email);
                Auth.showError('Email non valida');
                return;
            }
            
            await Auth.register(formData);
        });
    }

    // Gestione logout
    const logoutBtn = document.querySelector('.logout-btn');
    if (logoutBtn) {
        console.log('Pulsante logout trovato, configurazione event listener...');
        logoutBtn.addEventListener('click', async (e) => {
            e.preventDefault();
            console.log('Click su pulsante logout');
            await Auth.logout();
        });
    }
});
