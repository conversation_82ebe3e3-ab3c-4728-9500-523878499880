/**
 * Classe per il calcolo dei parametri sismici secondo NTC 2018
 */
class SeismicCalculator {
    constructor() {
        // Costanti per gli stati limite
        this.STATES = {
            SLO: { PVR: 0.81, description: 'Operatività' },
            SLD: { PVR: 0.63, description: '<PERSON><PERSON>' },
            SLV: { PVR: 0.10, description: 'Salvaguardia Vita' },
            SLC: { PVR: 0.05, description: 'Collasso' }
        };

        // Coefficienti per le categorie di sottosuolo
        this.SOIL_CATEGORIES = {
            'A': { SS: 1.00, CC: 1.00 },
            'B': { 
                SS: { min: 1.00, max: 1.20, formula: (F0, ag) => Math.min(Math.max(1.00, 1.40 - 0.40 * F0 * ag/9.81), 1.20) },
                CC: { formula: (TC) => 1.10 * Math.pow(TC, -0.20) }
            },
            'C': {
                SS: { min: 1.00, max: 1.50, formula: (F0, ag) => Math.min(Math.max(1.00, 1.70 - 0.60 * F0 * ag/9.81), 1.50) },
                CC: { formula: (TC) => 1.05 * Math.pow(TC, -0.33) }
            },
            'D': {
                SS: { min: 0.90, max: 1.80, formula: (F0, ag) => Math.min(Math.max(0.90, 2.40 - 1.50 * F0 * ag/9.81), 1.80) },
                CC: { formula: (TC) => 1.25 * Math.pow(TC, -0.50) }
            },
            'E': {
                SS: { min: 1.00, max: 1.60, formula: (F0, ag) => Math.min(Math.max(1.00, 2.00 - 1.10 * F0 * ag/9.81), 1.60) },
                CC: { formula: (TC) => 1.15 * Math.pow(TC, -0.40) }
            }
        };

        // Coefficienti topografici
        this.TOPOGRAPHIC_CATEGORIES = {
            'T1': 1.0,
            'T2': 1.2,
            'T3': 1.2,
            'T4': 1.4
        };
    }

    /**
     * Interpola un valore usando i 4 punti più vicini della griglia
     * @param {number} x - Coordinata x normalizzata (0-1)
     * @param {number} y - Coordinata y normalizzata (0-1)
     * @param {Object} points - Oggetto con i 4 punti della griglia {p1, p2, p3, p4}
     * @returns {number} Valore interpolato
     */
    interpolate(x, y, points) {
        const { p1, p2, p3, p4 } = points;
        return p1 * (1-x) * (1-y) + 
               p2 * x * (1-y) + 
               p3 * (1-x) * y + 
               p4 * x * y;
    }

    /**
     * Calcola il periodo di ritorno TR
     * @param {number} VN - Vita nominale
     * @param {number} CU - Coefficiente d'uso
     * @param {number} PVR - Probabilità di superamento
     * @returns {number} Periodo di ritorno TR
     */
    calculateTR(VN, CU, PVR) {
        const VR = VN * CU;
        return -VR / Math.log(1 - PVR);
    }

    /**
     * Calcola il coefficiente di smorzamento η
     * @param {number} xi - Coefficiente di smorzamento viscoso (%)
     * @returns {number} Fattore η
     */
    calculateDampingFactor(xi) {
        return Math.max(Math.sqrt(10 / (5 + xi)), 0.55);
    }

    /**
     * Calcola i coefficienti di amplificazione stratigrafica
     * @param {string} soilCategory - Categoria di sottosuolo (A-E)
     * @param {number} F0 - Fattore di amplificazione spettrale
     * @param {number} ag - Accelerazione orizzontale massima
     * @param {number} TC - Periodo TC*
     * @returns {Object} Coefficienti SS e CC
     */
    calculateSoilCoefficients(soilCategory, F0, ag, TC) {
        const category = this.SOIL_CATEGORIES[soilCategory];
        if (!category) throw new Error('Categoria di sottosuolo non valida');

        const SS = typeof category.SS === 'number' ? 
            category.SS : 
            category.SS.formula(F0, ag);

        const CC = typeof category.CC === 'number' ? 
            category.CC : 
            category.CC.formula(TC);

        return { SS, CC };
    }

    /**
     * Calcola i periodi caratteristici
     * @param {number} TC_star - Periodo TC* da interpolazione
     * @param {number} CC - Coefficiente CC
     * @param {number} ag - Accelerazione orizzontale massima
     * @returns {Object} Periodi TB, TC e TD
     */
    calculatePeriods(TC_star, CC, ag) {
        const TC = CC * TC_star;
        const TB = TC / 3;
        const TD = 4.0 * (ag/9.81) + 1.6;
        return { TB, TC, TD };
    }

    /**
     * Calcola lo spettro di risposta elastico
     * @param {Object} params - Parametri per il calcolo
     * @param {number} params.ag - Accelerazione orizzontale massima
     * @param {number} params.F0 - Fattore di amplificazione spettrale
     * @param {number} params.TC - Periodo TC
     * @param {number} params.TB - Periodo TB
     * @param {number} params.TD - Periodo TD
     * @param {number} params.S - Coefficiente che tiene conto della categoria di sottosuolo
     * @param {number} params.eta - Fattore di smorzamento
     * @param {number} T - Periodo di vibrazione
     * @returns {number} Accelerazione spettrale Se(T)
     */
    calculateElasticSpectrum(params, T) {
        const { ag, F0, TC, TB, TD, S, eta } = params;

        if (T < 0) throw new Error('Il periodo T deve essere positivo');

        // Calcolo dello spettro secondo le 4 espressioni della normativa
        if (T >= 0 && T < TB) {
            return ag * S * eta * F0 * (T/TB + (1/(eta*F0)) * (1 - T/TB));
        }
        else if (T >= TB && T < TC) {
            return ag * S * eta * F0;
        }
        else if (T >= TC && T < TD) {
            return ag * S * eta * F0 * (TC/T);
        }
        else { // T >= TD
            return ag * S * eta * F0 * (TC*TD/(T*T));
        }
    }

    /**
     * Calcola lo spettro di progetto
     * @param {Object} params - Parametri per il calcolo
     * @param {number} params.q - Fattore di struttura
     * @param {number} T - Periodo di vibrazione
     * @returns {number} Accelerazione spettrale Sd(T)
     */
    calculateDesignSpectrum(params, T) {
        const { q } = params;
        if (q <= 0) throw new Error('Il fattore di struttura q deve essere positivo');

        // Sostituiamo η con 1/q nello spettro elastico
        const elasticParams = { ...params, eta: 1/q };
        return this.calculateElasticSpectrum(elasticParams, T);
    }

    /**
     * Genera i punti dello spettro per il plotting
     * @param {Object} params - Parametri per il calcolo
     * @param {number} numPoints - Numero di punti da generare
     * @param {boolean} isDesignSpectrum - Se true calcola lo spettro di progetto
     * @returns {Array} Array di punti {T, Se}
     */
    generateSpectrumPoints(params, numPoints = 1000, isDesignSpectrum = false) {
        const { TB, TC, TD } = params;
        const Tmax = 4.0; // Periodo massimo per il grafico
        const points = [];

        // Genera punti con distribuzione non uniforme per catturare meglio i cambi di pendenza
        const regions = [
            { start: 0, end: TB, points: numPoints/4 },
            { start: TB, end: TC, points: numPoints/4 },
            { start: TC, end: TD, points: numPoints/4 },
            { start: TD, end: Tmax, points: numPoints/4 }
        ];

        regions.forEach(region => {
            const dT = (region.end - region.start) / region.points;
            for (let i = 0; i <= region.points; i++) {
                const T = region.start + i * dT;
                const Se = isDesignSpectrum ? 
                    this.calculateDesignSpectrum(params, T) :
                    this.calculateElasticSpectrum(params, T);
                points.push({ T, Se });
            }
        });

        return points;
    }
}

// Esporta la classe per l'uso in altri moduli
export default SeismicCalculator; 