<?php
if (!defined('ACCESS_ALLOWED')) {
    define('ACCESS_ALLOWED', true);
}
?>
<footer class="admin-footer">
    <div class="footer-content">
        <div class="footer-info">
            <span class="copyright">&copy; <?php echo date('Y'); ?> ASDP - Advanced Seismic Dissipator Project</span>
            <span class="version">Admin Panel v2.0</span>
        </div>
        <div class="footer-links">
            <a href="#" class="footer-link" onclick="showPrivacyPolicy(event)">Privacy Policy</a>
            <span class="separator">|</span>
            <span class="footer-link disabled">Termini di Servizio</span>
            <span class="separator">|</span>
            <span class="footer-link disabled">Supporto</span>
        </div>
    </div>
</footer>

<!-- Modal Privacy Policy -->
<div id="privacyModal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h2>Privacy Policy</h2>
            <span class="close-modal" onclick="closePrivacyModal()">&times;</span>
        </div>
        <div class="modal-body">
            <!-- Contenuto Privacy Policy -->
        </div>
    </div>
</div>

<style>
.admin-footer {
    background: #1E1E1E;
    border-top: 1px solid #333;
    padding: 0.5rem;
    margin-top: auto;
    width: 100%;
    position: fixed;
    bottom: 0;
    left: 0;
    z-index: 1000;
}

.footer-content {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.footer-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #BDBDBD;
    font-size: 0.8rem;
}

.version {
    color: #FF7043;
    padding-left: 0.5rem;
    border-left: 1px solid #333;
}

.footer-links {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.footer-link {
    color: #BDBDBD;
    text-decoration: none;
    font-size: 0.8rem;
    transition: color 0.2s ease;
}

.footer-link:hover {
    color: #FF7043;
}

.separator {
    color: #333;
}

@media (max-width: 768px) {
    .admin-footer {
        position: relative;
        padding: 0.4rem;
    }

    .footer-content {
        flex-direction: column;
        text-align: center;
        gap: 0.4rem;
    }

    .footer-info {
        flex-direction: column;
        gap: 0.3rem;
    }

    .version {
        border-left: none;
        padding-left: 0;
    }

    .footer-links {
        flex-wrap: wrap;
        justify-content: center;
    }
}

/* Aggiustamenti per il contenuto principale */
.content {
    padding-bottom: 50px;
}

@media (max-width: 768px) {
    .content {
        padding-bottom: 70px;
    }
}

.footer-link.disabled {
    color: #666;
    cursor: not-allowed;
    text-decoration: none;
}

.footer-link.disabled:hover {
    color: #666;
}

/* Stili Modal */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(18, 18, 18, 0.95); /* Sfondo più scuro e opaco */
    z-index: 2000;
}

.modal-content {
    position: relative;
    background-color: #1E1E1E;
    margin: 2% auto;
    padding: 0;
    width: 80%;
    max-width: 1000px;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.6);
    border: 1px solid #333;
}

.modal-header {
    padding: 1.5rem;
    background: #1a1a1a;
    border-bottom: 2px solid #FF7043;
    border-radius: 8px 8px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h2 {
    color: #FF7043;
    margin: 0;
    font-size: 1.5rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.modal-header h2:before {
    content: '\f05a'; /* Icona info */
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
}

.close-modal {
    color: #BDBDBD;
    font-size: 1.8rem;
    font-weight: normal;
    cursor: pointer;
    transition: all 0.2s ease;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

.close-modal:hover {
    color: #FF7043;
    background-color: rgba(255, 112, 67, 0.1);
    transform: rotate(90deg);
}

.modal-body {
    padding: 2rem;
    max-height: 75vh;
    overflow-y: auto;
    color: #FFFFFF;
    line-height: 1.6;
    font-size: 1rem;
}

/* Stile della scrollbar del modal */
.modal-body::-webkit-scrollbar {
    width: 8px;
}

.modal-body::-webkit-scrollbar-track {
    background: #1a1a1a;
    border-radius: 4px;
}

.modal-body::-webkit-scrollbar-thumb {
    background: #FF7043;
    border-radius: 4px;
}

.modal-body::-webkit-scrollbar-thumb:hover {
    background: #F4511E;
}

/* Stili per Firefox */
.modal-body {
    scrollbar-width: thin;
    scrollbar-color: #FF7043 #1a1a1a;
}

/* Stili per il contenuto della privacy policy */
.modal-body h3 {
    color: #FF7043;
    margin: 1.5rem 0 1rem;
    font-size: 1.2rem;
    font-weight: 500;
}

.modal-body p {
    margin-bottom: 1rem;
    color: #BDBDBD;
}

.modal-body ul {
    margin: 1rem 0;
    padding-left: 1.5rem;
    color: #BDBDBD;
}

.modal-body li {
    margin-bottom: 0.5rem;
}

@keyframes modalSlideIn {
    from {
        transform: translateY(-50px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@media (max-width: 768px) {
    .modal-content {
        width: 95%;
        margin: 5% auto;
    }

    .modal-header {
        padding: 1rem;
    }

    .modal-body {
        padding: 1.5rem;
        max-height: 70vh;
    }

    .modal-header h2 {
        font-size: 1.3rem;
    }
}
</style>

<script>
function showPrivacyPolicy(event) {
    event.preventDefault();
    document.getElementById('privacyModal').style.display = 'block';
    // Carica il contenuto della privacy policy via AJAX
    fetch('../privacy.php')
        .then(response => response.text())
        .then(data => {
            // Estrai solo il contenuto principale
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = data;
            const content = tempDiv.querySelector('.privacy-content') || tempDiv;
            document.querySelector('.modal-body').innerHTML = content.innerHTML;
        })
        .catch(error => {
            document.querySelector('.modal-body').innerHTML = 'Errore nel caricamento della Privacy Policy.';
            console.error('Errore:', error);
        });
}

function closePrivacyModal() {
    document.getElementById('privacyModal').style.display = 'none';
}

// Chiudi il modal se si clicca fuori
window.onclick = function(event) {
    const modal = document.getElementById('privacyModal');
    if (event.target == modal) {
        modal.style.display = 'none';
    }
}

// Chiudi il modal con il tasto ESC
document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape') {
        document.getElementById('privacyModal').style.display = 'none';
    }
});
</script> 