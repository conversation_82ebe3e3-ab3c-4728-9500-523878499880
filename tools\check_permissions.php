<?php
// Script per verificare e correggere i permessi delle directory

function checkAndFixDirectory($path, $permissions = 0777) {
    echo "Controllo directory: $path\n";
    
    if (!file_exists($path)) {
        echo "- Directory non esistente, la creo...\n";
        if (mkdir($path, $permissions, true)) {
            echo "- Directory creata con successo\n";
        } else {
            echo "- ERRORE: Impossibile creare la directory\n";
            return false;
        }
    }
    
    $currentPerms = substr(sprintf('%o', fileperms($path)), -4);
    echo "- Permessi attuali: $currentPerms\n";
    
    if (!is_writable($path)) {
        echo "- Directory non scrivibile, modifico i permessi...\n";
        if (chmod($path, $permissions)) {
            echo "- Permessi modificati con successo\n";
        } else {
            echo "- ERRORE: Impossibile modificare i permessi\n";
            return false;
        }
    } else {
        echo "- Directory scrivibile: OK\n";
    }
    
    return true;
}

// Directory da controllare
$directories = [
    __DIR__ . '/../cache',
    __DIR__ . '/../logs',
    __DIR__ . '/../includes/cache'
];

echo "=== Verifica Permessi Directory ===\n\n";

foreach ($directories as $dir) {
    echo "\nVerifica: " . basename($dir) . "\n";
    echo str_repeat('-', 30) . "\n";
    if (checkAndFixDirectory($dir)) {
        echo "✓ Directory verificata e corretta\n";
    } else {
        echo "✗ Problemi con la directory\n";
    }
}

echo "\n=== Verifica Completata ===\n"; 