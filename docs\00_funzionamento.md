# Funzionamento dell'Applicazione ASDP

## Panoramica
ASDP (Advanced Seismic Dissipator Project) è un'applicazione web per il calcolo e l'analisi dei parametri sismici. L'applicazione permette di:
- Ricercare località tramite mappa interattiva
- Calcolare parametri sismici per una data posizione
- Generare report dettagliati
- Gestire progetti e valutazioni

## Accesso al Sistema

### Login
1. Accedere alla pagina di login (`login.php`)
2. Inserire credenziali (username/email e password)
3. Il sistema verifica le credenziali e reindirizza alla dashboard

### Registrazione
1. <PERSON>la pagina di login, cliccare su "Registrati"
2. Compilare il form con:
   - Username
   - Email
   - Password
   - Conferma password
3. Il sistema verifica i dati e crea il nuovo account

## Interfaccia Principale

### Dashboard
- Mappa interattiva per la selezione del punto
- Pannello informazioni località
- Barra laterale con menu di navigazione
- Area calcoli parametri sismici

### Menu Laterale
1. **Dashboard** (in sviluppo)
   - Panoramica generale
   - Statistiche utente
   - Ultimi calcoli

2. **Nuova Valutazione** (in sviluppo)
   - Form per nuova valutazione
   - Selezione parametri
   - Calcolo risultati

3. **Storico Valutazioni** (in sviluppo)
   - Lista valutazioni precedenti
   - Filtri e ricerca
   - Dettagli calcoli

4. **Report**
   - Generazione report HTML
   - Stampa risultati
   - Esportazione dati

5. **Progetti** (in sviluppo)
   - Gestione progetti
   - Organizzazione valutazioni
   - Condivisione dati

6. **Impostazioni**
   - Preferenze utente
   - Configurazione account
   - Personalizzazione UI

## Funzionalità Core

### 1. Ricerca Località
- Ricerca per indirizzo/coordinate
- Selezione punto sulla mappa
- Visualizzazione dati catastali
- Info amministrative (comune, provincia, regione)

### 2. Calcolo Parametri Sismici
- Input parametri di calcolo:
  - Vita nominale
  - Classe d'uso
  - Categoria sottosuolo
  - Categoria topografica
  - Smorzamento
  - Fattore di struttura
- Calcolo automatico parametri:
  - ag [g]
  - F0
  - Tc* [s]
  - TR [anni]

### 3. Generazione Report
- Creazione report HTML
- Inclusione dati località
- Parametri di calcolo utilizzati
- Risultati ottenuti
- Opzioni di stampa

## Gestione Dati

### Database
- Tabella utenti
- Storico calcoli
- Configurazioni
- Log sistema

### Backup
- Backup automatico database
- Backup file configurazione
- Gestione versioni
- Restore dati

## Sicurezza

### Autenticazione
- Login sicuro
- Gestione sessioni
- Password hashing
- Protezione CSRF

### Validazione
- Input sanitization
- Controllo permessi
- Rate limiting
- Logging accessi

## Note Tecniche

### Requisiti Sistema
- XAMPP v3.3.0+
- PHP 8.1+
- MySQL 8.0+
- Browser moderno con JavaScript

### Performance
- Caching risultati
- Ottimizzazione query
- Compressione assets
- Lazy loading

### Manutenzione
- Log errori in `/logs`
- Backup periodici
- Aggiornamenti sicurezza
- Monitoraggio sistema

## Supporto

### Documentazione
- Guida utente
- Documentazione tecnica
- API reference
- FAQ

### Assistenza
- Sistema ticket
- Email support
- Knowledge base
- Tutorial video 