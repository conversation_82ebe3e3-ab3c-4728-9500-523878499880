<?php
/**
 * config.php - Configurazione per il modulo massa inerziale
 * Path: /inertial_mass/includes/config.php
 */

// Carica le variabili d'ambiente dal file .env principale
$envPath = dirname(dirname(dirname(__FILE__))) . '/.env';
if (file_exists($envPath)) {
    $lines = file($envPath, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) continue;
        list($name, $value) = explode('=', $line, 2);
        $name = trim($name);
        $value = trim($value);
        if (!empty($name)) {
            putenv("$name=$value");
            $_ENV[$name] = $value;
        }
    }
}

// Configurazione LLM
return [
    // Provider LLM primario - MODIFICATO: Gemma3 come primario
    'primary_provider'   => 'gemini',
    'fallback_provider'  => 'deepseek',
    
    // Deepseek API
    'deepseek_api_key'   => getenv('DEEPSEEK_API_KEY') ?: '***********************************',
    'deepseek_model'     => getenv('DEEPSEEK_MODEL') ?: 'deepseek-chat',
    'deepseek_api_url'   => 'https://api.deepseek.com/v1/chat/completions',
    
    // Gemini API (usando Gemma3)
    'gemini_api_key'     => getenv('GEMINI_API_KEY') ?: '',
    'gemini_model'       => getenv('GEMINI_MODEL') ?: 'gemma-2-2b-it',
    'gemini_api_url'     => 'https://generativelanguage.googleapis.com/v1beta/models/',
    
    // Configurazione timeout e retry
    'api_timeout'        => 120, // secondi
    'max_retries'        => 3,
    'retry_delay'        => 2, // secondi
    
    // Configurazione cache
    'enable_cache'       => true,
    'cache_ttl'          => 3600, // 1 ora
    
    // Configurazione sicurezza
    'rate_limit'         => 10, // richieste per minuto
    'require_auth'       => true,
];
