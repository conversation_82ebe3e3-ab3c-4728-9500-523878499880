<?php
namespace App\Services;

class CacheService {
    private $redis;
    
    public function __construct() {
        $this->redis = new \Redis();
        try {
            $this->redis->connect('127.0.0.1', 6379);
        } catch (\Exception $e) {
            error_log("Errore connessione Redis: " . $e->getMessage());
        }
    }
    
    public function get($key) {
        try {
            return $this->redis->get($key);
        } catch (\Exception $e) {
            error_log("Errore Redis get: " . $e->getMessage());
            return null;
        }
    }
    
    public function set($key, $value, $ttl = 3600) {
        try {
            return $this->redis->set($key, $value, $ttl);
        } catch (\Exception $e) {
            error_log("Errore Redis set: " . $e->getMessage());
            return false;
        }
    }
} 