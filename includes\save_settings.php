<?php
session_start();
require_once 'db_config.php';

header('Content-Type: application/json');

if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Non autorizzato']);
    exit();
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Metodo non consentito']);
    exit();
}

try {
    $conn = getConnection();
    
    // Lista delle impostazioni consentite
    $allowedSettings = [
        'defaultMapView',
        'showCatasto',
        'themeSelect',
        'sidebarOpen',
        'coordSystem',
        'coordFormat',
        'exportFormat',
        'includeMap',
        'includeCatasto'
    ];

    // Prepara la query per l'inserimento/aggiornamento
    $stmt = $conn->prepare("
        INSERT INTO user_settings (user_id, setting_key, setting_value)
        VALUES (?, ?, ?)
        ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value)
    ");

    // Processa ogni impostazione
    foreach ($_POST as $key => $value) {
        if (in_array($key, $allowedSettings)) {
            $stmt->bind_param("iss", $_SESSION['user_id'], $key, $value);
            $stmt->execute();
        }
    }
    
    echo json_encode([
        'success' => true,
        'message' => 'Impostazioni salvate con successo'
    ]);

} catch (Exception $e) {
    error_log("Errore nel salvataggio delle impostazioni: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Errore nel salvataggio delle impostazioni'
    ]);
} 