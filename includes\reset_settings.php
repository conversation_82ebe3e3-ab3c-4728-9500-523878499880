<?php
session_start();
require_once 'db_config.php';

header('Content-Type: application/json');

if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Non autorizzato']);
    exit();
}

try {
    $conn = getConnection();
    
    // Elimina tutte le impostazioni dell'utente
    $stmt = $conn->prepare("DELETE FROM user_settings WHERE user_id = ?");
    $stmt->bind_param("i", $_SESSION['user_id']);
    $stmt->execute();
    
    // Imposta i valori predefiniti
    $defaultSettings = [
        'defaultMapView' => 'roadmap',
        'showCatasto' => '0',
        'themeSelect' => 'light',
        'sidebarOpen' => '0',
        'coordSystem' => 'WGS84',
        'coordFormat' => 'decimal',
        'exportFormat' => 'pdf',
        'includeMap' => '1',
        'includeCatasto' => '1'
    ];

    // Inserisce le impostazioni predefinite
    $stmt = $conn->prepare("
        INSERT INTO user_settings (user_id, setting_key, setting_value)
        VALUES (?, ?, ?)
    ");

    foreach ($defaultSettings as $key => $value) {
        $stmt->bind_param("iss", $_SESSION['user_id'], $key, $value);
        $stmt->execute();
    }
    
    echo json_encode([
        'success' => true,
        'message' => 'Impostazioni ripristinate con successo',
        'data' => $defaultSettings
    ]);

} catch (Exception $e) {
    error_log("Errore nel ripristino delle impostazioni: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Errore nel ripristino delle impostazioni'
    ]);
} 