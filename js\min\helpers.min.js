function debounce(func,wait){let timeout;return function executedFunction(...args){const later=()=>{clearTimeout(timeout);func(...args);};clearTimeout(timeout);timeout=setTimeout(later,wait);};}
function formatDate(dateString){if(!dateString)return'N/D';const date=new Date(dateString);if(isNaN(date.getTime()))return dateString;return date.toLocaleDateString('it-IT',{day:'2-digit',month:'2-digit',year:'numeric'});}