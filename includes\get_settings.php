<?php
session_start();
require_once 'db_config.php';

header('Content-Type: application/json');

// Verifica se l'utente è loggato
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode([
        'success' => false,
        'message' => 'Utente non autorizzato'
    ]);
    exit();
}

try {
    $pdo = getConnection();
    
    // Recupera le impostazioni dell'utente
    $stmt = $pdo->prepare("
        SELECT * FROM user_settings 
        WHERE user_id = ?
    ");
    
    $stmt->execute([$_SESSION['user_id']]);
    $settings = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // Se non esistono impostazioni, usa quelle di default
    if (!$settings) {
        $settings = [
            'map_type' => 'satellite',
            'language' => 'it',
            'notifications_enabled' => true,
            'auto_save' => true,
            'theme' => 'dark'
        ];
        
        // Salva le impostazioni di default
        $stmt = $pdo->prepare("
            INSERT INTO user_settings 
            (user_id, map_type, language, notifications_enabled, auto_save, theme) 
            VALUES (?, ?, ?, ?, ?, ?)
        ");
        
        $stmt->execute([
            $_SESSION['user_id'],
            $settings['map_type'],
            $settings['language'],
            $settings['notifications_enabled'],
            $settings['auto_save'],
            $settings['theme']
        ]);
    }
    
    echo json_encode([
        'success' => true,
        'data' => $settings
    ]);
    
} catch (Exception $e) {
    error_log("Errore nel recupero delle impostazioni: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Errore nel recupero delle impostazioni'
    ]);
} 