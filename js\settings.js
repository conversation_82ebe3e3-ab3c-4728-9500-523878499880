document.addEventListener('DOMContentLoaded', function() {
    // Elementi DOM
    const settingsLink = document.querySelector('[data-section="impostazioni"]');
    
    // Crea l'overlay
    const overlay = document.createElement('div');
    overlay.className = 'settings-overlay';
    document.body.appendChild(overlay);

    // Carica il contenuto delle impostazioni
    function loadSettingsContent() {
        console.log('Caricamento impostazioni...'); // Debug
        fetch('settings.php')
            .then(response => {
                if (!response.ok) {
                    throw new Error('Errore nella risposta del server');
                }
                return response.text();
            })
            .then(html => {
                overlay.innerHTML = html;
                overlay.classList.add('active');
                
                // Gestione chiusura
                const closeBtn = overlay.querySelector('#closeSettings');
                if (closeBtn) {
                    closeBtn.addEventListener('click', closeSettings);
                }

                // Inizializza gli event listener per i form
                initializeSettingsHandlers();

                // Carica le impostazioni salvate
                loadSavedSettings();

                // Chiudi con ESC
                document.addEventListener('keydown', handleEscKey);
                
                // Chiudi cliccando fuori dal popup
                overlay.addEventListener('click', handleOverlayClick);
            })
            .catch(error => {
                console.error('Errore nel caricamento delle impostazioni:', error);
                alert('Si è verificato un errore nel caricamento delle impostazioni. Riprova più tardi.');
            });
    }

    // Gestione dei form e controlli
    function initializeSettingsHandlers() {
        const saveBtn = overlay.querySelector('#saveSettings');
        const resetBtn = overlay.querySelector('#resetSettings');
        const allInputs = overlay.querySelectorAll('input, select');

        // Gestione salvataggio impostazioni
        if (saveBtn) {
            saveBtn.addEventListener('click', function() {
                saveSettings();
            });
        }

        // Gestione reset impostazioni
        if (resetBtn) {
            resetBtn.addEventListener('click', function() {
                if (confirm('Sei sicuro di voler ripristinare le impostazioni predefinite?')) {
                    resetSettings();
                }
            });
        }

        // Gestione cambiamenti in tempo reale
        allInputs.forEach(input => {
            input.addEventListener('change', function() {
                applySettingChange(this.id, this.type === 'checkbox' ? this.checked : this.value);
            });
        });
    }

    // Carica le impostazioni salvate
    function loadSavedSettings() {
        fetch('includes/get_settings.php')
            .then(response => response.json())
            .then(settings => {
                if (settings.success && settings.data && typeof settings.data === 'object') {
                    Object.entries(settings.data).forEach(([key, value]) => {
                        const element = document.getElementById(key);
                        if (element) {
                            if (element.type === 'checkbox') {
                                element.checked = value === '1' || value === true;
                            } else {
                                element.value = value;
                            }
                        }
                    });
                } else {
                    console.warn('Nessuna impostazione trovata o formato non valido');
                }
            })
            .catch(error => {
                console.error('Errore nel caricamento delle impostazioni:', error);
                showNotification('Errore nel caricamento delle impostazioni', 'error');
            });
    }

    // Salva le impostazioni
    function saveSettings() {
        const formData = new FormData();
        const allInputs = overlay.querySelectorAll('input, select');
        
        allInputs.forEach(input => {
            const value = input.type === 'checkbox' ? (input.checked ? '1' : '0') : input.value;
            formData.append(input.id, value);
        });

        fetch('includes/save_settings.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('Impostazioni salvate con successo', 'success');
                closeSettings();
            } else {
                throw new Error(data.message || 'Errore nel salvataggio');
            }
        })
        .catch(error => {
            console.error('Errore nel salvataggio:', error);
            showNotification('Errore nel salvataggio delle impostazioni', 'error');
        });
    }

    // Reset impostazioni
    function resetSettings() {
        fetch('includes/reset_settings.php')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    loadSavedSettings();
                    showNotification('Impostazioni ripristinate con successo', 'success');
                } else {
                    throw new Error(data.message || 'Errore nel ripristino');
                }
            })
            .catch(error => {
                console.error('Errore nel ripristino:', error);
                showNotification('Errore nel ripristino delle impostazioni', 'error');
            });
    }

    // Applica le modifiche in tempo reale
    function applySettingChange(settingId, value) {
        switch(settingId) {
            case 'sidebarOpen':
                // Gestito dal codice della sidebar
                break;
            case 'defaultMapView':
                // Gestito dal codice della mappa
                break;
            // Aggiungi altri casi secondo necessità
        }
    }

    // Funzione per mostrare le notifiche
    function showNotification(message, type) {
        // Implementa la tua logica per mostrare le notifiche
        console.log(`${type}: ${message}`);
    }

    // Funzione per chiudere il popup
    function closeSettings() {
        if (overlay) {
            overlay.classList.remove('active');
            document.removeEventListener('keydown', handleEscKey);
            overlay.removeEventListener('click', handleOverlayClick);
        }
    }

    // Gestione tasto ESC
    function handleEscKey(e) {
        if (e.key === 'Escape') {
            closeSettings();
        }
    }

    // Gestione click fuori dal popup
    function handleOverlayClick(e) {
        if (e.target === overlay) {
            closeSettings();
        }
    }

    // Event listener per il link delle impostazioni
    if (settingsLink) {
        settingsLink.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            loadSettingsContent();
        });
    } else {
        console.error('Link impostazioni non trovato'); // Debug
    }
}); 